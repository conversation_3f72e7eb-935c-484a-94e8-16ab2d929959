{"version": 3, "file": "emoji-picker-react.esm.js", "sources": ["../src/DomUtils/classNames.ts", "../src/Stylesheet/stylesheet.tsx", "../src/config/compareConfig.ts", "../src/components/Reactions/DEFAULT_REACTIONS.ts", "../src/types/exposedTypes.ts", "../src/config/categoryConfig.ts", "../src/config/cdnUrls.ts", "../src/data/skinToneVariations.ts", "../src/dataUtils/DataTypes.ts", "../src/dataUtils/alphaNumericEmojiIndex.ts", "../src/dataUtils/emojiSelectors.ts", "../src/config/config.ts", "../src/components/context/PickerConfigContext.tsx", "../src/hooks/useDebouncedState.ts", "../src/hooks/useHideEmojisByUniocode.ts", "../src/hooks/useDisallowedEmojis.ts", "../src/hooks/useInitialLoad.ts", "../src/components/context/PickerContext.tsx", "../src/config/mutableConfig.ts", "../src/config/useConfig.ts", "../src/hooks/useIsSearchMode.ts", "../src/DomUtils/focusElement.ts", "../src/DomUtils/getActiveElement.ts", "../src/components/context/ElementRefContext.tsx", "../src/DomUtils/scrollTo.ts", "../src/DomUtils/keyboardNavigation.ts", "../src/hooks/useCloseAllOpenToggles.ts", "../src/hooks/useDisallowMouseMove.ts", "../src/hooks/useFocus.ts", "../src/hooks/useFilter.ts", "../src/hooks/useSetVariationPicker.ts", "../src/hooks/useShouldShowSkinTonePicker.ts", "../src/hooks/useKeyboardNavigation.ts", "../src/hooks/preloadEmoji.ts", "../src/hooks/useOnFocus.ts", "../src/components/main/PickerMain.tsx", "../src/DomUtils/elementPositionInRow.ts", "../src/DomUtils/selectors.ts", "../src/dataUtils/parseNativeEmoji.ts", "../src/dataUtils/suggested.ts", "../src/typeRefinements/typeRefinements.ts", "../src/hooks/useMouseDownHandlers.ts", "../src/components/atoms/Button.tsx", "../src/components/emoji/ClickableEmojiButton.tsx", "../src/components/emoji/emojiStyles.ts", "../src/components/emoji/EmojiImg.tsx", "../src/components/emoji/NativeEmoji.tsx", "../src/components/emoji/ViewOnlyEmoji.tsx", "../src/components/emoji/Emoji.tsx", "../src/components/Reactions/BtnPlus.tsx", "../src/components/Reactions/Reactions.tsx", "../src/hooks/useOnScroll.ts", "../src/hooks/useIsEmojiHidden.ts", "../src/components/body/EmojiCategory.tsx", "../src/hooks/useIsEverMounted.ts", "../src/components/body/Suggested.tsx", "../src/components/body/EmojiList.tsx", "../src/components/body/EmojiVariationPicker.tsx", "../src/components/body/Body.tsx", "../src/DomUtils/detectEmojyPartiallyBelowFold.ts", "../src/hooks/useEmojiPreviewEvents.ts", "../src/components/Layout/Flex.tsx", "../src/components/Layout/Space.tsx", "../src/components/Layout/Absolute.tsx", "../src/components/Layout/Relative.tsx", "../src/components/header/SkinTonePicker/BtnSkinToneVariation.tsx", "../src/components/header/SkinTonePicker/SkinTonePicker.tsx", "../src/components/footer/Preview.tsx", "../src/DomUtils/categoryNameFromDom.ts", "../src/hooks/useActiveCategoryScrollDetection.ts", "../src/hooks/useScrollCategoryIntoView.ts", "../src/hooks/useShouldHideCustomEmojis.ts", "../src/components/navigation/CategoryButton.tsx", "../src/components/navigation/CategoryNavigation.tsx", "../src/components/header/Search/BtnClearSearch.tsx", "../src/components/header/Search/CssSearch.tsx", "../src/components/header/Search/IcnSearch.tsx", "../src/components/header/Search/Search.tsx", "../src/components/header/Header.tsx", "../src/EmojiPickerReact.tsx", "../src/components/ErrorBoundary.tsx", "../src/components/emoji/ExportedEmoji.tsx", "../src/index.tsx"], "sourcesContent": ["export enum ClassNames {\n  hiddenOnSearch = 'epr-hidden-on-search',\n  searchActive = 'epr-search-active',\n  hidden = 'epr-hidden',\n  visible = 'epr-visible',\n  active = 'epr-active',\n  emoji = 'epr-emoji',\n  category = 'epr-emoji-category',\n  label = 'epr-emoji-category-label',\n  categoryContent = 'epr-emoji-category-content',\n  emojiHasVariations = 'epr-emoji-has-variations',\n  scrollBody = 'epr-body',\n  emojiList = 'epr-emoji-list',\n  external = '__EmojiPicker__',\n  emojiPicker = 'EmojiPickerReact',\n  open = 'epr-open',\n  vertical = 'epr-vertical',\n  horizontal = 'epr-horizontal',\n  variationPicker = 'epr-emoji-variation-picker',\n  darkTheme = 'epr-dark-theme',\n  autoTheme = 'epr-auto-theme'\n}\n\nexport function asSelectors(...classNames: ClassNames[]): string {\n  return classNames.map(c => `.${c}`).join('');\n}\n", "import { Styles, createSheet } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../DomUtils/classNames';\n\nexport const stylesheet = createSheet('epr', null);\n\nconst hidden = {\n  display: 'none',\n  opacity: '0',\n  pointerEvents: 'none',\n  visibility: 'hidden',\n  overflow: 'hidden'\n};\n\nexport const commonStyles = stylesheet.create({\n  hidden: {\n    '.': ClassNames.hidden,\n    ...hidden\n  }\n});\n\nexport const PickerStyleTag = React.memo(function PickerStyleTag() {\n  return (\n    <style\n      suppressHydrationWarning\n      dangerouslySetInnerHTML={{ __html: stylesheet.getStyle() }}\n    />\n  );\n});\n\nexport const commonInteractionStyles = stylesheet.create({\n  '.epr-main': {\n    ':has(input:not(:placeholder-shown))': {\n      categoryBtn: {\n        ':hover': {\n          opacity: '1',\n          backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n        }\n      },\n      hiddenOnSearch: {\n        '.': ClassNames.hiddenOnSearch,\n        ...hidden\n      }\n    },\n    ':has(input:placeholder-shown)': {\n      visibleOnSearchOnly: hidden\n    }\n  },\n  hiddenOnReactions: {\n    transition: 'all 0.5s ease-in-out'\n  },\n  '.epr-reactions': {\n    hiddenOnReactions: {\n      height: '0px',\n      width: '0px',\n      opacity: '0',\n      pointerEvents: 'none',\n      overflow: 'hidden'\n    }\n  },\n  '.EmojiPickerReact:not(.epr-search-active)': {\n    categoryBtn: {\n      ':hover': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      },\n      '&.epr-active': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      }\n    },\n    visibleOnSearchOnly: {\n      '.': 'epr-visible-on-search-only',\n      ...hidden\n    }\n  }\n});\n\nexport function darkMode(key: string, value: Styles) {\n  return {\n    '.epr-dark-theme': {\n      [key]: value\n    },\n    '.epr-auto-theme': {\n      [key]: {\n        '@media (prefers-color-scheme: dark)': value\n      }\n    }\n  };\n}\n", "import { PickerConfig } from './config';\n\n// eslint-disable-next-line complexity\nexport function compareConfig(prev: PickerConfig, next: PickerConfig) {\n  const prevCustomEmojis = prev.customEmojis ?? [];\n  const nextCustomEmojis = next.customEmojis ?? [];\n  return (\n    prev.open === next.open &&\n    prev.emojiVersion === next.emojiVersion &&\n    prev.reactionsDefaultOpen === next.reactionsDefaultOpen &&\n    prev.searchPlaceHolder === next.searchPlaceHolder &&\n    prev.searchPlaceholder === next.searchPlaceholder &&\n    prev.defaultSkinTone === next.defaultSkinTone &&\n    prev.skinTonesDisabled === next.skinTonesDisabled &&\n    prev.autoFocusSearch === next.autoFocusSearch &&\n    prev.emojiStyle === next.emojiStyle &&\n    prev.theme === next.theme &&\n    prev.suggestedEmojisMode === next.suggestedEmojisMode &&\n    prev.lazyLoadEmojis === next.lazyLoadEmojis &&\n    prev.className === next.className &&\n    prev.height === next.height &&\n    prev.width === next.width &&\n    prev.style === next.style &&\n    prev.searchDisabled === next.searchDisabled &&\n    prev.skinTonePickerLocation === next.skinTonePickerLocation &&\n    prevCustomEmojis.length === nextCustomEmojis.length\n  );\n}", "export const DEFAULT_REACTIONS = [\n  '1f44d', // 👍\n  '2764-fe0f', // ❤️\n  '1f603', // 😃\n  '1f622', // 😢\n  '1f64f', // 🙏\n  '1f44e', // 👎\n  '1f621' // 😡\n];\n", "export type EmojiClickData = {\n  activeSkinTone: SkinTones;\n  unified: string;\n  unifiedWithoutSkinTone: string;\n  emoji: string;\n  names: string[];\n  imageUrl: string;\n  getImageUrl: (emojiStyle?: EmojiStyle) => string;\n  isCustom: boolean;\n};\n\nexport enum SuggestionMode {\n  RECENT = 'recent',\n  FREQUENT = 'frequent'\n}\n\nexport enum EmojiStyle {\n  NATIVE = 'native',\n  APPLE = 'apple',\n  TWITTER = 'twitter',\n  GOOGLE = 'google',\n  FACEBOOK = 'facebook'\n}\n\nexport enum Theme {\n  DARK = 'dark',\n  LIGHT = 'light',\n  AUTO = 'auto'\n}\n\nexport enum SkinTones {\n  NEUTRAL = 'neutral',\n  LIGHT = '1f3fb',\n  MEDIUM_LIGHT = '1f3fc',\n  MEDIUM = '1f3fd',\n  MEDIUM_DARK = '1f3fe',\n  DARK = '1f3ff'\n}\n\nexport enum Categories {\n  SUGGESTED = 'suggested',\n  CUSTOM = 'custom',\n  SMILEYS_PEOPLE = 'smileys_people',\n  ANIMALS_NATURE = 'animals_nature',\n  FOOD_DRINK = 'food_drink',\n  TRAVEL_PLACES = 'travel_places',\n  ACTIVITIES = 'activities',\n  OBJECTS = 'objects',\n  SYMBOLS = 'symbols',\n  FLAGS = 'flags'\n}\n\nexport enum SkinTonePickerLocation {\n  SEARCH = 'SEARCH',\n  PREVIEW = 'PREVIEW'\n}\n", "import { Categories, SuggestionMode } from '../types/exposedTypes';\n\nexport { Categories };\n\nconst categoriesOrdered: Categories[] = [\n  Categories.SUGGESTED,\n  Categories.CUSTOM,\n  Categories.SMILEYS_PEOPLE,\n  Categories.ANIMALS_NATURE,\n  Categories.FOOD_DRINK,\n  Categories.TRAVEL_PLACES,\n  Categories.ACTIVITIES,\n  Categories.OBJECTS,\n  Categories.SYMBOLS,\n  Categories.FLAGS\n];\n\nexport const SuggestedRecent: CategoryConfig = {\n  name: 'Recently Used',\n  category: Categories.SUGGESTED\n};\n\nexport type CustomCategoryConfig = {\n  category: Categories.CUSTOM;\n  name: string;\n};\n\nconst configByCategory: Record<Categories, CategoryConfig> = {\n  [Categories.SUGGESTED]: {\n    category: Categories.SUGGESTED,\n    name: 'Frequently Used'\n  },\n  [Categories.CUSTOM]: {\n    category: Categories.CUSTOM,\n    name: 'Custom Emojis'\n  },\n  [Categories.SMILEYS_PEOPLE]: {\n    category: Categories.SMILEYS_PEOPLE,\n    name: 'Smileys & People'\n  },\n  [Categories.ANIMALS_NATURE]: {\n    category: Categories.ANIMALS_NATURE,\n    name: 'Animals & Nature'\n  },\n  [Categories.FOOD_DRINK]: {\n    category: Categories.FOOD_DRINK,\n    name: 'Food & Drink'\n  },\n  [Categories.TRAVEL_PLACES]: {\n    category: Categories.TRAVEL_PLACES,\n    name: 'Travel & Places'\n  },\n  [Categories.ACTIVITIES]: {\n    category: Categories.ACTIVITIES,\n    name: 'Activities'\n  },\n  [Categories.OBJECTS]: {\n    category: Categories.OBJECTS,\n    name: 'Objects'\n  },\n  [Categories.SYMBOLS]: {\n    category: Categories.SYMBOLS,\n    name: 'Symbols'\n  },\n  [Categories.FLAGS]: {\n    category: Categories.FLAGS,\n    name: 'Flags'\n  }\n};\n\nexport function baseCategoriesConfig(\n  modifiers?: Record<Categories, CategoryConfig>\n): CategoriesConfig {\n  return categoriesOrdered.map(category => {\n    return {\n      ...configByCategory[category],\n      ...(modifiers && modifiers[category] && modifiers[category])\n    };\n  });\n}\n\nexport function categoryFromCategoryConfig(category: CategoryConfig) {\n  return category.category;\n}\n\nexport function categoryNameFromCategoryConfig(category: CategoryConfig) {\n  return category.name;\n}\n\nexport type CategoriesConfig = CategoryConfig[];\n\nexport type CategoryConfig = {\n  category: Categories;\n  name: string;\n};\n\nexport type UserCategoryConfig = Array<Categories | CategoryConfig>;\n\nexport function mergeCategoriesConfig(\n  userCategoriesConfig: UserCategoryConfig = [],\n  modifiers: CategoryConfigModifiers = {}\n): CategoriesConfig {\n  const extra = {} as Record<Categories, CategoryConfig>;\n\n  if (modifiers.suggestionMode === SuggestionMode.RECENT) {\n    extra[Categories.SUGGESTED] = SuggestedRecent;\n  }\n\n  const base = baseCategoriesConfig(extra);\n  if (!userCategoriesConfig?.length) {\n    return base;\n  }\n\n  return userCategoriesConfig.map(category => {\n    if (typeof category === 'string') {\n      return getBaseConfigByCategory(category, extra[category]);\n    }\n\n    return {\n      ...getBaseConfigByCategory(category.category, extra[category.category]),\n      ...category\n    };\n  });\n}\n\nfunction getBaseConfigByCategory(\n  category: Categories,\n  modifier: CategoryConfig = {} as CategoryConfig\n) {\n  return Object.assign(configByCategory[category], modifier);\n}\n\ntype CategoryConfigModifiers = {\n  suggestionMode?: SuggestionMode;\n};\n", "import { EmojiStyle } from '../types/exposedTypes';\n\nconst CDN_URL_APPLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/';\nconst CDN_URL_FACEBOOK =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-facebook/img/facebook/64/';\nconst CDN_URL_TWITTER =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-twitter/img/twitter/64/';\nconst CDN_URL_GOOGLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-google/img/google/64/';\n\nexport function cdnUrl(emojiStyle: EmojiStyle): string {\n  switch (emojiStyle) {\n    case EmojiStyle.TWITTER:\n      return CDN_URL_TWITTER;\n    case EmojiStyle.GOOGLE:\n      return CDN_URL_GOOGLE;\n    case EmojiStyle.FACEBOOK:\n      return CDN_URL_FACEBOOK;\n    case EmojiStyle.APPLE:\n    default:\n      return CDN_URL_APPLE;\n  }\n}\n", "import { SkinTones } from '../types/exposedTypes';\n\nconst skinToneVariations = [\n  SkinTones.NEUTRAL,\n  SkinTones.LIGHT,\n  SkinTones.MEDIUM_LIGHT,\n  SkinTones.MEDIUM,\n  SkinTones.MEDIUM_DARK,\n  SkinTones.DARK\n];\n\nexport const skinTonesNamed = Object.entries(SkinTones).reduce(\n  (acc, [key, value]) => {\n    acc[value] = key;\n    return acc;\n  },\n  {} as Record<string, string>\n);\n\nexport const skinTonesMapped: Record<\n  string,\n  string\n> = skinToneVariations.reduce(\n  (mapped, skinTone) =>\n    Object.assign(mapped, {\n      [skinTone]: skinTone\n    }),\n  {}\n);\n\nexport default skinToneVariations;\n", "import emojis from '../data/emojis';\n\nexport enum EmojiProperties {\n  name = 'n',\n  unified = 'u',\n  variations = 'v',\n  added_in = 'a',\n  imgUrl = 'imgUrl'\n}\n\nexport interface DataEmoji extends WithName {\n  [EmojiProperties.unified]: string;\n  [EmojiProperties.variations]?: string[];\n  [EmojiProperties.added_in]: string;\n  [EmojiProperties.imgUrl]?: string;\n}\n\nexport type DataEmojis = DataEmoji[];\n\nexport type DataGroups = keyof typeof emojis;\n\nexport type WithName = {\n  [EmojiProperties.name]: string[];\n};\n", "import { DataEmoji } from './DataTypes';\nimport { allEmojis, emojiNames, emojiUnified } from './emojiSelectors';\n\nexport const alphaNumericEmojiIndex: BaseIndex = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((searchIndex, emoji) => {\n    indexEmoji(emoji);\n    return searchIndex;\n  }, alphaNumericEmojiIndex as BaseIndex);\n});\n\ntype BaseIndex = Record<string, Record<string, DataEmoji>>;\n\nexport function indexEmoji(emoji: DataEmoji): void {\n  const joinedNameString = emojiNames(emoji)\n    .flat()\n    .join('')\n    .toLowerCase()\n    .replace(/[^a-zA-Z\\d]/g, '')\n    .split('');\n\n  joinedNameString.forEach(char => {\n    alphaNumericEmojiIndex[char] = alphaNumericEmojiIndex[char] ?? {};\n\n    alphaNumericEmojiIndex[char][emojiUnified(emoji)] = emoji;\n  });\n}\n", "import { Categories } from '../config/categoryConfig';\nimport { cdnUrl } from '../config/cdnUrls';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport emojis from '../data/emojis';\nimport skinToneVariations, {\n  skinTonesMapped\n} from '../data/skinToneVariations';\nimport { EmojiStyle, SkinTones } from '../types/exposedTypes';\n\nimport { DataEmoji, DataEmojis, EmojiProperties, WithName } from './DataTypes';\nimport { indexEmoji } from './alphaNumericEmojiIndex';\n\nexport function emojiNames(emoji: WithName): string[] {\n  return emoji[EmojiProperties.name] ?? [];\n}\n\nexport function addedIn(emoji: DataEmoji): number {\n  return parseFloat(emoji[EmojiProperties.added_in]);\n}\n\nexport function emojiName(emoji?: WithName): string {\n  if (!emoji) {\n    return '';\n  }\n\n  return emojiNames(emoji)[0];\n}\n\nexport function unifiedWithoutSkinTone(unified: string): string {\n  const splat = unified.split('-');\n  const [skinTone] = splat.splice(1, 1);\n\n  if (skinTonesMapped[skinTone]) {\n    return splat.join('-');\n  }\n\n  return unified;\n}\n\nexport function emojiUnified(emoji: DataEmoji, skinTone?: string): string {\n  const unified = emoji[EmojiProperties.unified];\n\n  if (!skinTone || !emojiHasVariations(emoji)) {\n    return unified;\n  }\n\n  return emojiVariationUnified(emoji, skinTone) ?? unified;\n}\n\nexport function emojisByCategory(category: Categories): DataEmojis {\n  // @ts-ignore\n  return emojis?.[category] ?? [];\n}\n\n// WARNING: DO NOT USE DIRECTLY\nexport function emojiUrlByUnified(\n  unified: string,\n  emojiStyle: EmojiStyle\n): string {\n  return `${cdnUrl(emojiStyle)}${unified}.png`;\n}\n\nexport function emojiVariations(emoji: DataEmoji): string[] {\n  return emoji[EmojiProperties.variations] ?? [];\n}\n\nexport function emojiHasVariations(emoji: DataEmoji): boolean {\n  return emojiVariations(emoji).length > 0;\n}\n\nexport function emojiVariationUnified(\n  emoji: DataEmoji,\n  skinTone?: string\n): string | undefined {\n  return skinTone\n    ? emojiVariations(emoji).find(variation => variation.includes(skinTone))\n    : emojiUnified(emoji);\n}\n\nexport function emojiByUnified(unified?: string): DataEmoji | undefined {\n  if (!unified) {\n    return;\n  }\n\n  if (allEmojisByUnified[unified]) {\n    return allEmojisByUnified[unified];\n  }\n\n  const withoutSkinTone = unifiedWithoutSkinTone(unified);\n  return allEmojisByUnified[withoutSkinTone];\n}\n\nexport const allEmojis: DataEmojis = Object.values(emojis).flat();\n\nexport function setCustomEmojis(customEmojis: CustomEmoji[]): void {\n  emojis[Categories.CUSTOM].length = 0;\n\n  customEmojis.forEach(emoji => {\n    const emojiData = customToRegularEmoji(emoji);\n\n    emojis[Categories.CUSTOM].push(emojiData as never);\n\n    if (allEmojisByUnified[emojiData[EmojiProperties.unified]]) {\n      return;\n    }\n\n    allEmojis.push(emojiData);\n    allEmojisByUnified[emojiData[EmojiProperties.unified]] = emojiData;\n    indexEmoji(emojiData);\n  });\n}\n\nfunction customToRegularEmoji(emoji: CustomEmoji): DataEmoji {\n  return {\n    [EmojiProperties.name]: emoji.names.map(name => name.toLowerCase()),\n    [EmojiProperties.unified]: emoji.id.toLowerCase(),\n    [EmojiProperties.added_in]: '0',\n    [EmojiProperties.imgUrl]: emoji.imgUrl\n  };\n}\n\nconst allEmojisByUnified: {\n  [unified: string]: DataEmoji;\n} = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((allEmojis, Emoji) => {\n    allEmojis[emojiUnified(Emoji)] = Emoji;\n\n    if (emojiHasVariations(Emoji)) {\n      emojiVariations(Emoji).forEach(variation => {\n        allEmojis[variation] = Emoji;\n      });\n    }\n\n    return allEmojis;\n  }, allEmojisByUnified);\n});\n\nexport function activeVariationFromUnified(unified: string): SkinTones | null {\n  const [, suspectedSkinTone] = unified.split('-') as [string, SkinTones];\n  return skinToneVariations.includes(suspectedSkinTone)\n    ? suspectedSkinTone\n    : null;\n}\n", "import * as React from 'react';\n\nimport { DEFAULT_REACTIONS } from '../components/Reactions/DEFAULT_REACTIONS';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  setCustomEmojis,\n  emojiUrlByUnified,\n} from '../dataUtils/emojiSelectors';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme,\n} from '../types/exposedTypes';\n\nimport {\n  CategoriesConfig,\n  baseCategoriesConfig,\n  mergeCategoriesConfig,\n} from './categoryConfig';\nimport { CustomEmoji } from './customEmojiConfig';\n\nconst KNOWN_FAILING_EMOJIS = ['2640-fe0f', '2642-fe0f', '2695-fe0f'];\n\nexport const DEFAULT_SEARCH_PLACEHOLDER = 'Search';\nexport const SEARCH_RESULTS_NO_RESULTS_FOUND = 'No results found';\nexport const SEARCH_RESULTS_SUFFIX =\n  ' found. Use up and down arrow keys to navigate.';\nexport const SEARCH_RESULTS_ONE_RESULT_FOUND =\n  '1 result' + SEARCH_RESULTS_SUFFIX;\nexport const SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND =\n  '%n results' + SEARCH_RESULTS_SUFFIX;\n\nexport function mergeConfig(\n  userConfig: PickerConfig = {}\n): PickerConfigInternal {\n  const base = basePickerConfig();\n\n  const previewConfig = Object.assign(\n    base.previewConfig,\n    userConfig.previewConfig ?? {}\n  );\n  const config = Object.assign(base, userConfig);\n\n  const categories = mergeCategoriesConfig(userConfig.categories, {\n    suggestionMode: config.suggestedEmojisMode,\n  });\n\n  config.hiddenEmojis.forEach((emoji) => {\n    config.unicodeToHide.add(emoji);\n  });\n\n  setCustomEmojis(config.customEmojis ?? []);\n\n  const skinTonePickerLocation = config.searchDisabled\n    ? SkinTonePickerLocation.PREVIEW\n    : config.skinTonePickerLocation;\n\n  return {\n    ...config,\n    categories,\n    previewConfig,\n    skinTonePickerLocation,\n  };\n}\n\nexport function basePickerConfig(): PickerConfigInternal {\n  return {\n    autoFocusSearch: true,\n    categories: baseCategoriesConfig(),\n    className: '',\n    customEmojis: [],\n    defaultSkinTone: SkinTones.NEUTRAL,\n    emojiStyle: EmojiStyle.APPLE,\n    emojiVersion: null,\n    getEmojiUrl: emojiUrlByUnified,\n    height: 450,\n    lazyLoadEmojis: false,\n    previewConfig: {\n      ...basePreviewConfig,\n    },\n    searchDisabled: false,\n    searchPlaceHolder: DEFAULT_SEARCH_PLACEHOLDER,\n    searchPlaceholder: DEFAULT_SEARCH_PLACEHOLDER,\n    skinTonePickerLocation: SkinTonePickerLocation.SEARCH,\n    skinTonesDisabled: false,\n    style: {},\n    suggestedEmojisMode: SuggestionMode.FREQUENT,\n    theme: Theme.LIGHT,\n    unicodeToHide: new Set<string>(KNOWN_FAILING_EMOJIS),\n    width: 350,\n    reactionsDefaultOpen: false,\n    reactions: DEFAULT_REACTIONS,\n    open: true,\n    allowExpandReactions: true,\n    hiddenEmojis: [],\n  };\n}\n\nexport type PickerConfigInternal = {\n  emojiVersion: string | null;\n  searchPlaceHolder: string;\n  searchPlaceholder: string;\n  defaultSkinTone: SkinTones;\n  skinTonesDisabled: boolean;\n  autoFocusSearch: boolean;\n  emojiStyle: EmojiStyle;\n  categories: CategoriesConfig;\n  theme: Theme;\n  suggestedEmojisMode: SuggestionMode;\n  lazyLoadEmojis: boolean;\n  previewConfig: PreviewConfig;\n  className: string;\n  height: PickerDimensions;\n  width: PickerDimensions;\n  style: React.CSSProperties;\n  getEmojiUrl: GetEmojiUrl;\n  searchDisabled: boolean;\n  skinTonePickerLocation: SkinTonePickerLocation;\n  unicodeToHide: Set<string>;\n  customEmojis: CustomEmoji[];\n  reactionsDefaultOpen: boolean;\n  reactions: string[];\n  open: boolean;\n  allowExpandReactions: boolean;\n  hiddenEmojis: string[];\n};\n\nexport type PreviewConfig = {\n  defaultEmoji: string;\n  defaultCaption: string;\n  showPreview: boolean;\n};\n\nconst basePreviewConfig: PreviewConfig = {\n  defaultEmoji: '1f60a',\n  defaultCaption: \"What's your mood?\",\n  showPreview: true,\n};\n\ntype ConfigExternal = {\n  previewConfig: Partial<PreviewConfig>;\n  onEmojiClick: MouseDownEvent;\n  onReactionClick: MouseDownEvent;\n  onSkinToneChange: OnSkinToneChange;\n} & Omit<PickerConfigInternal, 'previewConfig' | 'unicodeToHide'>;\n\nexport type PickerConfig = Partial<ConfigExternal>;\n\nexport type PickerDimensions = string | number;\n\nexport type MouseDownEvent = (\n  emoji: EmojiClickData,\n  event: MouseEvent,\n  api?: OnEmojiClickApi\n) => void;\nexport type OnSkinToneChange = (emoji: SkinTones) => void;\n\ntype OnEmojiClickApi = {\n  collapseToReactions: () => void;\n};\n", "import * as React from 'react';\n\nimport { compareConfig } from '../../config/compareConfig';\nimport {\n  basePickerConfig,\n  mergeConfig,\n  PickerConfig,\n  PickerConfigInternal\n} from '../../config/config';\n\ntype Props = PickerConfig &\n  Readonly<{\n    children: React.ReactNode;\n  }>;\n\nconst ConfigContext = React.createContext<PickerConfigInternal>(\n  basePickerConfig()\n);\n\nexport function PickerConfigProvider({ children, ...config }: Props) {\n  const mergedConfig = useSetConfig(config);\n\n  return (\n    <ConfigContext.Provider value={mergedConfig}>\n      {children}\n    </ConfigContext.Provider>\n  );\n}\n\nexport function useSetConfig(config: PickerConfig) {\n  const [mergedConfig, setMergedConfig] = React.useState(() =>\n    mergeConfig(config)\n  );\n\n  React.useEffect(() => {\n    if (compareConfig(mergedConfig, config)) {\n      return;\n    }\n    setMergedConfig(mergeConfig(config));\n    // not gonna...\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n    config.customEmojis?.length,\n    config.open,\n    config.emojiVersion,\n    config.reactionsDefaultOpen,\n    config.searchPlaceHolder,\n    config.searchPlaceholder,\n    config.defaultSkinTone,\n    config.skinTonesDisabled,\n    config.autoFocusSearch,\n    config.emojiStyle,\n    config.theme,\n    config.suggestedEmojisMode,\n    config.lazyLoadEmojis,\n    config.className,\n    config.height,\n    config.width,\n    config.searchDisabled,\n    config.skinTonePickerLocation,\n    config.allowExpandReactions\n  ]);\n\n  return mergedConfig;\n}\n\nexport function usePickerConfig() {\n  return React.useContext(ConfigContext);\n}\n", "import { useRef, useState } from 'react';\n\nexport function useDebouncedState<T>(\n  initialValue: T,\n  delay: number = 0\n): [T, (value: T) => Promise<T>] {\n  const [state, setState] = useState<T>(initialValue);\n  const timer = useRef<number | null>(null);\n\n  function debouncedSetState(value: T) {\n    return new Promise<T>(resolve => {\n      if (timer.current) {\n        clearTimeout(timer.current);\n      }\n\n      timer.current = window?.setTimeout(() => {\n        setState(value);\n        resolve(value);\n      }, delay);\n    });\n  }\n\n  return [state, debouncedSetState];\n}\n", "import { useUnicodeToHide } from \"../config/useConfig\";\n\nexport function useIsUnicodeHidden() {\n    const unicodeToHide = useUnicodeToHide();\n    return (emojiUnified: string) => unicodeToHide.has(emojiUnified);\n  }\n", "import { useRef, useMemo } from 'react';\n\nimport { useEmojiVersionConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  addedIn,\n  allEmojis,\n  emojiUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { useIsUnicodeHidden } from './useHideEmojisByUniocode';\n\nexport function useDisallowedEmojis() {\n  const DisallowedEmojisRef = useRef<Record<string, boolean>>({});\n  const emojiVersionConfig = useEmojiVersionConfig();\n\n  return useMemo(() => {\n    const emojiVersion = parseFloat(`${emojiVersionConfig}`);\n\n    if (!emojiVersionConfig || Number.isNaN(emojiVersion)) {\n      return DisallowedEmojisRef.current;\n    }\n\n    return allEmojis.reduce((disallowedEmojis, emoji) => {\n      if (addedInNewerVersion(emoji, emojiVersion)) {\n        disallowedEmojis[emojiUnified(emoji)] = true;\n      }\n\n      return disallowedEmojis;\n    }, DisallowedEmojisRef.current);\n  }, [emojiVersionConfig]);\n}\n\nexport function useIsEmojiDisallowed() {\n  const disallowedEmojis = useDisallowedEmojis();\n  const isUnicodeHidden = useIsUnicodeHidden();\n\n  return function isEmojiDisallowed(emoji: DataEmoji) {\n    const unified = unifiedWithoutSkinTone(emojiUnified(emoji));\n\n    return Boolean(disallowedEmojis[unified] || isUnicodeHidden(unified));\n  };\n}\n\nfunction addedInNewerVersion(\n  emoji: DataEmoji,\n  supportedLevel: number\n): boolean {\n  return addedIn(emoji) > supportedLevel;\n}\n", "import { useEffect } from 'react';\nimport * as React from 'react';\n\nexport function useMarkInitialLoad(\n  dispatch: React.Dispatch<React.SetStateAction<boolean>>\n) {\n  useEffect(() => {\n    dispatch(true);\n  }, [dispatch]);\n}\n", "import * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  useDefaultSkinToneConfig,\n  useReactionsOpenConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { alphaNumericEmojiIndex } from '../../dataUtils/alphaNumericEmojiIndex';\nimport { useDebouncedState } from '../../hooks/useDebouncedState';\nimport { useDisallowedEmojis } from '../../hooks/useDisallowedEmojis';\nimport { FilterDict } from '../../hooks/useFilter';\nimport { useMarkInitialLoad } from '../../hooks/useInitialLoad';\nimport { SkinTones } from '../../types/exposedTypes';\n\nexport function PickerContextProvider({ children }: Props) {\n  const disallowedEmojis = useDisallowedEmojis();\n  const defaultSkinTone = useDefaultSkinToneConfig();\n  const reactionsDefaultOpen = useReactionsOpenConfig();\n\n  // Initialize the filter with the inititial dictionary\n  const filterRef = React.useRef<FilterState>(alphaNumericEmojiIndex);\n  const disallowClickRef = React.useRef<boolean>(false);\n  const disallowMouseRef = React.useRef<boolean>(false);\n  const disallowedEmojisRef = React.useRef<Record<string, boolean>>(\n    disallowedEmojis\n  );\n\n  const suggestedUpdateState = useDebouncedState(Date.now(), 200);\n  const searchTerm = useDebouncedState('', 100);\n  const skinToneFanOpenState = useState<boolean>(false);\n  const activeSkinTone = useState<SkinTones>(defaultSkinTone);\n  const activeCategoryState = useState<ActiveCategoryState>(null);\n  const emojisThatFailedToLoadState = useState<Set<string>>(new Set());\n  const emojiVariationPickerState = useState<DataEmoji | null>(null);\n  const reactionsModeState = useState(reactionsDefaultOpen);\n  const [isPastInitialLoad, setIsPastInitialLoad] = useState(false);\n\n  useMarkInitialLoad(setIsPastInitialLoad);\n\n  return (\n    <PickerContext.Provider\n      value={{\n        activeCategoryState,\n        activeSkinTone,\n        disallowClickRef,\n        disallowMouseRef,\n        disallowedEmojisRef,\n        emojiVariationPickerState,\n        emojisThatFailedToLoadState,\n        filterRef,\n        isPastInitialLoad,\n        searchTerm,\n        skinToneFanOpenState,\n        suggestedUpdateState,\n        reactionsModeState\n      }}\n    >\n      {children}\n    </PickerContext.Provider>\n  );\n}\n\ntype ReactState<T> = [T, React.Dispatch<React.SetStateAction<T>>];\n\nconst PickerContext = React.createContext<{\n  searchTerm: [string, (term: string) => Promise<string>];\n  suggestedUpdateState: [number, (term: number) => void];\n  activeCategoryState: ReactState<ActiveCategoryState>;\n  activeSkinTone: ReactState<SkinTones>;\n  emojisThatFailedToLoadState: ReactState<Set<string>>;\n  isPastInitialLoad: boolean;\n  emojiVariationPickerState: ReactState<DataEmoji | null>;\n  skinToneFanOpenState: ReactState<boolean>;\n  filterRef: React.MutableRefObject<FilterState>;\n  disallowClickRef: React.MutableRefObject<boolean>;\n  disallowMouseRef: React.MutableRefObject<boolean>;\n  disallowedEmojisRef: React.MutableRefObject<Record<string, boolean>>;\n  reactionsModeState: ReactState<boolean>;\n}>({\n  activeCategoryState: [null, () => {}],\n  activeSkinTone: [SkinTones.NEUTRAL, () => {}],\n  disallowClickRef: { current: false },\n  disallowMouseRef: { current: false },\n  disallowedEmojisRef: { current: {} },\n  emojiVariationPickerState: [null, () => {}],\n  emojisThatFailedToLoadState: [new Set(), () => {}],\n  filterRef: { current: {} },\n  isPastInitialLoad: true,\n  searchTerm: ['', () => new Promise<string>(() => undefined)],\n  skinToneFanOpenState: [false, () => {}],\n  suggestedUpdateState: [Date.now(), () => {}],\n  reactionsModeState: [false, () => {}]\n});\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport function useFilterRef() {\n  const { filterRef } = React.useContext(PickerContext);\n  return filterRef;\n}\n\nexport function useDisallowClickRef() {\n  const { disallowClickRef } = React.useContext(PickerContext);\n  return disallowClickRef;\n}\n\nexport function useDisallowMouseRef() {\n  const { disallowMouseRef } = React.useContext(PickerContext);\n  return disallowMouseRef;\n}\n\nexport function useReactionsModeState() {\n  const { reactionsModeState } = React.useContext(PickerContext);\n  return reactionsModeState;\n}\n\nexport function useSearchTermState() {\n  const { searchTerm } = React.useContext(PickerContext);\n  return searchTerm;\n}\n\nexport function useActiveSkinToneState(): [\n  SkinTones,\n  (skinTone: SkinTones) => void\n] {\n  const { activeSkinTone } = React.useContext(PickerContext);\n  return activeSkinTone;\n}\n\nexport function useEmojisThatFailedToLoadState() {\n  const { emojisThatFailedToLoadState } = React.useContext(PickerContext);\n  return emojisThatFailedToLoadState;\n}\n\nexport function useIsPastInitialLoad(): boolean {\n  const { isPastInitialLoad } = React.useContext(PickerContext);\n  return isPastInitialLoad;\n}\n\nexport function useEmojiVariationPickerState() {\n  const { emojiVariationPickerState } = React.useContext(PickerContext);\n  return emojiVariationPickerState;\n}\n\nexport function useSkinToneFanOpenState() {\n  const { skinToneFanOpenState } = React.useContext(PickerContext);\n  return skinToneFanOpenState;\n}\n\nexport function useDisallowedEmojisRef() {\n  const { disallowedEmojisRef } = React.useContext(PickerContext);\n  return disallowedEmojisRef;\n}\n\nexport function useUpdateSuggested(): [number, () => void] {\n  const { suggestedUpdateState } = React.useContext(PickerContext);\n\n  const [suggestedUpdated, setsuggestedUpdate] = suggestedUpdateState;\n  return [\n    suggestedUpdated,\n    function updateSuggested() {\n      setsuggestedUpdate(Date.now());\n    }\n  ];\n}\n\nexport type FilterState = Record<string, FilterDict>;\n\ntype ActiveCategoryState = null | string;\n", "import React from 'react';\n\nimport { MouseDownEvent, OnSkinToneChange } from './config';\n\nexport type MutableConfig = {\n  onEmojiClick?: MouseDownEvent;\n  onReactionClick?: MouseDownEvent;\n  onSkinToneChange?: OnSkinToneChange;\n};\n\nexport const MutableConfigContext = React.createContext<\n  React.MutableRefObject<MutableConfig>\n>({} as React.MutableRefObject<MutableConfig>);\n\nexport function useMutableConfig(): React.MutableRefObject<MutableConfig> {\n  const mutableConfig = React.useContext(MutableConfigContext);\n  return mutableConfig;\n}\n\nexport function useDefineMutableConfig(\n  config: MutableConfig\n): React.MutableRefObject<MutableConfig> {\n  const MutableConfigRef = React.useRef<MutableConfig>({\n    onEmojiClick: config.onEmojiClick || emptyFunc,\n    onReactionClick: config.onReactionClick || config.onEmojiClick,\n    onSkinToneChange: config.onSkinToneChange || emptyFunc\n  });\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onEmojiClick = config.onEmojiClick || emptyFunc;\n    MutableConfigRef.current.onReactionClick =\n      config.onReactionClick || config.onEmojiClick;\n  }, [config.onEmojiClick, config.onReactionClick]);\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onSkinToneChange =\n      config.onSkinToneChange || emptyFunc;\n  }, [config.onSkinToneChange]);\n\n  return MutableConfigRef;\n}\n\nfunction emptyFunc() {}\n", "import * as React from 'react';\n\nimport { usePickerConfig } from '../components/context/PickerConfigContext';\nimport { useReactionsModeState } from '../components/context/PickerContext';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme\n} from '../types/exposedTypes';\n\nimport { CategoriesConfig } from './categoryConfig';\nimport {\n  DEFAULT_SEARCH_PLACEHOLDER,\n  SEARCH_RESULTS_NO_RESULTS_FOUND,\n  SEARCH_RESULTS_ONE_RESULT_FOUND,\n  SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND,\n  PickerDimensions,\n  PreviewConfig\n} from './config';\nimport { CustomEmoji } from './customEmojiConfig';\nimport { useMutableConfig } from './mutableConfig';\n\nexport enum MOUSE_EVENT_SOURCE {\n  REACTIONS = 'reactions',\n  PICKER = 'picker'\n}\n\nexport function useSearchPlaceHolderConfig(): string {\n  const { searchPlaceHolder, searchPlaceholder } = usePickerConfig();\n  return (\n    [searchPlaceHolder, searchPlaceholder].find(\n      p => p !== DEFAULT_SEARCH_PLACEHOLDER\n    ) ?? DEFAULT_SEARCH_PLACEHOLDER\n  );\n}\n\nexport function useDefaultSkinToneConfig(): SkinTones {\n  const { defaultSkinTone } = usePickerConfig();\n  return defaultSkinTone;\n}\n\nexport function useAllowExpandReactions(): boolean {\n  const { allowExpandReactions } = usePickerConfig();\n  return allowExpandReactions;\n}\n\nexport function useSkinTonesDisabledConfig(): boolean {\n  const { skinTonesDisabled } = usePickerConfig();\n  return skinTonesDisabled;\n}\n\nexport function useEmojiStyleConfig(): EmojiStyle {\n  const { emojiStyle } = usePickerConfig();\n  return emojiStyle;\n}\n\nexport function useAutoFocusSearchConfig(): boolean {\n  const { autoFocusSearch } = usePickerConfig();\n  return autoFocusSearch;\n}\n\nexport function useCategoriesConfig(): CategoriesConfig {\n  const { categories } = usePickerConfig();\n  return categories;\n}\n\nexport function useCustomEmojisConfig(): CustomEmoji[] {\n  const { customEmojis } = usePickerConfig();\n  return customEmojis;\n}\n\nexport function useOpenConfig(): boolean {\n  const { open } = usePickerConfig();\n  return open;\n}\n\nexport function useOnEmojiClickConfig(\n  mouseEventSource: MOUSE_EVENT_SOURCE\n): (emoji: EmojiClickData, event: MouseEvent) => void {\n  const { current } = useMutableConfig();\n  const [, setReactionsOpen] = useReactionsModeState();\n\n  const handler = current.onEmojiClick || (() => {});\n  const { onReactionClick } = current;\n\n  if (mouseEventSource === MOUSE_EVENT_SOURCE.REACTIONS && onReactionClick) {\n    return (...args) =>\n      onReactionClick(...args, {\n        collapseToReactions: () => {\n          setReactionsOpen(o => o);\n        }\n      });\n  }\n\n  return (...args) => {\n    handler(...args, {\n      collapseToReactions: () => {\n        setReactionsOpen(true);\n      }\n    });\n  };\n}\n\nexport function useOnSkinToneChangeConfig(): (skinTone: SkinTones) => void {\n  const { current } = useMutableConfig();\n\n  return current.onSkinToneChange || (() => {});\n}\n\nexport function usePreviewConfig(): PreviewConfig {\n  const { previewConfig } = usePickerConfig();\n  return previewConfig;\n}\n\nexport function useThemeConfig(): Theme {\n  const { theme } = usePickerConfig();\n\n  return theme;\n}\n\nexport function useSuggestedEmojisModeConfig(): SuggestionMode {\n  const { suggestedEmojisMode } = usePickerConfig();\n  return suggestedEmojisMode;\n}\n\nexport function useLazyLoadEmojisConfig(): boolean {\n  const { lazyLoadEmojis } = usePickerConfig();\n  return lazyLoadEmojis;\n}\n\nexport function useClassNameConfig(): string {\n  const { className } = usePickerConfig();\n  return className;\n}\n\nexport function useStyleConfig(): React.CSSProperties {\n  const { height, width, style } = usePickerConfig();\n  return { height: getDimension(height), width: getDimension(width), ...style };\n}\n\nexport function useReactionsOpenConfig(): boolean {\n  const { reactionsDefaultOpen } = usePickerConfig();\n  return reactionsDefaultOpen;\n}\n\nexport function useEmojiVersionConfig(): string | null {\n  const { emojiVersion } = usePickerConfig();\n  return emojiVersion;\n}\n\nexport function useSearchDisabledConfig(): boolean {\n  const { searchDisabled } = usePickerConfig();\n  return searchDisabled;\n}\n\nexport function useSkinTonePickerLocationConfig(): SkinTonePickerLocation {\n  const { skinTonePickerLocation } = usePickerConfig();\n  return skinTonePickerLocation;\n}\n\nexport function useUnicodeToHide() {\n  const { unicodeToHide } = usePickerConfig();\n  return unicodeToHide;\n}\n\nexport function useReactionsConfig(): string[] {\n  const { reactions } = usePickerConfig();\n  return reactions;\n}\n\nexport function useGetEmojiUrlConfig(): (\n  unified: string,\n  style: EmojiStyle\n) => string {\n  const { getEmojiUrl } = usePickerConfig();\n  return getEmojiUrl;\n}\n\nfunction getDimension(dimensionConfig: PickerDimensions): PickerDimensions {\n  return typeof dimensionConfig === 'number'\n    ? `${dimensionConfig}px`\n    : dimensionConfig;\n}\n\nexport function useSearchResultsConfig(searchResultsCount: number): string {\n  const hasResults = searchResultsCount > 0;\n  const isPlural = searchResultsCount > 1;\n\n  if (hasResults) {\n    return isPlural\n      ? SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND.replace(\n          '%n',\n          searchResultsCount.toString()\n        )\n      : SEARCH_RESULTS_ONE_RESULT_FOUND;\n  }\n\n  return SEARCH_RESULTS_NO_RESULTS_FOUND;\n}\n", "import { useSearchTermState } from '../components/context/PickerContext';\n\nexport default function useIsSearchMode(): boolean {\n  const [searchTerm] = useSearchTermState();\n\n  return !!searchTerm;\n}\n", "import { NullableElement } from './selectors';\n\nexport function focusElement(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    element.focus();\n  });\n}\n\nexport function focusPrevElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const prev = element.previousElementSibling as HTMLElement;\n\n  focusElement(prev);\n}\n\nexport function focusNextElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const next = element.nextElementSibling as HTMLElement;\n\n  focusElement(next);\n}\n\nexport function focusFirstElementChild(element: NullableElement) {\n  if (!element) return;\n\n  const first = element.firstElementChild as HTMLElement;\n\n  focusElement(first);\n}\n", "import { NullableElement } from './selectors';\n\nexport function getActiveElement() {\n  return document.activeElement as NullableElement;\n}\n", "import * as React from 'react';\n\nimport { focusElement } from '../../DomUtils/focusElement';\nimport { NullableElement } from '../../DomUtils/selectors';\n\nexport function ElementRefContextProvider({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const PickerMainRef = React.useRef<HTMLElement>(null);\n  const AnchoredEmojiRef = React.useRef<HTMLElement>(null);\n  const BodyRef = React.useRef<HTMLDivElement>(null);\n  const SearchInputRef = React.useRef<HTMLInputElement>(null);\n  const SkinTonePickerRef = React.useRef<HTMLDivElement>(null);\n  const CategoryNavigationRef = React.useRef<HTMLDivElement>(null);\n  const VariationPickerRef = React.useRef<HTMLDivElement>(null);\n  const ReactionsRef = React.useRef<HTMLUListElement>(null);\n\n  return (\n    <ElementRefContext.Provider\n      value={{\n        AnchoredEmojiRef,\n        BodyRef,\n        CategoryNavigationRef,\n        PickerMainRef,\n        SearchInputRef,\n        SkinTonePickerRef,\n        VariationPickerRef,\n        ReactionsRef\n      }}\n    >\n      {children}\n    </ElementRefContext.Provider>\n  );\n}\n\nexport type ElementRef<\n  E extends HTMLElement = HTMLElement\n> = React.MutableRefObject<E | null>;\n\ntype ElementRefs = {\n  PickerMainRef: ElementRef;\n  AnchoredEmojiRef: ElementRef;\n  SkinTonePickerRef: ElementRef<HTMLDivElement>;\n  SearchInputRef: ElementRef<HTMLInputElement>;\n  BodyRef: ElementRef<HTMLDivElement>;\n  CategoryNavigationRef: ElementRef<HTMLDivElement>;\n  VariationPickerRef: ElementRef<HTMLDivElement>;\n  ReactionsRef: ElementRef<HTMLUListElement>;\n};\n\nconst ElementRefContext = React.createContext<ElementRefs>({\n  AnchoredEmojiRef: React.createRef(),\n  BodyRef: React.createRef(),\n  CategoryNavigationRef: React.createRef(),\n  PickerMainRef: React.createRef(),\n  SearchInputRef: React.createRef(),\n  SkinTonePickerRef: React.createRef(),\n  VariationPickerRef: React.createRef(),\n  ReactionsRef: React.createRef()\n});\n\nfunction useElementRef() {\n  return React.useContext(ElementRefContext);\n}\n\nexport function usePickerMainRef() {\n  return useElementRef()['PickerMainRef'];\n}\n\nexport function useAnchoredEmojiRef() {\n  return useElementRef()['AnchoredEmojiRef'];\n}\n\nexport function useSetAnchoredEmojiRef(): (target: NullableElement) => void {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return (target: NullableElement) => {\n    if (target === null && AnchoredEmojiRef.current !== null) {\n      focusElement(AnchoredEmojiRef.current);\n    }\n\n    AnchoredEmojiRef.current = target;\n  };\n}\n\nexport function useBodyRef() {\n  return useElementRef()['BodyRef'];\n}\n\nexport function useReactionsRef() {\n  return useElementRef()['ReactionsRef'];\n}\n\nexport function useSearchInputRef() {\n  return useElementRef()['SearchInputRef'];\n}\n\nexport function useSkinTonePickerRef() {\n  return useElementRef()['SkinTonePickerRef'];\n}\n\nexport function useCategoryNavigationRef() {\n  return useElementRef()['CategoryNavigationRef'];\n}\n\nexport function useVariationPickerRef() {\n  return useElementRef()['VariationPickerRef'];\n}\n", "import { useCallback } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport {\n  categoryLabelHeight,\n  closestCategory,\n  closestScrollBody,\n  emojiDistanceFromScrollTop,\n  isEmojiBehindLabel,\n  NullableElement,\n  queryScrollBody\n} from './selectors';\n\nexport function scrollTo(root: NullableElement, top: number = 0) {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = top;\n  });\n}\n\nexport function scrollBy(root: NullableElement, by: number): void {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = $eprBody.scrollTop + by;\n  });\n}\n\nexport function useScrollTo() {\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    (top: number) => {\n      requestAnimationFrame(() => {\n        if (BodyRef.current) {\n          BodyRef.current.scrollTop = top;\n        }\n      });\n    },\n    [BodyRef]\n  );\n}\n\nexport function scrollEmojiAboveLabel(emoji: NullableElement) {\n  if (!emoji || !isEmojiBehindLabel(emoji)) {\n    return;\n  }\n\n  if (emoji.closest(asSelectors(ClassNames.variationPicker))) {\n    return;\n  }\n\n  const scrollBody = closestScrollBody(emoji);\n  const by = emojiDistanceFromScrollTop(emoji);\n  scrollBy(scrollBody, -(categoryLabelHeight(closestCategory(emoji)) - by));\n}\n", "import {\n  elementCountInRow,\n  elementIndexInRow,\n  getElementInNextRow,\n  getElementInPrevRow,\n  getElementInRow,\n  hasNextRow,\n  rowNumber\n} from './elementPositionInRow';\nimport { focusElement } from './focusElement';\nimport { scrollEmojiAboveLabel } from './scrollTo';\nimport {\n  allVisibleEmojis,\n  closestCategory,\n  firstVisibleEmoji,\n  lastVisibleEmoji,\n  nextCategory,\n  nextVisibleEmoji,\n  NullableElement,\n  prevCategory,\n  prevVisibleEmoji,\n  closestCategoryContent\n} from './selectors';\n\nexport function focusFirstVisibleEmoji(parent: NullableElement) {\n  const emoji = firstVisibleEmoji(parent);\n  focusElement(emoji);\n  scrollEmojiAboveLabel(emoji);\n}\n\nexport function focusAndClickFirstVisibleEmoji(parent: NullableElement) {\n  const firstEmoji = firstVisibleEmoji(parent);\n\n  focusElement(firstEmoji);\n  firstEmoji?.click();\n}\n\nexport function focusLastVisibleEmoji(parent: NullableElement) {\n  focusElement(lastVisibleEmoji(parent));\n}\n\nexport function focusNextVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = nextVisibleEmoji(element);\n\n  if (!next) {\n    return focusFirstVisibleEmoji(nextCategory(element));\n  }\n\n  focusElement(next);\n  scrollEmojiAboveLabel(next);\n}\n\nexport function focusPrevVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const prev = prevVisibleEmoji(element);\n\n  if (!prev) {\n    return focusLastVisibleEmoji(prevCategory(element));\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowUp(\n  element: NullableElement,\n  exitUp: () => void\n) {\n  if (!element) {\n    return;\n  }\n\n  const prev = visibleEmojiOneRowUp(element);\n\n  if (!prev) {\n    return exitUp();\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowDown(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = visibleEmojiOneRowDown(element);\n\n  return focusElement(next);\n}\n\nfunction visibleEmojiOneRowUp(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n\n  if (row === 0) {\n    const prevVisibleCategory = prevCategory(category);\n\n    if (!prevVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(prevVisibleCategory),\n      -1, // last row\n      countInRow,\n      indexInRow\n    );\n  }\n\n  return getElementInPrevRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n}\n\nfunction visibleEmojiOneRowDown(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n  if (!hasNextRow(categoryContent, element)) {\n    const nextVisibleCategory = nextCategory(category);\n\n    if (!nextVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(nextVisibleCategory),\n      0,\n      countInRow,\n      indexInRow\n    );\n  }\n\n  const itemInNextRow = getElementInNextRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n\n  return itemInNextRow;\n}\n", "import { useCallback } from 'react';\n\nimport {\n  useEmojiVariationPickerState,\n  useSkinToneFanOpenState\n} from '../components/context/PickerContext';\n\nexport function useCloseAllOpenToggles() {\n  const [variationPicker, setVariationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen, setSkinToneFanOpen] = useSkinToneFanOpenState();\n\n  const closeAllOpenToggles = useCallback(() => {\n    if (variationPicker) {\n      setVariationPicker(null);\n    }\n\n    if (skinToneFanOpen) {\n      setSkinToneFanOpen(false);\n    }\n  }, [\n    variationPicker,\n    skinToneFanOpen,\n    setVariationPicker,\n    setSkinToneFanOpen\n  ]);\n\n  return closeAllOpenToggles;\n}\n\nexport function useHasOpenToggles() {\n  const [variationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen] = useSkinToneFanOpenState();\n\n  return function hasOpenToggles() {\n    return !!variationPicker || skinToneFanOpen;\n  };\n}\n", "import { useEffect } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useDisallowMouseRef } from '../components/context/PickerContext';\n\nexport function useDisallowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function disallowMouseMove() {\n    DisallowMouseRef.current = true;\n  };\n}\n\nexport function useAllowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function allowMouseMove() {\n    DisallowMouseRef.current = false;\n  };\n}\n\nexport function useIsMouseDisallowed() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function isMouseDisallowed() {\n    return DisallowMouseRef.current;\n  };\n}\n\nexport function useOnMouseMove() {\n  const BodyRef = useBodyRef();\n  const allowMouseMove = useAllowMouseMove();\n  const isMouseDisallowed = useIsMouseDisallowed();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    bodyRef?.addEventListener('mousemove', onMouseMove, {\n      passive: true\n    });\n\n    function onMouseMove() {\n      if (isMouseDisallowed()) {\n        allowMouseMove();\n      }\n    }\n    return () => {\n      bodyRef?.removeEventListener('mousemove', onMouseMove);\n    };\n  }, [BodyRef, allowMouseMove, isMouseDisallowed]);\n}\n", "import { useCallback } from 'react';\n\nimport { focusElement, focusFirstElementChild } from '../DomUtils/focusElement';\nimport {\n  useCategoryNavigationRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\n\nexport function useFocusSearchInput() {\n  const SearchInputRef = useSearchInputRef();\n\n  return useCallback(() => {\n    focusElement(SearchInputRef.current);\n  }, [SearchInputRef]);\n}\n\nexport function useFocusSkinTonePicker() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n\n  return useCallback(() => {\n    if (!SkinTonePickerRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(SkinTonePickerRef.current);\n  }, [SkinTonePickerRef]);\n}\n\nexport function useFocusCategoryNavigation() {\n  const CategoryNavigationRef = useCategoryNavigationRef();\n\n  return useCallback(() => {\n    if (!CategoryNavigationRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(CategoryNavigationRef.current);\n  }, [CategoryNavigationRef]);\n}\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport {\n  usePickerMainRef,\n  useSearchInputRef\n} from '../components/context/ElementRefContext';\nimport {\n  FilterState,\n  useFilterRef,\n  useSearchTermState\n} from '../components/context/PickerContext';\nimport { useSearchResultsConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiNames } from '../dataUtils/emojiSelectors';\n\nimport { useFocusSearchInput } from './useFocus';\n\nfunction useSetFilterRef() {\n  const filterRef = useFilterRef();\n\n  return function setFilter(\n    setter: FilterState | ((current: FilterState) => FilterState)\n  ): void {\n    if (typeof setter === 'function') {\n      return setFilter(setter(filterRef.current));\n    }\n\n    filterRef.current = setter;\n  };\n}\n\nexport function useClearSearch() {\n  const applySearch = useApplySearch();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n\n  return function clearSearch() {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = '';\n    }\n\n    applySearch('');\n    focusSearchInput();\n  };\n}\n\nexport function useAppendSearch() {\n  const SearchInputRef = useSearchInputRef();\n  const applySearch = useApplySearch();\n\n  return function appendSearch(str: string) {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = `${SearchInputRef.current.value}${str}`;\n      applySearch(getNormalizedSearchTerm(SearchInputRef.current.value));\n    } else {\n      applySearch(getNormalizedSearchTerm(str));\n    }\n  };\n}\n\nexport function useFilter() {\n  const SearchInputRef = useSearchInputRef();\n  const filterRef = useFilterRef();\n  const setFilterRef = useSetFilterRef();\n  const applySearch = useApplySearch();\n\n  const [searchTerm] = useSearchTermState();\n  const statusSearchResults = getStatusSearchResults(\n    filterRef.current,\n    searchTerm\n  );\n\n  return {\n    onChange,\n    searchTerm,\n    SearchInputRef,\n    statusSearchResults\n  };\n\n  function onChange(inputValue: string) {\n    const filter = filterRef.current;\n\n    const nextValue = inputValue.toLowerCase();\n\n    if (filter?.[nextValue] || nextValue.length <= 1) {\n      return applySearch(nextValue);\n    }\n\n    const longestMatch = findLongestMatch(nextValue, filter);\n\n    if (!longestMatch) {\n      // Can we even get here?\n      // If so, we need to search among all emojis\n      return applySearch(nextValue);\n    }\n\n    setFilterRef(current =>\n      Object.assign(current, {\n        [nextValue]: filterEmojiObjectByKeyword(longestMatch, nextValue)\n      })\n    );\n    applySearch(nextValue);\n  }\n}\n\nfunction useApplySearch() {\n  const [, setSearchTerm] = useSearchTermState();\n  const PickerMainRef = usePickerMainRef();\n\n  return function applySearch(searchTerm: string) {\n    requestAnimationFrame(() => {\n      setSearchTerm(searchTerm ? searchTerm?.toLowerCase() : searchTerm).then(\n        () => {\n          scrollTo(PickerMainRef.current, 0);\n        }\n      );\n    });\n  };\n}\n\nfunction filterEmojiObjectByKeyword(\n  emojis: FilterDict,\n  keyword: string\n): FilterDict {\n  const filtered: FilterDict = {};\n\n  for (const unified in emojis) {\n    const emoji = emojis[unified];\n\n    if (hasMatch(emoji, keyword)) {\n      filtered[unified] = emoji;\n    }\n  }\n\n  return filtered;\n}\n\nfunction hasMatch(emoji: DataEmoji, keyword: string): boolean {\n  return emojiNames(emoji).some(name => name.includes(keyword));\n}\n\nexport function useIsEmojiFiltered(): (unified: string) => boolean {\n  const { current: filter } = useFilterRef();\n  const [searchTerm] = useSearchTermState();\n\n  return unified => isEmojiFilteredBySearchTerm(unified, filter, searchTerm);\n}\n\nfunction isEmojiFilteredBySearchTerm(\n  unified: string,\n  filter: FilterState,\n  searchTerm: string\n): boolean {\n  if (!filter || !searchTerm) {\n    return false;\n  }\n\n  return !filter[searchTerm]?.[unified];\n}\n\nexport type FilterDict = Record<string, DataEmoji>;\n\nfunction findLongestMatch(\n  keyword: string,\n  dict: Record<string, FilterDict> | null\n): FilterDict | null {\n  if (!dict) {\n    return null;\n  }\n\n  if (dict[keyword]) {\n    return dict[keyword];\n  }\n\n  const longestMatchingKey = Object.keys(dict)\n    .sort((a, b) => b.length - a.length)\n    .find(key => keyword.includes(key));\n\n  if (longestMatchingKey) {\n    return dict[longestMatchingKey];\n  }\n\n  return null;\n}\n\nexport function getNormalizedSearchTerm(str: string): string {\n  if (!str || typeof str !== 'string') {\n    return '';\n  }\n\n  return str.trim().toLowerCase();\n}\n\nfunction getStatusSearchResults(\n  filterState: FilterState,\n  searchTerm: string\n): string {\n  if (!filterState?.[searchTerm]) return '';\n\n  const searchResultsCount =\n    Object.entries(filterState?.[searchTerm])?.length || 0;\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useSearchResultsConfig(searchResultsCount);\n}\n", "import { emojiFromElement, NullableElement } from '../DomUtils/selectors';\nimport { useSetAnchoredEmojiRef } from '../components/context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../components/context/PickerContext';\n\nexport default function useSetVariationPicker() {\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n\n  return function setVariationPicker(element: NullableElement) {\n    const [emoji] = emojiFromElement(element);\n\n    if (emoji) {\n      setAnchoredEmojiRef(element);\n      setEmojiVariationPicker(emoji);\n    }\n  };\n}\n", "import { useSkinTonePickerLocationConfig } from '../config/useConfig';\nimport { SkinTonePickerLocation } from '../types/exposedTypes';\n\nexport function useShouldShowSkinTonePicker() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return function shouldShowSkinTonePicker(location: SkinTonePickerLocation) {\n    return skinTonePickerLocationConfig === location;\n  };\n}\n\nexport function useIsSkinToneInSearch() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.SEARCH;\n}\n\nexport function useIsSkinToneInPreview() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.PREVIEW;\n}\n", "import { useCallback, useEffect, useMemo } from 'react';\n\nimport { hasNextElementSibling } from '../DomUtils/elementPositionInRow';\nimport {\n  focusNextElementSibling,\n  focusPrevElementSibling\n} from '../DomUtils/focusElement';\nimport { getActiveElement } from '../DomUtils/getActiveElement';\nimport {\n  focusAndClickFirstVisibleEmoji,\n  focusFirstVisibleEmoji,\n  focusNextVisibleEmoji,\n  focusPrevVisibleEmoji,\n  focusVisibleEmojiOneRowDown,\n  focusVisibleEmojiOneRowUp\n} from '../DomUtils/keyboardNavigation';\nimport { useScrollTo } from '../DomUtils/scrollTo';\nimport { buttonFromTarget } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  useCategoryNavigationRef,\n  usePickerMainRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\nimport { useSkinToneFanOpenState } from '../components/context/PickerContext';\nimport { useSearchDisabledConfig } from '../config/useConfig';\n\nimport {\n  useCloseAllOpenToggles,\n  useHasOpenToggles\n} from './useCloseAllOpenToggles';\nimport { useDisallowMouseMove } from './useDisallowMouseMove';\nimport { useAppendSearch, useClearSearch } from './useFilter';\nimport {\n  useFocusCategoryNavigation,\n  useFocusSearchInput,\n  useFocusSkinTonePicker\n} from './useFocus';\nimport useIsSearchMode from './useIsSearchMode';\nimport useSetVariationPicker from './useSetVariationPicker';\nimport {\n  useIsSkinToneInPreview,\n  useIsSkinToneInSearch\n} from './useShouldShowSkinTonePicker';\n\nenum KeyboardEvents {\n  ArrowDown = 'ArrowDown',\n  ArrowUp = 'ArrowUp',\n  ArrowLeft = 'ArrowLeft',\n  ArrowRight = 'ArrowRight',\n  Escape = 'Escape',\n  Enter = 'Enter',\n  Space = ' '\n}\n\nexport function useKeyboardNavigation() {\n  usePickerMainKeyboardEvents();\n  useSearchInputKeyboardEvents();\n  useSkinTonePickerKeyboardEvents();\n  useCategoryNavigationKeyboardEvents();\n  useBodyKeyboardEvents();\n}\n\nfunction usePickerMainKeyboardEvents() {\n  const PickerMainRef = usePickerMainRef();\n  const clearSearch = useClearSearch();\n  const scrollTo = useScrollTo();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n  const hasOpenToggles = useHasOpenToggles();\n  const disallowMouseMove = useDisallowMouseMove();\n\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        disallowMouseMove();\n        switch (key) {\n          // eslint-disable-next-line no-fallthrough\n          case KeyboardEvents.Escape:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              return;\n            }\n            clearSearch();\n            scrollTo(0);\n            focusSearchInput();\n            break;\n        }\n      },\n    [\n      scrollTo,\n      clearSearch,\n      closeAllOpenToggles,\n      focusSearchInput,\n      hasOpenToggles,\n      disallowMouseMove\n    ]\n  );\n\n  useEffect(() => {\n    const current = PickerMainRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, scrollTo, onKeyDown]);\n}\n\nfunction useSearchInputKeyboardEvents() {\n  const focusSkinTonePicker = useFocusSkinTonePicker();\n  const PickerMainRef = usePickerMainRef();\n  const BodyRef = useBodyRef();\n  const SearchInputRef = useSearchInputRef();\n  const [, setSkinToneFanOpenState] = useSkinToneFanOpenState();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            if (!isSkinToneInSearch) {\n              return;\n            }\n            event.preventDefault();\n            setSkinToneFanOpenState(true);\n            focusSkinTonePicker();\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            goDownFromSearchInput();\n            break;\n          case KeyboardEvents.Enter:\n            event.preventDefault();\n            focusAndClickFirstVisibleEmoji(BodyRef.current);\n            break;\n        }\n      },\n    [\n      focusSkinTonePicker,\n      goDownFromSearchInput,\n      setSkinToneFanOpenState,\n      BodyRef,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SearchInputRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, onKeyDown]);\n}\n\nfunction useSkinTonePickerKeyboardEvents() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const focusSearchInput = useFocusSearchInput();\n  const SearchInputRef = useSearchInputRef();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        if (isSkinToneInSearch) {\n          switch (key) {\n            case KeyboardEvents.ArrowLeft:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowRight:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (isOpen) {\n                setIsOpen(false);\n              }\n              goDownFromSearchInput();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n\n        if (isSkinToneInPreview) {\n          switch (key) {\n            case KeyboardEvents.ArrowUp:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n      },\n    [\n      isOpen,\n      focusSearchInput,\n      setIsOpen,\n      goDownFromSearchInput,\n      onType,\n      isSkinToneInPreview,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SkinTonePickerRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [SkinTonePickerRef, SearchInputRef, isOpen, onKeyDown]);\n}\n\nfunction useCategoryNavigationKeyboardEvents() {\n  const focusSearchInput = useFocusSearchInput();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const BodyRef = useBodyRef();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            focusSearchInput();\n            break;\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            focusFirstVisibleEmoji(BodyRef.current);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [BodyRef, focusSearchInput, onType]\n  );\n\n  useEffect(() => {\n    const current = CategoryNavigationRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [CategoryNavigationRef, BodyRef, onKeyDown]);\n}\n\nfunction useBodyKeyboardEvents() {\n  const BodyRef = useBodyRef();\n  const goUpFromBody = useGoUpFromBody();\n  const setVariationPicker = useSetVariationPicker();\n  const hasOpenToggles = useHasOpenToggles();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        const activeElement = buttonFromTarget(getActiveElement());\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowDown(activeElement);\n            break;\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowUp(activeElement, goUpFromBody);\n            break;\n          case KeyboardEvents.Space:\n            event.preventDefault();\n            setVariationPicker(event.target as HTMLElement);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [\n      goUpFromBody,\n      onType,\n      setVariationPicker,\n      hasOpenToggles,\n      closeAllOpenToggles\n    ]\n  );\n\n  useEffect(() => {\n    const current = BodyRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [BodyRef, onKeyDown]);\n}\n\nfunction useGoDownFromSearchInput() {\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    function goDownFromSearchInput() {\n      if (isSearchMode) {\n        return focusFirstVisibleEmoji(BodyRef.current);\n      }\n      return focusCategoryNavigation();\n    },\n    [BodyRef, focusCategoryNavigation, isSearchMode]\n  );\n}\n\nfunction useGoUpFromBody() {\n  const focusSearchInput = useFocusSearchInput();\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n\n  return useCallback(\n    function goUpFromEmoji() {\n      if (isSearchMode) {\n        return focusSearchInput();\n      }\n      return focusCategoryNavigation();\n    },\n    [focusSearchInput, isSearchMode, focusCategoryNavigation]\n  );\n}\n\nfunction focusNextSkinTone(exitLeft: () => void) {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  if (!hasNextElementSibling(currentSkinTone)) {\n    exitLeft();\n  }\n\n  focusNextElementSibling(currentSkinTone);\n}\n\nfunction focusPrevSkinTone() {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  focusPrevElementSibling(currentSkinTone);\n}\n\nfunction useOnType() {\n  const appendSearch = useAppendSearch();\n  const focusSearchInput = useFocusSearchInput();\n  const searchDisabled = useSearchDisabledConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  return function onType(event: KeyboardEvent) {\n    const { key } = event;\n\n    if (hasModifier(event) || searchDisabled) {\n      return;\n    }\n\n    if (key.match(/(^[a-zA-Z0-9]$){1}/)) {\n      event.preventDefault();\n      closeAllOpenToggles();\n      focusSearchInput();\n      appendSearch(key);\n    }\n  };\n}\n\nfunction hasModifier(event: KeyboardEvent): boolean {\n  const { metaKey, ctrlKey, altKey } = event;\n\n  return metaKey || ctrlKey || altKey;\n}\n", "import { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified, emojiVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nexport function preloadEmoji(\n  getEmojiUrl: GetEmojiUrl,\n  emoji: undefined | DataEmoji,\n  emojiStyle: EmojiStyle\n): void {\n  if (!emoji) {\n    return;\n  }\n\n  if (emojiStyle === EmojiStyle.NATIVE) {\n    return;\n  }\n\n  const unified = emojiUnified(emoji);\n\n  if (preloadedEmojs.has(unified)) {\n    return;\n  }\n\n  emojiVariations(emoji).forEach((variation) => {\n    const emojiUrl = getEmojiUrl(variation, emojiStyle);\n    preloadImage(emojiUrl);\n  });\n\n  preloadedEmojs.add(unified);\n}\n\nexport const preloadedEmojs: Set<string> = new Set();\n\nfunction preloadImage(url: string): void {\n  const image = new Image();\n  image.src = url;\n}\n", "import { useEffect } from 'react';\n\nimport { buttonFromTarget, emojiFromElement } from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useEmojiStyleConfig, useGetEmojiUrlConfig } from '../config/useConfig';\nimport { emojiHasVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nimport { preloadEmoji } from './preloadEmoji';\n\nexport function useOnFocus() {\n  const BodyRef = useBodyRef();\n  const emojiStyle = useEmojiStyleConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEffect(() => {\n    if (emojiStyle === EmojiStyle.NATIVE) {\n      return;\n    }\n\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('focusin', onFocus);\n\n    return () => {\n      bodyRef?.removeEventListener('focusin', onFocus);\n    };\n\n    function onFocus(event: FocusEvent) {\n      const button = buttonFromTarget(event.target as HTMLElement);\n\n      if (!button) {\n        return;\n      }\n\n      const [emoji] = emojiFromElement(button);\n\n      if (!emoji) {\n        return;\n      }\n\n      if (emojiHasVariations(emoji)) {\n        preloadEmoji(getEmojiUrl, emoji, emojiStyle);\n      }\n    }\n  }, [BodyRef, emojiStyle, getEmojiUrl]);\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useClassNameConfig,\n  useStyleConfig,\n  useThemeConfig\n} from '../../config/useConfig';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';\nimport { useOnFocus } from '../../hooks/useOnFocus';\nimport { Theme } from '../../types/exposedTypes';\nimport { usePickerMainRef } from '../context/ElementRefContext';\nimport {\n  PickerContextProvider,\n  useReactionsModeState\n} from '../context/PickerContext';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport const DEFAULT_LABEL_HEIGHT = 40;\n\nexport default function PickerMain({ children }: Props) {\n  return (\n    <PickerContextProvider>\n      <PickerRootElement>{children}</PickerRootElement>\n    </PickerContextProvider>\n  );\n}\n\ntype RootProps = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n  children: React.ReactNode;\n}>;\n\nfunction PickerRootElement({ children }: RootProps) {\n  const [reactionsMode] = useReactionsModeState();\n  const theme = useThemeConfig();\n  const searchModeActive = useIsSearchMode();\n  const PickerMainRef = usePickerMainRef();\n  const className = useClassNameConfig();\n  const style = useStyleConfig();\n\n  useKeyboardNavigation();\n  useOnFocus();\n\n  const { width, height, ...styleProps } = style || {};\n\n  return (\n    <aside\n      className={cx(\n        styles.main,\n        styles.baseVariables,\n        theme === Theme.DARK && styles.darkTheme,\n        theme === Theme.AUTO && styles.autoThemeDark,\n        {\n          [ClassNames.searchActive]: searchModeActive\n        },\n        reactionsMode && styles.reactionsMenu,\n        className\n      )}\n      ref={PickerMainRef}\n      style={{\n        ...styleProps,\n        ...(!reactionsMode && { height, width })\n      }}\n    >\n      {children}\n    </aside>\n  );\n}\n\nconst DarkTheme = {\n  '--epr-emoji-variation-picker-bg-color':\n    'var(--epr-dark-emoji-variation-picker-bg-color)',\n  '--epr-hover-bg-color-reduced-opacity':\n    'var(--epr-dark-hover-bg-color-reduced-opacity)',\n  '--epr-highlight-color': 'var(--epr-dark-highlight-color)',\n  '--epr-text-color': 'var(--epr-dark-text-color)',\n  '--epr-hover-bg-color': 'var(--epr-dark-hover-bg-color)',\n  '--epr-focus-bg-color': 'var(--epr-dark-focus-bg-color)',\n  '--epr-search-input-bg-color': 'var(--epr-dark-search-input-bg-color)',\n  '--epr-category-label-bg-color': 'var(--epr-dark-category-label-bg-color)',\n  '--epr-picker-border-color': 'var(--epr-dark-picker-border-color)',\n  '--epr-bg-color': 'var(--epr-dark-bg-color)',\n  '--epr-reactions-bg-color': 'var(--epr-dark-reactions-bg-color)',\n  '--epr-search-input-bg-color-active':\n    'var(--epr-dark-search-input-bg-color-active)',\n  '--epr-emoji-variation-indicator-color':\n    'var(--epr-dark-emoji-variation-indicator-color)',\n  '--epr-category-icon-active-color':\n    'var(--epr-dark-category-icon-active-color)',\n  '--epr-skin-tone-picker-menu-color':\n    'var(--epr-dark-skin-tone-picker-menu-color)',\n  '--epr-skin-tone-outer-border-color': 'var(--epr-dark-skin-tone-outer-border-color)',\n  '--epr-skin-tone-inner-border-color': 'var(--epr-dark-skin-tone-inner-border-color)'\n};\n\nconst styles = stylesheet.create({\n  main: {\n    '.': ['epr-main', ClassNames.emojiPicker],\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    borderWidth: '1px',\n    borderStyle: 'solid',\n    borderRadius: 'var(--epr-picker-border-radius)',\n    borderColor: 'var(--epr-picker-border-color)',\n    backgroundColor: 'var(--epr-bg-color)',\n    overflow: 'hidden',\n    transition: 'all 0.3s ease-in-out, background-color 0.1s ease-in-out',\n    '*': {\n      boxSizing: 'border-box',\n      fontFamily: 'sans-serif'\n    }\n  },\n  baseVariables: {\n    '--': {\n      '--epr-highlight-color': '#007aeb',\n      '--epr-hover-bg-color': '#e5f0fa',\n      '--epr-hover-bg-color-reduced-opacity': '#e5f0fa80',\n      '--epr-focus-bg-color': '#e0f0ff',\n      '--epr-text-color': '#858585',\n      '--epr-search-input-bg-color': '#f6f6f6',\n      '--epr-picker-border-color': '#e7e7e7',\n      '--epr-bg-color': '#fff',\n      '--epr-reactions-bg-color': '#ffffff90',\n      '--epr-category-icon-active-color': '#6aa8de',\n      '--epr-skin-tone-picker-menu-color': '#ffffff95',\n      '--epr-skin-tone-outer-border-color': '#555555',\n      '--epr-skin-tone-inner-border-color': 'var(--epr-bg-color)',\n\n      '--epr-horizontal-padding': '10px',\n\n      '--epr-picker-border-radius': '8px',\n\n      /* Header */\n      '--epr-search-border-color': 'var(--epr-highlight-color)',\n      '--epr-header-padding': '15px var(--epr-horizontal-padding)',\n\n      /* Skin Tone Picker */\n      '--epr-active-skin-tone-indicator-border-color':\n        'var(--epr-highlight-color)',\n      '--epr-active-skin-hover-color': 'var(--epr-hover-bg-color)',\n\n      /* Search */\n      '--epr-search-input-bg-color-active': 'var(--epr-search-input-bg-color)',\n      '--epr-search-input-padding': '0 30px',\n      '--epr-search-input-border-radius': '8px',\n      '--epr-search-input-height': '40px',\n      '--epr-search-input-text-color': 'var(--epr-text-color)',\n      '--epr-search-input-placeholder-color': 'var(--epr-text-color)',\n      '--epr-search-bar-inner-padding': 'var(--epr-horizontal-padding)',\n\n      /*  Category Navigation */\n      '--epr-category-navigation-button-size': '30px',\n\n      /* Variation Picker */\n      '--epr-emoji-variation-picker-height': '45px',\n      '--epr-emoji-variation-picker-bg-color': 'var(--epr-bg-color)',\n\n      /*  Preview */\n      '--epr-preview-height': '70px',\n      '--epr-preview-text-size': '14px',\n      '--epr-preview-text-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-preview-border-color': 'var(--epr-picker-border-color)',\n      '--epr-preview-text-color': 'var(--epr-text-color)',\n\n      /* Category */\n      '--epr-category-padding': '0 var(--epr-horizontal-padding)',\n\n      /*  Category Label */\n      '--epr-category-label-bg-color': '#ffffffe6',\n      '--epr-category-label-text-color': 'var(--epr-text-color)',\n      '--epr-category-label-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-category-label-height': `${DEFAULT_LABEL_HEIGHT}px`,\n\n      /*  Emoji */\n      '--epr-emoji-size': '30px',\n      '--epr-emoji-padding': '5px',\n      '--epr-emoji-fullsize':\n        'calc(var(--epr-emoji-size) + var(--epr-emoji-padding) * 2)',\n      '--epr-emoji-hover-color': 'var(--epr-hover-bg-color)',\n      '--epr-emoji-variation-indicator-color': 'var(--epr-picker-border-color)',\n      '--epr-emoji-variation-indicator-color-hover': 'var(--epr-text-color)',\n\n      /* Z-Index */\n      '--epr-header-overlay-z-index': '3',\n      '--epr-emoji-variations-indictator-z-index': '1',\n      '--epr-category-label-z-index': '2',\n      '--epr-skin-variation-picker-z-index': '5',\n      '--epr-preview-z-index': '6',\n\n      /* Dark Theme Variables */\n      '--epr-dark': '#000',\n      '--epr-dark-emoji-variation-picker-bg-color': 'var(--epr-dark)',\n      '--epr-dark-highlight-color': '#c0c0c0',\n      '--epr-dark-text-color': 'var(--epr-highlight-color)',\n      '--epr-dark-hover-bg-color': '#363636f6',\n      '--epr-dark-hover-bg-color-reduced-opacity': '#36363680',\n      '--epr-dark-focus-bg-color': '#474747',\n      '--epr-dark-search-input-bg-color': '#333333',\n      '--epr-dark-category-label-bg-color': '#222222e6',\n      '--epr-dark-picker-border-color': '#151617',\n      '--epr-dark-bg-color': '#222222',\n      '--epr-dark-reactions-bg-color': '#22222290',\n      '--epr-dark-search-input-bg-color-active': 'var(--epr-dark)',\n      '--epr-dark-emoji-variation-indicator-color': '#444',\n      '--epr-dark-category-icon-active-color': '#3271b7',\n      '--epr-dark-skin-tone-picker-menu-color': '#22222295',\n      '--epr-dark-skin-tone-outer-border-color': 'var(--epr-dark-picker-border-color)',\n      '--epr-dark-skin-tone-inner-border-color': '#00000000',\n    }\n  },\n  autoThemeDark: {\n    '.': ClassNames.autoTheme,\n    '@media (prefers-color-scheme: dark)': {\n      '--': DarkTheme\n    }\n  },\n  darkTheme: {\n    '.': ClassNames.darkTheme,\n    '--': DarkTheme\n  },\n  reactionsMenu: {\n    '.': 'epr-reactions',\n    height: '50px',\n    display: 'inline-flex',\n    backgroundColor: 'var(--epr-reactions-bg-color)',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(8px)',\n    '--': {\n      '--epr-picker-border-radius': '50px'\n    }\n  }\n});\n", "import { DEFAULT_LABEL_HEIGHT } from '../components/main/PickerMain';\n\nimport { ClassNames, asSelectors } from './classNames';\nimport { NullableElement } from './selectors';\n\nexport function elementCountInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const parentWidth = parent.getBoundingClientRect().width;\n  const elementWidth = element.getBoundingClientRect().width;\n  return Math.floor(parentWidth / elementWidth);\n}\n\nexport function elementIndexInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementWidth = element.getBoundingClientRect().width;\n  const elementLeft = element.getBoundingClientRect().left;\n  const parentLeft = parent.getBoundingClientRect().left;\n\n  return Math.floor((elementLeft - parentLeft) / elementWidth);\n}\n\nexport function rowNumber(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  return Math.round((elementTop - parentTop) / elementHeight);\n}\n\nexport function hasNextRow(\n  parent: NullableElement,\n  element: NullableElement\n): boolean {\n  if (!parent || !element) {\n    return false;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentHeight = parent.getBoundingClientRect().height;\n\n  return Math.round(elementTop - parentTop + elementHeight) < parentHeight;\n}\n\nfunction getRowElements(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number\n): HTMLElement[] {\n  if (row === -1) {\n    const lastRow = Math.floor((elements.length - 1) / elementsInRow);\n    const firstElementIndex = lastRow * elementsInRow;\n    const lastElementIndex = elements.length - 1;\n    return elements.slice(firstElementIndex, lastElementIndex + 1);\n  }\n\n  return elements.slice(row * elementsInRow, (row + 1) * elementsInRow);\n}\n\nfunction getNextRowElements(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number\n): HTMLElement[] {\n  const nextRow = currentRow + 1;\n\n  if (nextRow * elementsInRow > allElements.length) {\n    return [];\n  }\n\n  return getRowElements(allElements, nextRow, elementsInRow);\n}\n\nexport function getElementInRow(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number,\n  indexInRow: number\n): NullableElement {\n  const rowElements = getRowElements(elements, row, elementsInRow);\n  // get element, default to last\n  return rowElements[indexInRow] || rowElements[rowElements.length - 1] || null;\n}\n\nexport function getElementInNextRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const nextRowElements = getNextRowElements(\n    allElements,\n    currentRow,\n    elementsInRow\n  );\n\n  // return item in index, or last item in row\n  return (\n    nextRowElements[index] ||\n    nextRowElements[nextRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function getElementInPrevRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const prevRowElements = getRowElements(\n    allElements,\n    currentRow - 1,\n    elementsInRow\n  );\n\n  // default to last\n  return (\n    prevRowElements[index] ||\n    prevRowElements[prevRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function firstVisibleElementInContainer(\n  parent: NullableElement,\n  elements: HTMLElement[],\n  maxVisibilityDiffThreshold = 0\n): NullableElement {\n  if (!parent || !elements.length) {\n    return null;\n  }\n\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentBottom = parent.getBoundingClientRect().bottom;\n  const parentTopWithLabel = parentTop + getLabelHeight(parent);\n\n  const visibleElements = elements.find(element => {\n    const elementTop = element.getBoundingClientRect().top;\n    const elementBottom = element.getBoundingClientRect().bottom;\n    const maxVisibilityDiffPixels =\n      element.clientHeight * maxVisibilityDiffThreshold;\n\n    const elementTopWithAllowedDiff = elementTop + maxVisibilityDiffPixels;\n    const elementBottomWithAllowedDiff =\n      elementBottom - maxVisibilityDiffPixels;\n\n    if (elementTopWithAllowedDiff < parentTopWithLabel) {\n      return false;\n    }\n\n    return (\n      (elementTopWithAllowedDiff >= parentTop &&\n        elementTopWithAllowedDiff <= parentBottom) ||\n      (elementBottomWithAllowedDiff >= parentTop &&\n        elementBottomWithAllowedDiff <= parentBottom)\n    );\n  });\n\n  return visibleElements || null;\n}\n\nexport function hasNextElementSibling(element: HTMLElement) {\n  return !!element.nextElementSibling;\n}\n\nfunction getLabelHeight(parentNode: HTMLElement) {\n  const labels = Array.from(\n    parentNode.querySelectorAll(asSelectors(ClassNames.label))\n  );\n\n  for (const label of labels) {\n    const height = label.getBoundingClientRect().height;\n    // return height if label is not hidden\n    if (height > 0) {\n      return height;\n    }\n  }\n\n  return DEFAULT_LABEL_HEIGHT;\n}\n", "import { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  emojiByUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport { firstVisibleElementInContainer } from './elementPositionInRow';\n\nexport type NullableElement = HTMLElement | null;\n\nexport const EmojiButtonSelector = `button${asSelectors(ClassNames.emoji)}`;\nexport const VisibleEmojiSelector = [\n  EmojiButtonSelector,\n  asSelectors(ClassNames.visible),\n  `:not(${asSelectors(ClassNames.hidden)})`\n].join('');\n\nexport function buttonFromTarget(\n  emojiElement: NullableElement\n): HTMLButtonElement | null {\n  return emojiElement?.closest(EmojiButtonSelector) ?? null;\n}\n\nexport function isEmojiButton(element: NullableElement): boolean {\n  if (!element) {\n    return false;\n  }\n\n  return element.matches(EmojiButtonSelector);\n}\n\nexport function emojiFromElement(\n  element: NullableElement\n): [DataEmoji, string] | [] {\n  const originalUnified = originalUnifiedFromEmojiElement(element);\n  const unified = unifiedFromEmojiElement(element);\n\n  if (!originalUnified) {\n    return [];\n  }\n\n  const emoji = emojiByUnified(unified ?? originalUnified);\n\n  if (!emoji) {\n    return [];\n  }\n\n  return [emoji, unified as string];\n}\n\nexport function isEmojiElement(element: NullableElement): boolean {\n  return Boolean(\n    element?.matches(EmojiButtonSelector) ||\n      element?.parentElement?.matches(EmojiButtonSelector)\n  );\n}\n\nexport function categoryLabelFromCategory(\n  category: NullableElement\n): NullableElement {\n  return category?.querySelector(asSelectors(ClassNames.label)) ?? null;\n}\n\nexport function closestCategoryLabel(\n  element: NullableElement\n): NullableElement {\n  const category = closestCategory(element);\n  return categoryLabelFromCategory(category);\n}\n\nexport function elementHeight(element: NullableElement): number {\n  return element?.clientHeight ?? 0;\n}\n\nexport function emojiTrueOffsetTop(element: NullableElement): number {\n  if (!element) {\n    return 0;\n  }\n\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  // compensate for the label height\n  const labelHeight = categoryLabelHeight(category);\n\n  return elementOffsetTop(button) + elementOffsetTop(category) + labelHeight;\n}\n\nexport function categoryLabelHeight(category: NullableElement): number {\n  if (!category) {\n    return 0;\n  }\n\n  const categoryWithoutLabel = category.querySelector(\n    asSelectors(ClassNames.categoryContent)\n  );\n\n  return (\n    (category?.clientHeight ?? 0) - (categoryWithoutLabel?.clientHeight ?? 0)\n  );\n}\n\nexport function isEmojiBehindLabel(emoji: NullableElement): boolean {\n  if (!emoji) {\n    return false;\n  }\n\n  return (\n    emojiDistanceFromScrollTop(emoji) <\n    categoryLabelHeight(closestCategory(emoji))\n  );\n}\n\nexport function queryScrollBody(root: NullableElement): NullableElement {\n  if (!root) return null;\n\n  return root.matches(asSelectors(ClassNames.scrollBody))\n    ? root\n    : root.querySelector(asSelectors(ClassNames.scrollBody));\n}\n\nexport function emojiDistanceFromScrollTop(emoji: NullableElement): number {\n  if (!emoji) {\n    return 0;\n  }\n\n  return emojiTrueOffsetTop(emoji) - (closestScrollBody(emoji)?.scrollTop ?? 0);\n}\n\nexport function closestScrollBody(element: NullableElement): NullableElement {\n  if (!element) {\n    return null;\n  }\n\n  return element.closest(asSelectors(ClassNames.scrollBody)) ?? null;\n}\n\nexport function emojiTruOffsetLeft(element: NullableElement): number {\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  return elementOffsetLeft(button) + elementOffsetLeft(category);\n}\n\nfunction elementOffsetTop(element: NullableElement): number {\n  return element?.offsetTop ?? 0;\n}\n\nfunction elementOffsetLeft(element: NullableElement): number {\n  return element?.offsetLeft ?? 0;\n}\n\nexport function unifiedFromEmojiElement(emoji: NullableElement): string | null {\n  return elementDataSetKey(buttonFromTarget(emoji), 'unified') ?? null;\n}\n\nexport function originalUnifiedFromEmojiElement(\n  emoji: NullableElement\n): string | null {\n  const unified = unifiedFromEmojiElement(emoji);\n\n  if (unified) {\n    return unifiedWithoutSkinTone(unified);\n  }\n  return null;\n}\n\nexport function allUnifiedFromEmojiElement(\n  emoji: NullableElement\n): { unified: string | null; originalUnified: string | null } {\n  if (!emoji) {\n    return {\n      unified: null,\n      originalUnified: null\n    };\n  }\n\n  return {\n    unified: unifiedFromEmojiElement(emoji),\n    originalUnified: originalUnifiedFromEmojiElement(emoji)\n  };\n}\n\nfunction elementDataSetKey(\n  element: NullableElement,\n  key: string\n): string | null {\n  return elementDataSet(element)[key] ?? null;\n}\n\nfunction elementDataSet(element: NullableElement): DOMStringMap {\n  return element?.dataset ?? {};\n}\n\nexport function isVisibleEmoji(element: HTMLElement) {\n  return element.classList.contains(ClassNames.visible);\n}\n\nexport function isHidden(element: NullableElement) {\n  if (!element) return true;\n\n  return element.classList.contains(ClassNames.hidden);\n}\n\nexport function allVisibleEmojis(parent: NullableElement) {\n  if (!parent) {\n    return [];\n  }\n\n  return Array.from(\n    parent.querySelectorAll(VisibleEmojiSelector)\n  ) as HTMLElement[];\n}\n\nexport function lastVisibleEmoji(element: NullableElement): NullableElement {\n  if (!element) return null;\n\n  const allEmojis = allVisibleEmojis(element);\n  const [last] = allEmojis.slice(-1);\n  if (!last) {\n    return null;\n  }\n\n  if (!isVisibleEmoji(last)) {\n    return prevVisibleEmoji(last);\n  }\n\n  return last;\n}\n\nexport function nextVisibleEmoji(element: HTMLElement): NullableElement {\n  const next = element.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return firstVisibleEmoji(nextCategory(element));\n  }\n\n  if (!isVisibleEmoji(next)) {\n    return nextVisibleEmoji(next);\n  }\n\n  return next;\n}\n\nexport function prevVisibleEmoji(element: HTMLElement): NullableElement {\n  const prev = element.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return lastVisibleEmoji(prevCategory(element));\n  }\n\n  if (!isVisibleEmoji(prev)) {\n    return prevVisibleEmoji(prev);\n  }\n\n  return prev;\n}\n\nexport function firstVisibleEmoji(parent: NullableElement) {\n  if (!parent) {\n    return null;\n  }\n\n  const allEmojis = allVisibleEmojis(parent);\n\n  return firstVisibleElementInContainer(parent, allEmojis, 0.1);\n}\n\nexport function prevCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const prev = category.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return null;\n  }\n\n  if (isHidden(prev)) {\n    return prevCategory(prev);\n  }\n\n  return prev;\n}\n\nexport function nextCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const next = category.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return null;\n  }\n\n  if (isHidden(next)) {\n    return nextCategory(next);\n  }\n\n  return next;\n}\n\nexport function closestCategory(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(asSelectors(ClassNames.category)) as HTMLElement;\n}\n\nexport function closestCategoryContent(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(\n    asSelectors(ClassNames.categoryContent)\n  ) as HTMLElement;\n}\n", "export function parseNativeEmoji(unified: string): string {\n  return unified\n    .split('-')\n    .map(hex => String.fromCodePoint(parseInt(hex, 16)))\n    .join('');\n}\n", "import { SkinTones, SuggestionMode } from '../types/exposedTypes';\n\nimport { DataEmoji } from './DataTypes';\nimport { emojiUnified } from './emojiSelectors';\n\nconst SUGGESTED_LS_KEY = 'epr_suggested';\n\ntype SuggestedItem = {\n  unified: string;\n  original: string;\n  count: number;\n};\n\ntype Suggested = SuggestedItem[];\n\nexport function getSuggested(mode?: SuggestionMode): Suggested {\n  try {\n    if (!window?.localStorage) {\n      return [];\n    }\n    const recent = JSON.parse(\n      window?.localStorage.getItem(SUGGESTED_LS_KEY) ?? '[]'\n    ) as Suggested;\n\n    if (mode === SuggestionMode.FREQUENT) {\n      return recent.sort((a, b) => b.count - a.count);\n    }\n\n    return recent;\n  } catch {\n    return [];\n  }\n}\n\nexport function setSuggested(emoji: DataEmoji, skinTone: SkinTones) {\n  const recent = getSuggested();\n\n  const unified = emojiUnified(emoji, skinTone);\n  const originalUnified = emojiUnified(emoji);\n\n  let existing = recent.find(({ unified: u }) => u === unified);\n\n  let nextList: SuggestedItem[];\n\n  if (existing) {\n    nextList = [existing].concat(recent.filter(i => i !== existing));\n  } else {\n    existing = {\n      unified,\n      original: originalUnified,\n      count: 0\n    };\n    nextList = [existing, ...recent];\n  }\n\n  existing.count++;\n\n  nextList.length = Math.min(nextList.length, 14);\n\n  try {\n    window?.localStorage.setItem(SUGGESTED_LS_KEY, JSON.stringify(nextList));\n    // Prevents the change from being seen immediately.\n  } catch {\n    // ignore\n  }\n}\n", "import {\n  Categories,\n  CategoryConfig,\n  CustomCategoryConfig\n} from '../config/categoryConfig';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\n\nexport function isCustomCategory(\n  category: CategoryConfig | CustomCategoryConfig\n): category is CustomCategoryConfig {\n  return category.category === Categories.CUSTOM;\n}\n\nexport function isCustomEmoji(emoji: Partial<DataEmoji>): emoji is CustomEmoji {\n  return emoji.imgUrl !== undefined;\n}\n", "import * as React from 'react';\nimport { useEffect, useRef } from 'react';\n\nimport {\n  emojiFromElement,\n  isEmojiElement,\n  NullableElement\n} from '../DomUtils/selectors';\nimport {\n  useActiveSkinToneState,\n  useDisallowClickRef,\n  useEmojiVariationPickerState,\n  useUpdateSuggested\n} from '../components/context/PickerContext';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useOnEmojiClickConfig\n} from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  activeVariationFromUnified,\n  emojiHasVariations,\n  emojiNames,\n  emojiUnified\n} from '../dataUtils/emojiSelectors';\nimport { parseNativeEmoji } from '../dataUtils/parseNativeEmoji';\nimport { setSuggested } from '../dataUtils/suggested';\nimport { isCustomEmoji } from '../typeRefinements/typeRefinements';\nimport { EmojiClickData, SkinTones, EmojiStyle } from '../types/exposedTypes';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\nimport useSetVariationPicker from './useSetVariationPicker';\n\nexport function useMouseDownHandlers(\n  ContainerRef: React.MutableRefObject<NullableElement>,\n  mouseEventSource: MOUSE_EVENT_SOURCE\n) {\n  const mouseDownTimerRef = useRef<undefined | number>();\n  const setVariationPicker = useSetVariationPicker();\n  const disallowClickRef = useDisallowClickRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const onEmojiClick = useOnEmojiClickConfig(mouseEventSource);\n  const [, updateSuggested] = useUpdateSuggested();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const activeEmojiStyle = useEmojiStyleConfig();\n\n  const onClick = React.useCallback(\n    function onClick(event: MouseEvent) {\n      if (disallowClickRef.current) {\n        return;\n      }\n\n      closeAllOpenToggles();\n\n      const [emoji, unified] = emojiFromEvent(event);\n\n      if (!emoji || !unified) {\n        return;\n      }\n\n      const skinToneToUse =\n        activeVariationFromUnified(unified) || activeSkinTone;\n\n      updateSuggested();\n      setSuggested(emoji, skinToneToUse);\n      onEmojiClick(\n        emojiClickOutput(emoji, skinToneToUse, activeEmojiStyle, getEmojiUrl),\n        event\n      );\n    },\n    [\n      activeSkinTone,\n      closeAllOpenToggles,\n      disallowClickRef,\n      onEmojiClick,\n      updateSuggested,\n      getEmojiUrl,\n      activeEmojiStyle\n    ]\n  );\n\n  const onMouseDown = React.useCallback(\n    function onMouseDown(event: MouseEvent) {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n      }\n\n      const [emoji] = emojiFromEvent(event);\n\n      if (!emoji || !emojiHasVariations(emoji)) {\n        return;\n      }\n\n      mouseDownTimerRef.current = window?.setTimeout(() => {\n        disallowClickRef.current = true;\n        mouseDownTimerRef.current = undefined;\n        closeAllOpenToggles();\n        setVariationPicker(event.target as HTMLElement);\n        setEmojiVariationPicker(emoji);\n      }, 500);\n    },\n    [\n      disallowClickRef,\n      closeAllOpenToggles,\n      setVariationPicker,\n      setEmojiVariationPicker\n    ]\n  );\n  const onMouseUp = React.useCallback(\n    function onMouseUp() {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n        mouseDownTimerRef.current = undefined;\n      } else if (disallowClickRef.current) {\n        // The problem we're trying to overcome here\n        // is that the emoji has both mouseup and click events\n        // and when releasing a mouseup event\n        // the click gets triggered too\n        // So we're disallowing the click event for a short time\n\n        requestAnimationFrame(() => {\n          disallowClickRef.current = false;\n        });\n      }\n    },\n    [disallowClickRef]\n  );\n\n  useEffect(() => {\n    if (!ContainerRef.current) {\n      return;\n    }\n    const confainerRef = ContainerRef.current;\n    confainerRef.addEventListener('click', onClick, {\n      passive: true\n    });\n\n    confainerRef.addEventListener('mousedown', onMouseDown, {\n      passive: true\n    });\n    confainerRef.addEventListener('mouseup', onMouseUp, {\n      passive: true\n    });\n\n    return () => {\n      confainerRef?.removeEventListener('click', onClick);\n      confainerRef?.removeEventListener('mousedown', onMouseDown);\n      confainerRef?.removeEventListener('mouseup', onMouseUp);\n    };\n  }, [ContainerRef, onClick, onMouseDown, onMouseUp]);\n}\n\nfunction emojiFromEvent(event: MouseEvent): [DataEmoji, string] | [] {\n  const target = event?.target as HTMLElement;\n  if (!isEmojiElement(target)) {\n    return [];\n  }\n\n  return emojiFromElement(target);\n}\n\nfunction emojiClickOutput(\n  emoji: DataEmoji,\n  activeSkinTone: SkinTones,\n  activeEmojiStyle: EmojiStyle,\n  getEmojiUrl: GetEmojiUrl\n): EmojiClickData {\n  const names = emojiNames(emoji);\n\n  if (isCustomEmoji(emoji)) {\n    const unified = emojiUnified(emoji);\n    return {\n      activeSkinTone,\n      emoji: unified,\n      getImageUrl() {\n        return emoji.imgUrl;\n      },\n      imageUrl: emoji.imgUrl,\n      isCustom: true,\n      names,\n      unified,\n      unifiedWithoutSkinTone: unified\n    };\n  }\n  const unified = emojiUnified(emoji, activeSkinTone);\n\n  return {\n    activeSkinTone,\n    emoji: parseNativeEmoji(unified),\n    getImageUrl(emojiStyle: EmojiStyle = activeEmojiStyle ?? EmojiStyle.APPLE) {\n      return getEmojiUrl(unified, emojiStyle);\n    },\n    imageUrl: getEmojiUrl(unified, activeEmojiStyle ?? EmojiStyle.APPLE),\n    isCustom: false,\n    names,\n    unified,\n    unifiedWithoutSkinTone: emojiUnified(emoji)\n  };\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\ninterface Props\n  extends React.DetailedHTMLProps<\n    React.ButtonHTMLAttributes<HTMLButtonElement>,\n    HTMLButtonElement\n  > {\n  className?: string;\n}\n\nexport function Button(props: Props) {\n  return (\n    <button\n      type=\"button\"\n      {...props}\n      className={cx(styles.button, props.className)}\n    >\n      {props.children}\n    </button>\n  );\n}\n\nconst styles = stylesheet.create({\n  button: {\n    '.': 'epr-btn',\n    cursor: 'pointer',\n    border: '0',\n    background: 'none',\n    outline: 'none'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\n\ntype ClickableEmojiButtonProps = Readonly<{\n  hidden?: boolean;\n  showVariations?: boolean;\n  hiddenOnSearch?: boolean;\n  emojiNames: string[];\n  children: React.ReactNode;\n  hasVariations: boolean;\n  unified?: string;\n  noBackground?: boolean;\n  className?: string;\n}>;\n\nexport function ClickableEmojiButton({\n  emojiNames,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  showVariations = true,\n  hasVariations,\n  children,\n  className,\n  noBackground = false\n}: ClickableEmojiButtonProps) {\n  return (\n    <Button\n      className={cx(\n        styles.emoji,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch,\n        {\n          [ClassNames.visible]: !hidden && !hiddenOnSearch\n        },\n        !!(hasVariations && showVariations) && styles.hasVariations,\n        noBackground && styles.noBackground,\n        className\n      )}\n      data-unified={unified}\n      aria-label={getAriaLabel(emojiNames)}\n      data-full-name={emojiNames}\n    >\n      {children}\n    </Button>\n  );\n}\n\nfunction getAriaLabel(emojiNames: string[]) {\n  return emojiNames[0].match('flag-')\n    ? emojiNames[1] ?? emojiNames[0]\n    : emojiNames[0];\n}\n\nconst styles = stylesheet.create({\n  emoji: {\n    '.': ClassNames.emoji,\n    position: 'relative',\n    width: 'var(--epr-emoji-fullsize)',\n    height: 'var(--epr-emoji-fullsize)',\n    boxSizing: 'border-box',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    borderRadius: '8px',\n    overflow: 'hidden',\n    transition: 'background-color 0.2s',\n    ':hover': {\n      backgroundColor: 'var(--epr-emoji-hover-color)'\n    },\n    ':focus': {\n      backgroundColor: 'var(--epr-focus-bg-color)'\n    }\n  },\n  noBackground: {\n    background: 'none',\n    ':hover': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    },\n    ':focus': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    }\n  },\n  hasVariations: {\n    '.': ClassNames.emojiHasVariations,\n    ':after': {\n      content: '',\n      display: 'block',\n      width: '0',\n      height: '0',\n      right: '0px',\n      bottom: '1px',\n      position: 'absolute',\n      borderLeft: '4px solid transparent',\n      borderRight: '4px solid transparent',\n      transform: 'rotate(135deg)',\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color)',\n      zIndex: 'var(--epr-emoji-variations-indictator-z-index)'\n    },\n    ':hover:after': {\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color-hover)'\n    }\n  }\n});\n", "import { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport const emojiStyles = stylesheet.create({\n  external: {\n    '.': ClassNames.external,\n    fontSize: '0'\n  },\n  common: {\n    alignSelf: 'center',\n    justifySelf: 'center',\n    display: 'block'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function EmojiImg({\n  emojiName,\n  style,\n  lazyLoad = false,\n  imgUrl,\n  onError,\n  className\n}: {\n  emojiName: string;\n  emojiStyle: EmojiStyle;\n  style: React.CSSProperties;\n  lazyLoad?: boolean;\n  imgUrl: string;\n    onError: () => void;\n  className?: string;\n}) {\n  return (\n    <img\n      src={imgUrl}\n      alt={emojiName}\n      className={cx(styles.emojiImag, emojiStyles.external, emojiStyles.common, className)}\n      loading={lazyLoad ? 'lazy' : 'eager'}\n      onError={onError}\n      style={style}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiImag: {\n    '.': 'epr-emoji-img',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    minWidth: 'var(--epr-emoji-fullsize)',\n    minHeight: 'var(--epr-emoji-fullsize)',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { parseNativeEmoji } from '../../dataUtils/parseNativeEmoji';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function NativeEmoji({\n  unified,\n  style,\n  className\n}: {\n  unified: string;\n  style: React.CSSProperties;\n  className?: string;\n}) {\n  return (\n    <span\n      className={cx(\n        styles.nativeEmoji,\n        emojiStyles.common,\n        emojiStyles.external,\n        className\n      )}\n      data-unified={unified}\n      style={style}\n    >\n      {parseNativeEmoji(unified)}\n    </span>\n  );\n}\n\nconst styles = stylesheet.create({\n  nativeEmoji: {\n    '.': 'epr-emoji-native',\n    fontFamily:\n      '\"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\"!important',\n    position: 'relative',\n    lineHeight: '100%',\n    fontSize: 'var(--epr-emoji-size)',\n    textAlign: 'center',\n    alignSelf: 'center',\n    justifySelf: 'center',\n    letterSpacing: '0',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import * as React from 'react';\n\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUrlByUnified\n} from '../../dataUtils/emojiSelectors';\nimport { isCustomEmoji } from '../../typeRefinements/typeRefinements';\nimport { EmojiStyle } from '../../types/exposedTypes';\nimport { useEmojisThatFailedToLoadState } from '../context/PickerContext';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { EmojiImg } from './EmojiImg';\nimport { NativeEmoji } from './NativeEmoji';\n\nexport function ViewOnlyEmoji({\n  emoji,\n  unified,\n  emojiStyle,\n  size,\n  lazyLoad,\n  getEmojiUrl = emojiUrlByUnified,\n  className\n}: BaseEmojiProps) {\n  const [, setEmojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n\n  const style = {} as React.CSSProperties;\n  if (size) {\n    style.width = style.height = style.fontSize = `${size}px`;\n  }\n\n  const emojiToRender = emoji ? emoji : emojiByUnified(unified);\n\n  if (!emojiToRender) {\n    return null;\n  }\n\n  if (isCustomEmoji(emojiToRender)) {\n    return (\n      <EmojiImg\n        style={style}\n        emojiName={unified}\n        emojiStyle={EmojiStyle.NATIVE}\n        lazyLoad={lazyLoad}\n        imgUrl={emojiToRender.imgUrl}\n        onError={onError}\n        className={className}\n      />\n    );\n  }\n\n  return (\n    <>\n      {emojiStyle === EmojiStyle.NATIVE ? (\n        <NativeEmoji unified={unified} style={style} className={className} />\n      ) : (\n        <EmojiImg\n          style={style}\n          emojiName={emojiName(emojiToRender)}\n          emojiStyle={emojiStyle}\n          lazyLoad={lazyLoad}\n          imgUrl={getEmojiUrl(unified, emojiStyle)}\n          onError={onError}\n          className={className}\n        />\n      )}\n    </>\n  );\n\n  function onError() {\n    setEmojisThatFailedToLoad(prev => new Set(prev).add(unified));\n  }\n}\n", "import * as React from 'react';\n\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiHasVariations, emojiNames } from '../../dataUtils/emojiSelectors';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { ClickableEmojiButton } from './ClickableEmojiButton';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\ntype ClickableEmojiProps = Readonly<\n  BaseEmojiProps & {\n    hidden?: boolean;\n    showVariations?: boolean;\n    hiddenOnSearch?: boolean;\n    emoji: DataEmoji;\n    className?: string;\n    noBackground?: boolean;\n  }\n>;\n\nexport function ClickableEmoji({\n  emoji,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  emojiStyle,\n  showVariations = true,\n  size,\n  lazyLoad,\n  getEmojiUrl,\n  className,\n  noBackground = false\n}: ClickableEmojiProps) {\n  const hasVariations = emojiHasVariations(emoji);\n\n  return (\n    <ClickableEmojiButton\n      hasVariations={hasVariations}\n      showVariations={showVariations}\n      hidden={hidden}\n      hiddenOnSearch={hiddenOnSearch}\n      emojiNames={emojiNames(emoji)}\n      unified={unified}\n      noBackground={noBackground}\n    >\n      <ViewOnlyEmoji\n        unified={unified}\n        emoji={emoji}\n        size={size}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoad}\n        getEmojiUrl={getEmojiUrl}\n        className={className}\n      />\n    </ClickableEmojiButton>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\nimport { useReactionsModeState } from '../context/PickerContext';\n\nimport Plus from './svg/plus.svg';\n\nexport function BtnPlus() {\n  const [, setReactionsMode] = useReactionsModeState();\n  return (\n    <Button\n      aria-label=\"Show all Emojis\"\n      title=\"Show all Emojis\"\n      tabIndex={0}\n      className={cx(styles.plusSign)}\n      onClick={() => setReactionsMode(false)}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  plusSign: {\n    fontSize: '20px',\n    padding: '17px',\n    color: 'var(--epr-text-color)',\n    borderRadius: '50%',\n    textAlign: 'center',\n    lineHeight: '100%',\n    width: '20px',\n    height: '20px',\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    transition: 'background-color 0.2s ease-in-out',\n    ':after': {\n      content: '',\n      minWidth: '20px',\n      minHeight: '20px',\n      backgroundImage: `url(${Plus})`,\n      backgroundColor: 'transparent',\n      backgroundRepeat: 'no-repeat',\n      backgroundSize: '20px',\n      backgroundPositionY: '0'\n    },\n    ':hover': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-20px'\n      }\n    },\n    ':focus': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-40px'\n      }\n    }\n  },\n  ...darkMode('plusSign', {\n    ':after': { backgroundPositionY: '-40px' },\n    ':hover:after': { backgroundPositionY: '-60px' }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonStyles, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useReactionsConfig,\n  useAllowExpandReactions,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useReactionsRef } from '../context/ElementRefContext';\nimport { useReactionsModeState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { BtnPlus } from './BtnPlus';\n\nexport function Reactions() {\n  const [reactionsOpen] = useReactionsModeState();\n  const ReactionsRef = useReactionsRef();\n  const reactions = useReactionsConfig();\n  useMouseDownHandlers(ReactionsRef, MOUSE_EVENT_SOURCE.REACTIONS);\n  const emojiStyle = useEmojiStyleConfig();\n  const allowExpandReactions = useAllowExpandReactions();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  if (!reactionsOpen) {\n    return null;\n  }\n\n  return (\n    <ul\n      className={cx(styles.list, !reactionsOpen && commonStyles.hidden)}\n      ref={ReactionsRef}\n    >\n      {reactions.map(reaction => (\n        <li key={reaction}>\n          <ClickableEmoji\n            emoji={emojiByUnified(reaction) as DataEmoji}\n            emojiStyle={emojiStyle}\n            unified={reaction}\n            showVariations={false}\n            className={cx(styles.emojiButton)}\n            noBackground\n            getEmojiUrl={getEmojiUrl}\n          />\n        </li>\n      ))}\n      {allowExpandReactions ? (\n        <li>\n          <BtnPlus />\n        </li>\n      ) : null}\n    </ul>\n  );\n}\n\nconst styles = stylesheet.create({\n  list: {\n    listStyle: 'none',\n    margin: '0',\n    padding: '0 5px',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    height: '100%'\n  },\n  emojiButton: {\n    ':hover': {\n      transform: 'scale(1.2)'\n    },\n    ':focus': {\n      transform: 'scale(1.2)'\n    },\n    ':active': {\n      transform: 'scale(1.1)'\n    },\n    transition: 'transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.5)'\n  }\n});\n", "import { useEffect } from 'react';\n\nimport { ElementRef } from '../components/context/ElementRefContext';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\n\nexport function useOnScroll(BodyRef: ElementRef) {\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    if (!bodyRef) {\n      return;\n    }\n\n    bodyRef.addEventListener('scroll', onScroll, {\n      passive: true\n    });\n\n    function onScroll() {\n      closeAllOpenToggles();\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('scroll', onScroll);\n    };\n  }, [BodyRef, closeAllOpenToggles]);\n}\n", "import { useEmojisThatFailedToLoadState } from '../components/context/PickerContext';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified } from '../dataUtils/emojiSelectors';\n\nimport { useIsEmojiFiltered } from './useFilter';\n\nexport function useIsEmojiHidden(): (emoji: DataEmoji) => IsHiddenReturn {\n  const [emojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n  const isEmojiFiltered = useIsEmojiFiltered();\n\n  return (emoji: DataEmoji): IsHiddenReturn => {\n    const unified = emojiUnified(emoji);\n\n    const failedToLoad = emojisThatFailedToLoad.has(unified);\n    const filteredOut = isEmojiFiltered(unified);\n\n    return {\n      failedToLoad,\n      filteredOut,\n      hidden: failedToLoad || filteredOut\n    };\n  };\n}\n\ntype IsHiddenReturn = {\n  failedToLoad: boolean;\n  filteredOut: boolean;\n  hidden: boolean;\n};\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryFromCategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n  children?: React.ReactNode;\n  hidden?: boolean;\n  hiddenOnSearch?: boolean;\n}>;\n\nexport function EmojiCategory({\n  categoryConfig,\n  children,\n  hidden,\n  hiddenOnSearch\n}: Props) {\n  const category = categoryFromCategoryConfig(categoryConfig);\n  const categoryName = categoryNameFromCategoryConfig(categoryConfig);\n\n  return (\n    <li\n      className={cx(\n        styles.category,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch\n      )}\n      data-name={category}\n      aria-label={categoryName}\n    >\n      <h2 className={cx(styles.label)}>{categoryName}</h2>\n      <div className={cx(styles.categoryContent)}>{children}</div>\n    </li>\n  );\n}\n\nconst styles = stylesheet.create({\n  category: {\n    '.': ClassNames.category,\n    ':not(:has(.epr-visible))': {\n      display: 'none'\n    }\n  },\n  categoryContent: {\n    '.': ClassNames.categoryContent,\n    display: 'grid',\n    gridGap: '0',\n    gridTemplateColumns: 'repeat(auto-fill, var(--epr-emoji-fullsize))',\n    justifyContent: 'space-between',\n    margin: 'var(--epr-category-padding)',\n    position: 'relative'\n  },\n  label: {\n    '.': ClassNames.label,\n    alignItems: 'center',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(3px)',\n    backgroundColor: 'var(--epr-category-label-bg-color)',\n    color: 'var(--epr-category-label-text-color)',\n    display: 'flex',\n    fontSize: '16px',\n    fontWeight: 'bold',\n    height: 'var(--epr-category-label-height)',\n    margin: '0',\n    padding: 'var(--epr-category-label-padding)',\n    position: 'sticky',\n    textTransform: 'capitalize',\n    top: '0',\n    width: '100%',\n    zIndex: 'var(--epr-category-label-z-index)'\n  }\n});\n", "import * as React from 'react';\n\nlet isEverMounted = false;\n\nexport function useIsEverMounted() {\n  const [isMounted, setIsMounted] = React.useState(isEverMounted);\n\n  React.useEffect(() => {\n    setIsMounted(true);\n    isEverMounted = true;\n  }, []);\n\n  return isMounted || isEverMounted;\n}\n", "import * as React from 'react';\n\nimport { CategoryConfig } from '../../config/categoryConfig';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useSuggestedEmojisModeConfig\n} from '../../config/useConfig';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { getSuggested } from '../../dataUtils/suggested';\nimport { useIsEmojiDisallowed } from '../../hooks/useDisallowedEmojis';\nimport { useIsEverMounted } from '../../hooks/useIsEverMounted';\nimport { useUpdateSuggested } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n}>;\n\nexport function Suggested({ categoryConfig }: Props) {\n  const [suggestedUpdated] = useUpdateSuggested();\n  const isMounted = useIsEverMounted();\n  const suggestedEmojisModeConfig = useSuggestedEmojisModeConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const suggested = React.useMemo(\n    () => getSuggested(suggestedEmojisModeConfig) ?? [],\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [suggestedUpdated, suggestedEmojisModeConfig]\n  );\n  const emojiStyle = useEmojiStyleConfig();\n  const isEmojiDisallowed = useIsEmojiDisallowed();\n\n  if (!isMounted) {\n    return null;\n  }\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      hiddenOnSearch\n      hidden={suggested.length === 0}\n    >\n      {suggested.map(suggestedItem => {\n        const emoji = emojiByUnified(suggestedItem.original);\n\n        if (!emoji) {\n          return null;\n        }\n\n        if (isEmojiDisallowed(emoji)) {\n          return null;\n        }\n\n        return (\n          <ClickableEmoji\n            showVariations={false}\n            unified={suggestedItem.unified}\n            emojiStyle={emojiStyle}\n            emoji={emoji}\n            key={suggestedItem.unified}\n            getEmojiUrl={getEmojiUrl}\n          />\n        );\n      })}\n    </EmojiCategory>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  Categories,\n  CategoryConfig,\n  categoryFromCategoryConfig\n} from '../../config/categoryConfig';\nimport {\n  useCategoriesConfig,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useLazyLoadEmojisConfig,\n  useSkinTonesDisabledConfig\n} from '../../config/useConfig';\nimport { emojisByCategory, emojiUnified } from '../../dataUtils/emojiSelectors';\nimport { useIsEmojiDisallowed } from '../../hooks/useDisallowedEmojis';\nimport { useIsEmojiHidden } from '../../hooks/useIsEmojiHidden';\nimport {\n  useActiveSkinToneState,\n  useIsPastInitialLoad\n} from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\nimport { Suggested } from './Suggested';\n\nexport function EmojiList() {\n  const categories = useCategoriesConfig();\n  const renderdCategoriesCountRef = React.useRef(0);\n\n  return (\n    <ul className={cx(styles.emojiList)}>\n      {categories.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n\n        if (category === Categories.SUGGESTED) {\n          return <Suggested key={category} categoryConfig={categoryConfig} />;\n        }\n\n        return (\n          <React.Suspense key={category}>\n            <RenderCategory\n              category={category}\n              categoryConfig={categoryConfig}\n              renderdCategoriesCountRef={renderdCategoriesCountRef}\n            />\n          </React.Suspense>\n        );\n      })}\n    </ul>\n  );\n}\n\nfunction RenderCategory({\n  category,\n  categoryConfig,\n  renderdCategoriesCountRef\n}: {\n  category: Categories;\n  categoryConfig: CategoryConfig;\n  renderdCategoriesCountRef: React.MutableRefObject<number>;\n}) {\n  const isEmojiHidden = useIsEmojiHidden();\n  const lazyLoadEmojis = useLazyLoadEmojisConfig();\n  const emojiStyle = useEmojiStyleConfig();\n  const isPastInitialLoad = useIsPastInitialLoad();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const isEmojiDisallowed = useIsEmojiDisallowed();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const showVariations = !useSkinTonesDisabledConfig();\n\n  // Small trick to defer the rendering of all emoji categories until the first category is visible\n  // This way the user gets to actually see something and not wait for the whole picker to render.\n  const emojisToPush =\n    !isPastInitialLoad && renderdCategoriesCountRef.current > 0\n      ? []\n      : emojisByCategory(category);\n\n  if (emojisToPush.length > 0) {\n    renderdCategoriesCountRef.current++;\n  }\n\n  let hiddenCounter = 0;\n\n  const emojis = emojisToPush.map(emoji => {\n    const unified = emojiUnified(emoji, activeSkinTone);\n    const { failedToLoad, filteredOut, hidden } = isEmojiHidden(emoji);\n\n    const isDisallowed = isEmojiDisallowed(emoji);\n\n    if (hidden || isDisallowed) {\n      hiddenCounter++;\n    }\n\n    if (isDisallowed) {\n      return null;\n    }\n\n    return (\n      <ClickableEmoji\n        showVariations={showVariations}\n        key={unified}\n        emoji={emoji}\n        unified={unified}\n        hidden={failedToLoad}\n        hiddenOnSearch={filteredOut}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoadEmojis}\n        getEmojiUrl={getEmojiUrl}\n      />\n    );\n  });\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      // Indicates that there are no visible emojis\n      // Hence, the category should be hidden\n      hidden={hiddenCounter === emojis.length}\n    >\n      {emojis}\n    </EmojiCategory>\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiList: {\n    '.': ClassNames.emojiList,\n    listStyle: 'none',\n    margin: '0',\n    padding: '0'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { focusFirstVisibleEmoji } from '../../DomUtils/keyboardNavigation';\nimport {\n  buttonFromTarget,\n  elementHeight,\n  emojiTrueOffsetTop,\n  emojiTruOffsetLeft\n} from '../../DomUtils/selectors';\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport {\n  emojiHasVariations,\n  emojiUnified,\n  emojiVariations\n} from '../../dataUtils/emojiSelectors';\nimport {\n  useAnchoredEmojiRef,\n  useBodyRef,\n  useSetAnchoredEmojiRef,\n  useVariationPickerRef\n} from '../context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport SVGTriangle from './svg/triangle.svg';\n\nenum Direction {\n  Up,\n  Down\n}\n\n// eslint-disable-next-line complexity\nexport function EmojiVariationPicker() {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const VariationPickerRef = useVariationPickerRef();\n  const [emoji] = useEmojiVariationPickerState();\n  const emojiStyle = useEmojiStyleConfig();\n\n  const { getTop, getMenuDirection } = useVariationPickerTop(\n    VariationPickerRef\n  );\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const getPointerStyle = usePointerStyle(VariationPickerRef);\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n  const visible = Boolean(\n    emoji &&\n      button &&\n      emojiHasVariations(emoji) &&\n      button.classList.contains(ClassNames.emojiHasVariations)\n  );\n\n  useEffect(() => {\n    if (!visible) {\n      return;\n    }\n\n    focusFirstVisibleEmoji(VariationPickerRef.current);\n  }, [VariationPickerRef, visible, AnchoredEmojiRef]);\n\n  let top, pointerStyle;\n\n  if (!visible && AnchoredEmojiRef.current) {\n    setAnchoredEmojiRef(null);\n  } else {\n    top = getTop();\n    pointerStyle = getPointerStyle();\n  }\n\n  return (\n    <div\n      ref={VariationPickerRef}\n      className={cx(\n        styles.variationPicker,\n        getMenuDirection() === Direction.Down && styles.pointingUp,\n        visible && styles.visible\n      )}\n      style={{ top }}\n    >\n      {visible && emoji\n        ? [emojiUnified(emoji)]\n            .concat(emojiVariations(emoji))\n            .slice(0, 6)\n            .map(unified => (\n              <ClickableEmoji\n                key={unified}\n                emoji={emoji}\n                unified={unified}\n                emojiStyle={emojiStyle}\n                showVariations={false}\n                getEmojiUrl={getEmojiUrl}\n              />\n            ))\n        : null}\n      <div className={cx(styles.pointer)} style={pointerStyle} />\n    </div>\n  );\n}\n\nfunction usePointerStyle(VariationPickerRef: React.RefObject<HTMLElement>) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return function getPointerStyle() {\n    const style: React.CSSProperties = {};\n    if (!VariationPickerRef.current) {\n      return style;\n    }\n\n    if (AnchoredEmojiRef.current) {\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const offsetLeft = emojiTruOffsetLeft(button);\n\n      if (!button) {\n        return style;\n      }\n\n      // half of the button\n      style.left = offsetLeft + button?.clientWidth / 2;\n    }\n\n    return style;\n  };\n}\n\nfunction useVariationPickerTop(\n  VariationPickerRef: React.RefObject<HTMLElement>\n) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const BodyRef = useBodyRef();\n  let direction = Direction.Up;\n\n  return {\n    getMenuDirection,\n    getTop\n  };\n\n  function getMenuDirection() {\n    return direction;\n  }\n\n  function getTop() {\n    direction = Direction.Up;\n    let emojiOffsetTop = 0;\n\n    if (!VariationPickerRef.current) {\n      return 0;\n    }\n\n    const height = elementHeight(VariationPickerRef.current);\n\n    if (AnchoredEmojiRef.current) {\n      const bodyRef = BodyRef.current;\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const buttonHeight = elementHeight(button);\n\n      emojiOffsetTop = emojiTrueOffsetTop(button);\n\n      const scrollTop = bodyRef?.scrollTop ?? 0;\n\n      if (scrollTop > emojiOffsetTop - height) {\n        direction = Direction.Down;\n        emojiOffsetTop += buttonHeight + height;\n      }\n    }\n\n    return emojiOffsetTop - height;\n  }\n}\n\nconst styles = stylesheet.create({\n  variationPicker: {\n    '.': ClassNames.variationPicker,\n    position: 'absolute',\n    right: '15px',\n    left: '15px',\n    padding: '5px',\n    boxShadow: '0px 2px 5px rgba(0, 0, 0, 0.2)',\n    borderRadius: '3px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-around',\n    opacity: '0',\n    visibility: 'hidden',\n    pointerEvents: 'none',\n    top: '-100%',\n    border: '1px solid var(--epr-picker-border-color)',\n    height: 'var(--epr-emoji-variation-picker-height)',\n    zIndex: 'var(--epr-skin-variation-picker-z-index)',\n    background: 'var(--epr-emoji-variation-picker-bg-color)',\n    transform: 'scale(0.9)',\n    transition: 'transform 0.1s ease-out, opacity 0.2s ease-out'\n  },\n  visible: {\n    opacity: '1',\n    visibility: 'visible',\n    pointerEvents: 'all',\n    transform: 'scale(1)'\n  },\n  pointingUp: {\n    '.': 'pointing-up',\n    transformOrigin: 'center 0%',\n    transform: 'scale(0.9)'\n  },\n  '.pointing-up': {\n    pointer: {\n      top: '0',\n      transform: 'rotate(180deg) translateY(100%) translateX(18px)'\n    }\n  },\n  pointer: {\n    '.': 'epr-emoji-pointer',\n    content: '',\n    position: 'absolute',\n    width: '25px',\n    height: '15px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '50px 15px',\n    top: '100%',\n    transform: 'translateX(-18px)',\n    backgroundImage: `url(${SVGTriangle})`\n  },\n  ...darkMode('pointer', {\n    backgroundPosition: '-25px 0'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { MOUSE_EVENT_SOURCE } from '../../config/useConfig';\nimport { useOnMouseMove } from '../../hooks/useDisallowMouseMove';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useOnScroll } from '../../hooks/useOnScroll';\nimport { useBodyRef } from '../context/ElementRefContext';\n\nimport { EmojiList } from './EmojiList';\nimport { EmojiVariationPicker } from './EmojiVariationPicker';\n\nexport function Body() {\n  const BodyRef = useBodyRef();\n  useOnScroll(BodyRef);\n  useMouseDownHandlers(BodyRef, MOUSE_EVENT_SOURCE.PICKER);\n  useOnMouseMove();\n\n  return (\n    <div\n      className={cx(styles.body, commonInteractionStyles.hiddenOnReactions)}\n      ref={BodyRef}\n    >\n      <EmojiVariationPicker />\n      <EmojiList />\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  body: {\n    '.': ClassNames.scrollBody,\n    flex: '1',\n    overflowY: 'scroll',\n    overflowX: 'hidden',\n    position: 'relative'\n  }\n});\n", "import { NullableElement } from './selectors';\n\nexport function detectEmojyPartiallyBelowFold(\n  button: HTMLButtonElement,\n  bodyRef: NullableElement\n): number {\n  if (!button || !bodyRef) {\n    return 0;\n  }\n\n  const buttonRect = button.getBoundingClientRect();\n  const bodyRect = bodyRef.getBoundingClientRect();\n\n  // If the element is obscured by at least half of its size\n  return bodyRect.height - (buttonRect.y - bodyRect.y);\n}\n", "import * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { detectEmojyPartiallyBelowFold } from '../DomUtils/detectEmojyPartiallyBelowFold';\nimport { focusElement } from '../DomUtils/focusElement';\nimport {\n  allUnifiedFromEmojiElement,\n  buttonFromTarget\n} from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { PreviewEmoji } from '../components/footer/Preview';\n\nimport {\n  useAllowMouseMove,\n  useIsMouseDisallowed\n} from './useDisallowMouseMove';\n\nexport function useEmojiPreviewEvents(\n  allow: boolean,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const BodyRef = useBodyRef();\n  const isMouseDisallowed = useIsMouseDisallowed();\n  const allowMouseMove = useAllowMouseMove();\n\n  useEffect(() => {\n    if (!allow) {\n      return;\n    }\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('keydown', onEscape, {\n      passive: true\n    });\n\n    bodyRef?.addEventListener('mouseover', onMouseOver, true);\n\n    bodyRef?.addEventListener('focus', onEnter, true);\n\n    bodyRef?.addEventListener('mouseout', onLeave, {\n      passive: true\n    });\n    bodyRef?.addEventListener('blur', onLeave, true);\n\n    function onEnter(e: FocusEvent) {\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (!button) {\n        return onLeave();\n      }\n\n      const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n      if (!unified || !originalUnified) {\n        return onLeave();\n      }\n\n      setPreviewEmoji({\n        unified,\n        originalUnified\n      });\n    }\n    function onLeave(e?: FocusEvent | MouseEvent) {\n      if (e) {\n        const relatedTarget = e.relatedTarget as HTMLElement;\n\n        if (!buttonFromTarget(relatedTarget)) {\n          return setPreviewEmoji(null);\n        }\n      }\n\n      setPreviewEmoji(null);\n    }\n    function onEscape(e: KeyboardEvent) {\n      if (e.key === 'Escape') {\n        setPreviewEmoji(null);\n      }\n    }\n\n    function onMouseOver(e: MouseEvent) {\n      if (isMouseDisallowed()) {\n        return;\n      }\n\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (button) {\n        const belowFoldByPx = detectEmojyPartiallyBelowFold(button, bodyRef);\n        const buttonHeight = button.getBoundingClientRect().height;\n        if (belowFoldByPx < buttonHeight) {\n          return handlePartiallyVisibleElementFocus(button, setPreviewEmoji);\n        }\n\n        focusElement(button);\n      }\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('mouseover', onMouseOver);\n      bodyRef?.removeEventListener('mouseout', onLeave);\n      bodyRef?.removeEventListener('focus', onEnter, true);\n      bodyRef?.removeEventListener('blur', onLeave, true);\n      bodyRef?.removeEventListener('keydown', onEscape);\n    };\n  }, [BodyRef, allow, setPreviewEmoji, isMouseDisallowed, allowMouseMove]);\n}\n\nfunction handlePartiallyVisibleElementFocus(\n  button: HTMLElement,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n  if (!unified || !originalUnified) {\n    return;\n  }\n\n  (document.activeElement as HTMLElement)?.blur?.();\n\n  setPreviewEmoji({\n    unified,\n    originalUnified\n  });\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport enum FlexDirection {\n  ROW = 'FlexRow',\n  COLUMN = 'FlexColumn'\n}\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n  direction?: FlexDirection;\n}>;\n\nexport default function Flex({\n  children,\n  className,\n  style = {},\n  direction = FlexDirection.ROW\n}: Props) {\n  return (\n    <div\n      style={{ ...style }}\n      className={cx(styles.flex, className, styles[direction])}\n    >\n      {children}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  flex: {\n    display: 'flex'\n  },\n  [FlexDirection.ROW]: {\n    flexDirection: 'row'\n  },\n  [FlexDirection.COLUMN]: {\n    flexDirection: 'column'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\n\ntype Props = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Space({ className, style = {} }: Props) {\n  return <div style={{ flex: 1, ...style }} className={cx(className)} />;\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Absolute({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'absolute' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Relative({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'relative' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport { skinTonesNamed } from '../../../data/skinToneVariations';\nimport { SkinTones } from '../../../types/exposedTypes';\nimport { Button } from '../../atoms/Button';\n\ntype Props = {\n  isOpen: boolean;\n  onClick: () => void;\n  isActive: boolean;\n  skinToneVariation: SkinTones;\n  style?: React.CSSProperties;\n};\n\n// eslint-disable-next-line complexity\nexport function BtnSkinToneVariation({\n  isOpen,\n  onClick,\n  isActive,\n  skinToneVariation,\n  style\n}: Props) {\n  return (\n    <Button\n      style={style}\n      onClick={onClick}\n      className={cx(\n        `epr-tone-${skinToneVariation}`,\n        styles.tone,\n        !isOpen && styles.closedTone,\n        isActive && styles.active\n      )}\n      aria-pressed={isActive}\n      aria-label={`Skin tone ${skinTonesNamed[skinToneVariation as SkinTones]}`}\n    ></Button>\n  );\n}\n\nconst styles = stylesheet.create({\n  closedTone: {\n    opacity: '0',\n    zIndex: '0'\n  },\n  active: {\n    '.': 'epr-active',\n    zIndex: '1',\n    opacity: '1'\n  },\n  tone: {\n    '.': 'epr-tone',\n    width: 'var(--epr-skin-tone-size)',\n    display: 'block',\n    cursor: 'pointer',\n    borderRadius: '4px',\n    height: 'var(--epr-skin-tone-size)',\n    position: 'absolute',\n    right: '0',\n    transition: 'transform 0.3s ease-in-out, opacity 0.35s ease-in-out',\n    zIndex: '0',\n    border: '1px solid var(--epr-skin-tone-outer-border-color)',\n    boxShadow: 'inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)',\n    ':hover': {\n      boxShadow: '0 0 0 3px var(--epr-active-skin-hover-color), inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)'\n    },\n    ':focus': {\n      boxShadow: '0 0 0 3px var(--epr-focus-bg-color)'\n    },\n    '&.epr-tone-neutral': {\n      backgroundColor: '#ffd225'\n    },\n    '&.epr-tone-1f3fb': {\n      backgroundColor: '#ffdfbd'\n    },\n    '&.epr-tone-1f3fc': {\n      backgroundColor: '#e9c197'\n    },\n    '&.epr-tone-1f3fd': {\n      backgroundColor: '#c88e62'\n    },\n    '&.epr-tone-1f3fe': {\n      backgroundColor: '#a86637'\n    },\n    '&.epr-tone-1f3ff': {\n      backgroundColor: '#60463a'\n    }\n  }\n});\n", "/* eslint-disable complexity */\nimport { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../../DomUtils/classNames';\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useOnSkinToneChangeConfig,\n  useSkinTonesDisabledConfig\n} from '../../../config/useConfig';\nimport skinToneVariations from '../../../data/skinToneVariations';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFocusSearchInput } from '../../../hooks/useFocus';\nimport Absolute from '../../Layout/Absolute';\nimport Relative from '../../Layout/Relative';\nimport { useSkinTonePickerRef } from '../../context/ElementRefContext';\nimport {\n  useActiveSkinToneState,\n  useSkinToneFanOpenState\n} from '../../context/PickerContext';\n\nimport { BtnSkinToneVariation } from './BtnSkinToneVariation';\n\nconst ITEM_SIZE = 28;\n\ntype Props = {\n  direction?: SkinTonePickerDirection;\n};\n\nexport function SkinTonePickerMenu() {\n  return (\n    <Relative style={{ height: ITEM_SIZE }}>\n      <Absolute style={{ bottom: 0, right: 0 }}>\n        <SkinTonePicker direction={SkinTonePickerDirection.VERTICAL} />\n      </Absolute>\n    </Relative>\n  );\n}\n\nexport function SkinTonePicker({\n  direction = SkinTonePickerDirection.HORIZONTAL\n}: Props) {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const isDisabled = useSkinTonesDisabledConfig();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const [activeSkinTone, setActiveSkinTone] = useActiveSkinToneState();\n  const onSkinToneChange = useOnSkinToneChangeConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const focusSearchInput = useFocusSearchInput();\n\n  if (isDisabled) {\n    return null;\n  }\n\n  const fullWidth = `${ITEM_SIZE * skinToneVariations.length}px`;\n\n  const expandedSize = isOpen ? fullWidth : ITEM_SIZE + 'px';\n\n  const vertical = direction === SkinTonePickerDirection.VERTICAL;\n\n  return (\n    <Relative\n      className={cx(\n        styles.skinTones,\n        vertical && styles.vertical,\n        isOpen && styles.open,\n        vertical && isOpen && styles.verticalShadow\n      )}\n      style={\n        vertical\n          ? { flexBasis: expandedSize, height: expandedSize }\n          : { flexBasis: expandedSize }\n      }\n    >\n      <div className={cx(styles.select)} ref={SkinTonePickerRef}>\n        {skinToneVariations.map((skinToneVariation, i) => {\n          const active = skinToneVariation === activeSkinTone;\n\n          return (\n            <BtnSkinToneVariation\n              key={skinToneVariation}\n              skinToneVariation={skinToneVariation}\n              isOpen={isOpen}\n              style={{\n                transform: cx(\n                  vertical\n                    ? `translateY(-${i * (isOpen ? ITEM_SIZE : 0)}px)`\n                    : `translateX(-${i * (isOpen ? ITEM_SIZE : 0)}px)`,\n                  isOpen && active && 'scale(1.3)'\n                )\n              }}\n              isActive={active}\n              onClick={() => {\n                if (isOpen) {\n                  setActiveSkinTone(skinToneVariation);\n                  onSkinToneChange(skinToneVariation);\n                  focusSearchInput();\n                } else {\n                  setIsOpen(true);\n                }\n                closeAllOpenToggles();\n              }}\n            />\n          );\n        })}\n      </div>\n    </Relative>\n  );\n}\n\nexport enum SkinTonePickerDirection {\n  VERTICAL = ClassNames.vertical,\n  HORIZONTAL = ClassNames.horizontal\n}\n\nconst styles = stylesheet.create({\n  skinTones: {\n    '.': 'epr-skin-tones',\n    '--': {\n      '--epr-skin-tone-size': '15px'\n    },\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'flex-end',\n    transition: 'all 0.3s ease-in-out',\n    padding: '10px 0'\n  },\n  vertical: {\n    padding: '9px',\n    alignItems: 'flex-end',\n    flexDirection: 'column',\n    borderRadius: '6px',\n    border: '1px solid var(--epr-bg-color)'\n  },\n  verticalShadow: {\n    boxShadow: '0px 0 7px var(--epr-picker-border-color)'\n  },\n  open: {\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(5px)',\n    background: 'var(--epr-skin-tone-picker-menu-color)',\n    '.epr-active': {\n      border: '1px solid var(--epr-active-skin-tone-indicator-border-color)'\n    }\n  },\n  select: {\n    '.': 'epr-skin-tone-select',\n    position: 'relative',\n    width: 'var(--epr-skin-tone-size)',\n    height: 'var(--epr-skin-tone-size)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  usePreviewConfig\n} from '../../config/useConfig';\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUnified\n} from '../../dataUtils/emojiSelectors';\nimport { useEmojiPreviewEvents } from '../../hooks/useEmojiPreviewEvents';\nimport { useIsSkinToneInPreview } from '../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../Layout/Flex';\nimport Space from '../Layout/Space';\nimport {\n  useEmojiVariationPickerState,\n  useReactionsModeState\n} from '../context/PickerContext';\nimport { ViewOnlyEmoji } from '../emoji/ViewOnlyEmoji';\nimport { SkinTonePickerMenu } from '../header/SkinTonePicker/SkinTonePicker';\n\nexport function Preview() {\n  const previewConfig = usePreviewConfig();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n  const [reactionsOpen] = useReactionsModeState();\n\n  if (!previewConfig.showPreview) {\n    return null;\n  }\n\n  return (\n    <Flex\n      className={cx(\n        styles.preview,\n        commonInteractionStyles.hiddenOnReactions,\n        reactionsOpen && styles.hideOnReactions\n      )}\n    >\n      <PreviewBody />\n      <Space />\n      {isSkinToneInPreview ? <SkinTonePickerMenu /> : null}\n    </Flex>\n  );\n}\n\nexport function PreviewBody() {\n  const previewConfig = usePreviewConfig();\n  const [previewEmoji, setPreviewEmoji] = useState<PreviewEmoji>(null);\n  const emojiStyle = useEmojiStyleConfig();\n  const [variationPickerEmoji] = useEmojiVariationPickerState();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEmojiPreviewEvents(previewConfig.showPreview, setPreviewEmoji);\n\n  const emoji = emojiByUnified(\n    previewEmoji?.unified ?? previewEmoji?.originalUnified\n  );\n\n  const show = emoji != null && previewEmoji != null;\n\n  return <PreviewContent />;\n\n  function PreviewContent() {\n    const defaultEmoji =\n      variationPickerEmoji ?? emojiByUnified(previewConfig.defaultEmoji);\n    if (!defaultEmoji) {\n      return null;\n    }\n    const defaultText = variationPickerEmoji\n      ? emojiName(variationPickerEmoji)\n      : previewConfig.defaultCaption;\n\n    return (\n      <>\n        <div>\n          {show ? (\n            <ViewOnlyEmoji\n              unified={previewEmoji?.unified as string}\n              emoji={emoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : defaultEmoji ? (\n            <ViewOnlyEmoji\n              unified={emojiUnified(defaultEmoji)}\n              emoji={defaultEmoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : null}\n        </div>\n        <div className={cx(styles.label)}>\n          {show ? emojiName(emoji) : defaultText}\n        </div>\n      </>\n    );\n  }\n}\n\nexport type PreviewEmoji = null | {\n  unified: string;\n  originalUnified: string;\n};\n\nconst styles = stylesheet.create({\n  preview: {\n    alignItems: 'center',\n    borderTop: '1px solid var(--epr-preview-border-color)',\n    height: 'var(--epr-preview-height)',\n    padding: '0 var(--epr-horizontal-padding)',\n    position: 'relative',\n    zIndex: 'var(--epr-preview-z-index)'\n  },\n  label: {\n    color: 'var(--epr-preview-text-color)',\n    fontSize: 'var(--epr-preview-text-size)',\n    padding: 'var(--epr-preview-text-padding)',\n    textTransform: 'capitalize'\n  },\n  emoji: {\n    padding: '0'\n  },\n  hideOnReactions: {\n    opacity: '0',\n    transition: 'opacity 0.5s ease-in-out'\n  }\n});\n", "export function categoryNameFromDom($category: Element | null): string | null {\n  return $category?.getAttribute('data-name') ?? null;\n}\n", "import { useEffect } from 'react';\n\nimport { categoryNameFromDom } from '../DomUtils/categoryNameFromDom';\nimport { asSelectors, ClassNames } from '../DomUtils/classNames';\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nexport function useActiveCategoryScrollDetection(\n  setActiveCategory: (category: string) => void\n) {\n  const BodyRef = useBodyRef();\n\n  useEffect(() => {\n    const visibleCategories = new Map();\n    const bodyRef = BodyRef.current;\n    const observer = new IntersectionObserver(\n      entries => {\n        if (!bodyRef) {\n          return;\n        }\n\n        for (const entry of entries) {\n          const id = categoryNameFromDom(entry.target);\n          visibleCategories.set(id, entry.intersectionRatio);\n        }\n\n        const ratios = Array.from(visibleCategories);\n        const lastCategory = ratios[ratios.length - 1];\n\n        if (lastCategory[1] == 1) {\n          return setActiveCategory(lastCategory[0]);\n        }\n\n        for (const [id, ratio] of ratios) {\n          if (ratio) {\n            setActiveCategory(id);\n            break;\n          }\n        }\n      },\n      {\n        threshold: [0, 1]\n      }\n    );\n    bodyRef?.querySelectorAll(asSelectors(ClassNames.category)).forEach(el => {\n      observer.observe(el);\n    });\n  }, [BodyRef, setActiveCategory]);\n}\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport { NullableElement } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  usePickerMainRef\n} from '../components/context/ElementRefContext';\n\nexport function useScrollCategoryIntoView() {\n  const BodyRef = useBodyRef();\n  const PickerMainRef = usePickerMainRef();\n\n  return function scrollCategoryIntoView(category: string): void {\n    if (!BodyRef.current) {\n      return;\n    }\n    const $category = BodyRef.current?.querySelector(\n      `[data-name=\"${category}\"]`\n    ) as NullableElement;\n\n    if (!$category) {\n      return;\n    }\n\n    const offsetTop = $category.offsetTop || 0;\n\n    scrollTo(PickerMainRef.current, offsetTop);\n  };\n}\n", "import { useCustomEmojisConfig } from '../config/useConfig';\n\nexport function useShouldHideCustomEmojis() {\n  const customCategoryConfig = useCustomEmojisConfig();\n\n  if (!customCategoryConfig) {\n    return false;\n  }\n\n  return customCategoryConfig.length === 0;\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\nimport { Button } from '../atoms/Button';\n\nimport SVGNavigation from './svg/CategoryNav.svg';\n\ntype Props = {\n  isActiveCategory: boolean;\n  category: string;\n  allowNavigation: boolean;\n  onClick: () => void;\n  categoryConfig: CategoryConfig;\n};\n\nexport function CategoryButton({\n  isActiveCategory,\n  category,\n  allowNavigation,\n  categoryConfig,\n  onClick\n}: Props) {\n  return (\n    <Button\n      tabIndex={allowNavigation ? 0 : -1}\n      className={cx(\n        styles.catBtn,\n        commonInteractionStyles.categoryBtn,\n        `epr-icn-${category}`,\n        {\n          [ClassNames.active]: isActiveCategory\n        }\n      )}\n      onClick={onClick}\n      aria-label={categoryNameFromCategoryConfig(categoryConfig)}\n      aria-selected={isActiveCategory}\n      role=\"tab\"\n      aria-controls=\"epr-category-nav-id\"\n    />\n  );\n}\n\nconst DarkActivePositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 3)'\n};\nconst DarkPositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 2)'\n};\n\nconst DarkInactivePosition = {\n  ':not(.epr-search-active)': {\n    catBtn: {\n      ':hover': DarkActivePositionY,\n      '&.epr-active': DarkActivePositionY\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  catBtn: {\n    '.': 'epr-cat-btn',\n    display: 'inline-block',\n    transition: 'opacity 0.2s ease-in-out',\n    position: 'relative',\n    height: 'var(--epr-category-navigation-button-size)',\n    width: 'var(--epr-category-navigation-button-size)',\n    backgroundSize: 'calc(var(--epr-category-navigation-button-size) * 10)',\n    outline: 'none',\n    backgroundPosition: '0 0',\n    backgroundImage: `url(${SVGNavigation})`,\n    ':focus:before': {\n      content: '',\n      position: 'absolute',\n      top: '-2px',\n      left: '-2px',\n      right: '-2px',\n      bottom: '-2px',\n      border: '2px solid var(--epr-category-icon-active-color)',\n      borderRadius: '50%'\n    },\n    '&.epr-icn-suggested': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -8)'\n    },\n    '&.epr-icn-custom': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -9)'\n    },\n    '&.epr-icn-activities': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -4)'\n    },\n    '&.epr-icn-animals_nature': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -1)'\n    },\n    '&.epr-icn-flags': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -7)'\n    },\n    '&.epr-icn-food_drink': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -2)'\n    },\n    '&.epr-icn-objects': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -5)'\n    },\n    '&.epr-icn-smileys_people': {\n      backgroundPositionX: '0px'\n    },\n    '&.epr-icn-symbols': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -6)'\n    },\n    '&.epr-icn-travel_places': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -3)'\n    }\n  },\n  ...darkMode('catBtn', DarkPositionY),\n  '.epr-dark-theme': {\n    ...DarkInactivePosition\n  },\n  '.epr-auto-theme': {\n    ...DarkInactivePosition\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { categoryFromCategoryConfig } from '../../config/categoryConfig';\nimport { useCategoriesConfig } from '../../config/useConfig';\nimport { useActiveCategoryScrollDetection } from '../../hooks/useActiveCategoryScrollDetection';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useScrollCategoryIntoView } from '../../hooks/useScrollCategoryIntoView';\nimport { useShouldHideCustomEmojis } from '../../hooks/useShouldHideCustomEmojis';\nimport { isCustomCategory } from '../../typeRefinements/typeRefinements';\nimport { useCategoryNavigationRef } from '../context/ElementRefContext';\n\nimport { CategoryButton } from './CategoryButton';\n\nexport function CategoryNavigation() {\n  const [activeCategory, setActiveCategory] = useState<string | null>(null);\n  const scrollCategoryIntoView = useScrollCategoryIntoView();\n  useActiveCategoryScrollDetection(setActiveCategory);\n  const isSearchMode = useIsSearchMode();\n\n  const categoriesConfig = useCategoriesConfig();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const hideCustomCategory = useShouldHideCustomEmojis();\n\n  return (\n    <div\n      className={cx(styles.nav)}\n      role=\"tablist\"\n      aria-label=\"Category navigation\"\n      id=\"epr-category-nav-id\"\n      ref={CategoryNavigationRef}\n    >\n      {categoriesConfig.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n        const isActiveCategory = category === activeCategory;\n\n        if (isCustomCategory(categoryConfig) && hideCustomCategory) {\n          return null;\n        }\n\n        const allowNavigation = !isSearchMode && !isActiveCategory;\n\n        return (\n          <CategoryButton\n            key={category}\n            category={category}\n            isActiveCategory={isActiveCategory}\n            allowNavigation={allowNavigation}\n            categoryConfig={categoryConfig}\n            onClick={() => {\n              scrollCategoryIntoView(category);\n              setTimeout(() => {\n                setActiveCategory(category);\n              }, 10);\n            }}\n          />\n        );\n      })}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  nav: {\n    '.': 'epr-category-nav',\n    display: 'flex',\n    flexDirection: 'row',\n    justifyContent: 'space-around',\n    padding: 'var(--epr-header-padding)'\n  },\n  '.epr-search-active': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  },\n  '.epr-main:has(input:not(:placeholder-shown))': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../../Stylesheet/stylesheet';\nimport { useClearSearch } from '../../../hooks/useFilter';\nimport { Button } from '../../atoms/Button';\n\nimport SVGTimes from './svg/times.svg';\n\nexport function BtnClearSearch() {\n  const clearSearch = useClearSearch();\n\n  return (\n    <Button\n      className={cx(\n        styles.btnClearSearch,\n        commonInteractionStyles.visibleOnSearchOnly\n      )}\n      onClick={clearSearch}\n      aria-label=\"Clear\"\n      title=\"Clear\"\n    >\n      <div className={cx(styles.icnClearnSearch)} />\n    </Button>\n  );\n}\n\nconst HoverDark = {\n  ':hover': {\n    '> .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', HoverDark)\n});\n", "import * as React from 'react';\n\nimport { ClassNames, asSelectors } from '../../../DomUtils/classNames';\nimport { getNormalizedSearchTerm } from '../../../hooks/useFilter';\n\nconst SCOPE = `${asSelectors(ClassNames.emojiPicker)} ${asSelectors(\n  ClassNames.emojiList\n)}`;\n\nconst EMOJI_BUTTON = ['button', asSelectors(ClassNames.emoji)].join('');\nconst CATEGORY = asSelectors(ClassNames.category);\n\nexport function CssSearch({ value }: { value: undefined | string }) {\n  if (!value) {\n    return null;\n  }\n\n  const q = genQuery(value);\n\n  return (\n    <style>{`\n    ${SCOPE} ${EMOJI_BUTTON} {\n      display: none;\n    }\n\n\n    ${SCOPE} ${q} {\n      display: flex;\n    }\n\n    ${SCOPE} ${CATEGORY}:not(:has(${q})) {\n      display: none;\n    }\n  `}</style>\n  );\n}\n\nfunction genQuery(value: string): string {\n  return [\n    EMOJI_BUTTON,\n    '[data-full-name*=\"',\n    getNormalizedSearchTerm(value),\n    '\"]'\n  ].join('');\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\n\nimport SVGMagnifier from './svg/magnifier.svg';\n\nexport function IcnSearch() {\n  return <div className={cx(styles.icnSearch)} />;\n}\n\nconst styles = stylesheet.create({\n  icnSearch: {\n    '.': 'epr-icn-search',\n    content: '',\n    position: 'absolute',\n    top: '50%',\n    left: 'var(--epr-search-bar-inner-padding)',\n    transform: 'translateY(-50%)',\n    width: '20px',\n    height: '20px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '20px',\n    backgroundImage: `url(${SVGMagnifier})`\n  },\n  ...darkMode('icnSearch', {\n    backgroundPositionY: '-20px'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useAutoFocusSearchConfig,\n  useSearchDisabledConfig,\n  useSearchPlaceHolderConfig\n} from '../../../config/useConfig';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFilter } from '../../../hooks/useFilter';\nimport { useIsSkinToneInSearch } from '../../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../../Layout/Flex';\nimport Relative from '../../Layout/Relative';\nimport { useSearchInputRef } from '../../context/ElementRefContext';\nimport { SkinTonePicker } from '../SkinTonePicker/SkinTonePicker';\n\nimport { BtnClearSearch } from './BtnClearSearch';\nimport { CssSearch } from './CssSearch';\nimport { IcnSearch } from './IcnSearch';\nimport SVGTimes from './svg/times.svg';\n\nexport function SearchContainer() {\n  const searchDisabled = useSearchDisabledConfig();\n\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  if (searchDisabled) {\n    return null;\n  }\n\n  return (\n    <Flex className={cx(styles.overlay)}>\n      <Search />\n\n      {isSkinToneInSearch ? <SkinTonePicker /> : null}\n    </Flex>\n  );\n}\n\nexport function Search() {\n  const [inc, setInc] = useState(0);\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const SearchInputRef = useSearchInputRef();\n  const placeholder = useSearchPlaceHolderConfig();\n  const autoFocus = useAutoFocusSearchConfig();\n  const { statusSearchResults, searchTerm, onChange } = useFilter();\n\n  const input = SearchInputRef?.current;\n  const value = input?.value;\n\n  return (\n    <Relative className={cx(styles.searchContainer)}>\n      <CssSearch value={value} />\n      <input\n        // eslint-disable-next-line jsx-a11y/no-autofocus\n        autoFocus={autoFocus}\n        aria-label={'Type to search for an emoji'}\n        onFocus={closeAllOpenToggles}\n        className={cx(styles.search)}\n        type=\"text\"\n        aria-controls=\"epr-search-id\"\n        placeholder={placeholder}\n        onChange={event => {\n          setInc(inc + 1);\n          setTimeout(() => {\n            onChange(event?.target?.value ?? value);\n          });\n        }}\n        ref={SearchInputRef}\n      />\n      {searchTerm ? (\n        <div\n          role=\"status\"\n          className={cx('epr-status-search-results', styles.visuallyHidden)}\n          aria-live=\"polite\"\n          id=\"epr-search-id\"\n          aria-atomic=\"true\"\n        >\n          {statusSearchResults}\n        </div>\n      ) : null}\n      <IcnSearch />\n      <BtnClearSearch />\n    </Relative>\n  );\n}\n\nconst styles = stylesheet.create({\n  overlay: {\n    padding: 'var(--epr-header-padding)',\n    zIndex: 'var(--epr-header-overlay-z-index)'\n  },\n  searchContainer: {\n    '.': 'epr-search-container',\n    flex: '1',\n    display: 'block',\n    minWidth: '0'\n  },\n  visuallyHidden: {\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(50%)',\n    height: '1px',\n    overflow: 'hidden',\n    position: 'absolute',\n    whiteSpace: 'nowrap',\n    width: '1px'\n  },\n  search: {\n    outline: 'none',\n    transition: 'all 0.2s ease-in-out',\n    color: 'var(--epr-search-input-text-color)',\n    borderRadius: 'var(--epr-search-input-border-radius)',\n    padding: 'var(--epr-search-input-padding)',\n    height: 'var(--epr-search-input-height)',\n    backgroundColor: 'var(--epr-search-input-bg-color)',\n    border: '1px solid var(--epr-search-input-bg-color)',\n    width: '100%',\n    ':focus': {\n      backgroundColor: 'var(--epr-search-input-bg-color-active)',\n      border: '1px solid var(--epr-search-border-color)'\n    },\n    '::placeholder': {\n      color: 'var(--epr-search-input-placeholder-color)'\n    }\n  },\n\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', {\n    ':hover > .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonInteractionStyles } from '../../Stylesheet/stylesheet';\nimport Relative from '../Layout/Relative';\nimport { CategoryNavigation } from '../navigation/CategoryNavigation';\n\nimport { SearchContainer } from './Search/Search';\n\nexport function Header() {\n  return (\n    <Relative\n      className={cx('epr-header', commonInteractionStyles.hiddenOnReactions)}\n    >\n      <SearchContainer />\n      <CategoryNavigation />\n    </Relative>\n  );\n}\n", "import * as React from 'react';\n\nimport { PickerStyleTag } from './Stylesheet/stylesheet';\nimport { Reactions } from './components/Reactions/Reactions';\nimport { Body } from './components/body/Body';\nimport { ElementRefContextProvider } from './components/context/ElementRefContext';\nimport { PickerConfigProvider } from './components/context/PickerConfigContext';\nimport { useReactionsModeState } from './components/context/PickerContext';\nimport { Preview } from './components/footer/Preview';\nimport { Header } from './components/header/Header';\nimport PickerMain from './components/main/PickerMain';\nimport { compareConfig } from './config/compareConfig';\nimport { useAllowExpandReactions, useOpenConfig } from './config/useConfig';\n\nimport { PickerProps } from './index';\n\nfunction EmojiPicker(props: PickerProps) {\n  return (\n    <ElementRefContextProvider>\n      <PickerStyleTag />\n      <PickerConfigProvider {...props}>\n        <ContentControl />\n      </PickerConfigProvider>\n    </ElementRefContextProvider>\n  );\n}\n\nfunction ContentControl() {\n  const [reactionsDefaultOpen] = useReactionsModeState();\n  const allowExpandReactions = useAllowExpandReactions();\n\n  const [renderAll, setRenderAll] = React.useState(!reactionsDefaultOpen);\n  const isOpen = useOpenConfig();\n\n  React.useEffect(() => {\n    if (reactionsDefaultOpen && !allowExpandReactions) {\n      return;\n    }\n\n    if (!renderAll) {\n      setRenderAll(true);\n    }\n  }, [renderAll, allowExpandReactions, reactionsDefaultOpen]);\n\n  if (!isOpen) {\n    return null;\n  }\n\n  return (\n    <PickerMain>\n      <Reactions />\n      <ExpandedPickerContent renderAll={renderAll} />\n    </PickerMain>\n  );\n}\n\nfunction ExpandedPickerContent({ renderAll }: { renderAll: boolean }) {\n  if (!renderAll) {\n    return null;\n  }\n\n  return (\n    <>\n      <Header />\n      <Body />\n      <Preview />\n    </>\n  );\n}\n\n// eslint-disable-next-line complexity\nexport default React.memo(EmojiPicker, compareConfig);\n", "import * as React from 'react';\n\nexport default class ErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError() {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    // eslint-disable-next-line no-console\n    console.error('Emoji Picker React failed to render:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return null;\n    }\n\n    return this.props.children;\n  }\n}\n", "import * as React from 'react';\n\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { GetEmojiUrl } from './BaseEmojiProps';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\nexport function ExportedEmoji({\n  unified,\n  size = 32,\n  emojiStyle = EmojiStyle.APPLE,\n  lazyLoad = false,\n  getEmojiUrl,\n  emojiUrl\n}: {\n  unified: string;\n  emojiStyle?: EmojiStyle;\n  size?: number;\n  lazyLoad?: boolean;\n  getEmojiUrl?: GetEmojiUrl;\n  emojiUrl?: string;\n}) {\n  if (!unified && !emojiUrl && !getEmojiUrl) {\n    return null;\n  }\n\n  return (\n    <ViewOnlyEmoji\n      unified={unified}\n      size={size}\n      emojiStyle={emojiStyle}\n      lazyLoad={lazyLoad}\n      getEmojiUrl={emojiUrl ? () => emojiUrl : getEmojiUrl}\n    />\n  );\n}\n", "import * as React from 'react';\n\nimport EmojiPickerReact from './EmojiPickerReact';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { PickerConfig } from './config/config';\nimport {\n  MutableConfigContext,\n  useDefineMutableConfig\n} from './config/mutableConfig';\n\nexport { ExportedEmoji as Emoji } from './components/emoji/ExportedEmoji';\n\nexport {\n  EmojiStyle,\n  SkinTones,\n  Theme,\n  Categories,\n  EmojiClickData,\n  SuggestionMode,\n  SkinTonePickerLocation\n} from './types/exposedTypes';\n\nexport interface PickerProps extends PickerConfig {}\n\nexport default function EmojiPicker(props: PickerProps) {\n  const MutableConfigRef = useDefineMutableConfig({\n    onEmojiClick: props.onEmojiClick,\n    onReactionClick: props.onReactionClick,\n    onSkinToneChange: props.onSkinToneChange,\n  });\n\n  return (\n    <ErrorBoundary>\n      <MutableConfigContext.Provider value={MutableConfigRef}>\n        <EmojiPickerReact {...props} />\n      </MutableConfigContext.Provider>\n    </ErrorBoundary>\n  );\n}\n"], "names": ["ClassNames", "asSelectors", "classNames", "Array", "_len", "_key", "arguments", "map", "c", "join", "stylesheet", "createSheet", "hidden", "display", "opacity", "pointerEvents", "visibility", "overflow", "commonStyles", "create", "_extends", "PickerStyleTag", "React", "suppressHydrationWarning", "dangerouslySetInnerHTML", "__html", "getStyle", "commonInteractionStyles", "categoryBtn", "backgroundPositionY", "hiddenOnSearch", "visibleOnSearchOnly", "hiddenOnReactions", "transition", "height", "width", "darkMode", "key", "value", "_eprDarkTheme", "_eprAutoTheme", "compareConfig", "prev", "next", "prevCustomEmojis", "_prev$customEmojis", "customEmojis", "nextCustomEmojis", "_next$customEmojis", "open", "emojiVersion", "reactionsDefaultOpen", "searchPlaceHolder", "searchPlaceholder", "defaultSkinTone", "skinTonesDisabled", "autoFocusSearch", "emojiStyle", "theme", "suggestedEmojisMode", "lazyLoadEmojis", "className", "style", "searchDisabled", "skinTonePickerLocation", "length", "DEFAULT_REACTIONS", "SuggestionMode", "EmojiStyle", "Theme", "SkinTones", "Categories", "SkinTonePickerLocation", "categoriesOrdered", "SUGGESTED", "CUSTOM", "SMILEYS_PEOPLE", "ANIMALS_NATURE", "FOOD_DRINK", "TRAVEL_PLACES", "ACTIVITIES", "OBJECTS", "SYMBOLS", "FLAGS", "SuggestedRecent", "name", "category", "configByCategory", "_configByCategory", "baseCategoriesConfig", "modifiers", "categoryFromCategoryConfig", "categoryNameFromCategoryConfig", "mergeCategoriesConfig", "userCategoriesConfig", "extra", "suggestionMode", "RECENT", "base", "_userCategoriesConfig", "getBaseConfigByCategory", "modifier", "Object", "assign", "CDN_URL_APPLE", "CDN_URL_FACEBOOK", "CDN_URL_TWITTER", "CDN_URL_GOOGLE", "cdnUrl", "TWITTER", "GOOGLE", "FACEBOOK", "APPLE", "skinToneVariations", "NEUTRAL", "LIGHT", "MEDIUM_LIGHT", "MEDIUM", "MEDIUM_DARK", "DARK", "skinTonesNamed", "entries", "reduce", "acc", "_ref", "skinTonesMapped", "mapped", "skinTone", "_Object$assign", "EmojiProperties", "alphaNumericEmojiIndex", "setTimeout", "allEmojis", "searchIndex", "emoji", "indexEmoji", "joinedNameString", "emojiNames", "flat", "toLowerCase", "replace", "split", "for<PERSON>ach", "char", "_alphaNumericEmojiInd", "emojiUnified", "_emoji$EmojiPropertie", "addedIn", "parseFloat", "added_in", "emojiName", "unifiedWithoutSkinTone", "unified", "splat", "_splat$splice", "splice", "emojiHasVariations", "_emojiVariationUnifie", "emojiVariationUnified", "emojisByCategory", "_emojis$category", "emojis", "emojiUrlByUnified", "emojiVariations", "_emoji$EmojiPropertie2", "variations", "find", "variation", "includes", "emojiByUnified", "allEmojisByUnified", "withoutSkinTone", "values", "setCustomEmojis", "emojiData", "customToRegularEmoji", "push", "names", "id", "imgUrl", "<PERSON><PERSON><PERSON>", "activeVariationFromUnified", "_unified$split", "suspectedSkinTone", "KNOWN_FAILING_EMOJIS", "DEFAULT_SEARCH_PLACEHOLDER", "SEARCH_RESULTS_NO_RESULTS_FOUND", "SEARCH_RESULTS_SUFFIX", "SEARCH_RESULTS_ONE_RESULT_FOUND", "SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND", "mergeConfig", "userConfig", "basePickerConfig", "previewConfig", "_userConfig$previewCo", "config", "categories", "hidden<PERSON><PERSON><PERSON><PERSON>", "unicodeToHide", "add", "_config$customEmojis", "PREVIEW", "getEmojiUrl", "basePreviewConfig", "SEARCH", "FREQUENT", "Set", "reactions", "allowExpandReactions", "defaultEmoji", "defaultCaption", "showPreview", "ConfigContext", "PickerConfigProvider", "children", "_objectWithoutPropertiesLoose", "_excluded", "mergedConfig", "useSetConfig", "Provider", "_React$useState", "setMergedConfig", "usePickerConfig", "useDebouncedState", "initialValue", "delay", "_useState", "useState", "state", "setState", "timer", "useRef", "debouncedSetState", "Promise", "resolve", "current", "clearTimeout", "_window", "window", "useIsUnicodeHidden", "useUnicodeToHide", "has", "useDisallowedEmojis", "DisallowedEmojisRef", "emojiVersionConfig", "useEmojiVersionConfig", "useMemo", "Number", "isNaN", "disallowed<PERSON><PERSON><PERSON><PERSON>", "addedInNewerVersion", "useIsEmojiDisallowed", "isUnicodeHidden", "isEmojiDisallowed", "Boolean", "supportedLevel", "useMarkInitialLoad", "dispatch", "useEffect", "PickerContextProvider", "useDefaultSkinToneConfig", "useReactionsOpenConfig", "filterRef", "disallowClickRef", "disallowMouseRef", "disallowedEmojisRef", "suggestedUpdateState", "Date", "now", "searchTerm", "skinToneFanOpenState", "activeSkinTone", "activeCategoryState", "emojisThatFailedToLoadState", "emojiVariationPickerState", "reactionsModeState", "isPastInitialLoad", "setIsPastInitialLoad", "<PERSON>er<PERSON>ontext", "undefined", "useFilterRef", "_React$useContext", "useDisallowClickRef", "_React$useContext2", "useDisallowMouseRef", "_React$useContext3", "useReactionsModeState", "_React$useContext4", "useSearchTermState", "_React$useContext5", "useActiveSkinToneState", "_React$useContext6", "useEmojisThatFailedToLoadState", "_React$useContext7", "useIsPastInitialLoad", "_React$useContext8", "useEmojiVariationPickerState", "_React$useContext9", "useSkinToneFanOpenState", "_React$useContext10", "useUpdateSuggested", "_React$useContext12", "suggestedUpdated", "setsuggestedUpdate", "updateSuggested", "MutableConfigContext", "createContext", "useMutableConfig", "mutableConfig", "useContext", "useDefineMutableConfig", "MutableConfigRef", "onEmojiClick", "emptyFunc", "onReactionClick", "onSkinToneChange", "MOUSE_EVENT_SOURCE", "useSearchPlaceHolderConfig", "_usePickerConfig", "_find", "p", "_usePickerConfig2", "useAllowExpandReactions", "_usePickerConfig3", "useSkinTonesDisabledConfig", "_usePickerConfig4", "useEmojiStyleConfig", "_usePickerConfig5", "useAutoFocusSearchConfig", "_usePickerConfig6", "useCategoriesConfig", "_usePickerConfig7", "useCustomEmojisConfig", "_usePickerConfig8", "useOpenConfig", "_usePickerConfig9", "useOnEmojiClickConfig", "mouseEventSource", "_useMutableConfig", "_useReactionsModeStat", "setReactionsOpen", "handler", "REACTIONS", "args", "apply", "concat", "collapseToReactions", "o", "_len2", "_key2", "useOnSkinToneChangeConfig", "_useMutableConfig2", "usePreviewConfig", "_usePickerConfig10", "useThemeConfig", "_usePickerConfig11", "useSuggestedEmojisModeConfig", "_usePickerConfig12", "useLazyLoadEmojisConfig", "_usePickerConfig13", "useClassNameConfig", "_usePickerConfig14", "useStyleConfig", "_usePickerConfig15", "getDimension", "_usePickerConfig16", "_usePickerConfig17", "useSearchDisabledConfig", "_usePickerConfig18", "useSkinTonePickerLocationConfig", "_usePickerConfig19", "_usePickerConfig20", "useReactionsConfig", "_usePickerConfig21", "useGetEmojiUrlConfig", "_usePickerConfig22", "dimensionConfig", "useSearchResultsConfig", "searchResultsCount", "hasResults", "isPlural", "toString", "useIsSearchMode", "_useSearchTermState", "focusElement", "element", "requestAnimationFrame", "focus", "focusPrevElementSibling", "previousElementSibling", "focusNextElementSibling", "nextElement<PERSON><PERSON>ling", "focusFirstElementChild", "first", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getActiveElement", "document", "activeElement", "ElementRefContextProvider", "Picker<PERSON>ain<PERSON><PERSON>", "AnchoredEmojiRef", "BodyRef", "SearchInputRef", "SkinTonePickerRef", "CategoryNavigationRef", "VariationPickerRef", "ReactionsRef", "ElementRefContext", "useElementRef", "usePickerMainRef", "useAnchoredEmojiRef", "useSetAnchoredEmojiRef", "target", "useBodyRef", "useReactionsRef", "useSearchInputRef", "useSkinTonePickerRef", "useCategoryNavigationRef", "useVariationPickerRef", "scrollTo", "root", "top", "$eprBody", "queryScrollBody", "scrollTop", "scrollBy", "by", "useScrollTo", "useCallback", "scrollEmojiAboveLabel", "isEmojiBehindLabel", "closest", "variationPicker", "scrollBody", "closestScrollBody", "emojiDistanceFromScrollTop", "categoryLabelHeight", "closestCategory", "focusFirstVisibleEmoji", "parent", "firstVisibleEmoji", "focusAndClickFirstVisibleEmoji", "first<PERSON><PERSON><PERSON>", "click", "focusLastVisibleEmoji", "lastVisibleEmoji", "focusNextVisibleEmoji", "nextVisibleEmoji", "nextCategory", "focusPrevVisibleEmoji", "prevVisibleEmoji", "prevCategory", "focusVisibleEmojiOneRowUp", "exitUp", "visibleEmojiOneRowUp", "focusVisibleEmojiOneRowDown", "visibleEmojiOneRowDown", "categoryContent", "closestCategoryContent", "indexInRow", "elementIndexInRow", "row", "rowNumber", "countInRow", "elementCountInRow", "prevVisibleCategory", "getElementInRow", "allVisibleEmojis", "getElementInPrevRow", "hasNextRow", "nextVisibleCategory", "itemInNextRow", "getElementInNextRow", "useCloseAllOpenToggles", "_useEmojiVariationPic", "setVariationPicker", "_useSkinToneFanOpenSt", "skinToneFanOpen", "setSkinToneFanOpen", "closeAllOpenToggles", "useHasOpenToggles", "_useEmojiVariationPic2", "_useSkinToneFanOpenSt2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useDisallowMouseMove", "DisallowMouseRef", "disallowMouseMove", "useAllowMouseMove", "allowMouseMove", "useIsMouseDisallowed", "isMouseDisallowed", "useOnMouseMove", "bodyRef", "addEventListener", "onMouseMove", "passive", "removeEventListener", "useFocusSearchInput", "useFocusSkinTonePicker", "useFocusCategoryNavigation", "useSetFilterRef", "setFilter", "setter", "useClearSearch", "applySearch", "useApplySearch", "focusSearchInput", "clearSearch", "useAppendSearch", "appendSearch", "str", "getNormalizedSearchTerm", "useFilter", "setFilterRef", "statusSearchResults", "getStatusSearchResults", "onChange", "inputValue", "filter", "nextValue", "longestMatch", "findLongestMatch", "filterEmojiObjectByKeyword", "_useSearchTermState2", "setSearchTerm", "then", "keyword", "filtered", "hasMatch", "some", "useIsEmojiFiltered", "_useFilterRef", "_useSearchTermState3", "isEmojiFilteredBySearchTerm", "_filter$searchTerm", "dict", "longestMatchingKey", "keys", "sort", "a", "b", "trim", "filterState", "_Object$entries", "useSetVariationPicker", "setAnchoredEmojiRef", "setEmojiVariationPicker", "_emojiFromElement", "emojiFromElement", "useIsSkinToneInSearch", "skinTonePickerLocationConfig", "useIsSkinToneInPreview", "KeyboardEvents", "useKeyboardNavigation", "usePickerMainKeyboardEvents", "useSearchInputKeyboardEvents", "useSkinTonePickerKeyboardEvents", "useCategoryNavigationKeyboardEvents", "useBodyKeyboardEvents", "onKeyDown", "event", "Escape", "preventDefault", "focusSkinTonePicker", "setSkinToneFanOpenState", "goDownFromSearchInput", "useGoDownFromSearchInput", "isSkinToneInSearch", "ArrowRight", "ArrowDown", "Enter", "isOpen", "setIsOpen", "isSkinToneInPreview", "onType", "useOnType", "ArrowLeft", "focusNextSkinTone", "focusPrevSkinTone", "ArrowUp", "goUpFromBody", "useGoUpFromBody", "buttonFromTarget", "Space", "focusCategoryNavigation", "isSearchMode", "goUpFromEmoji", "exitLeft", "currentSkinTone", "hasNextElementSibling", "hasModifier", "match", "metaKey", "ctrl<PERSON>ey", "altKey", "preloadEmoji", "NATIVE", "preloadedEmojs", "emojiUrl", "preloadImage", "url", "image", "Image", "src", "useOnFocus", "onFocus", "button", "DEFAULT_LABEL_HEIGHT", "<PERSON><PERSON><PERSON><PERSON>", "PickerRootElement", "_ref2", "reactionsMode", "searchModeActive", "_ref3", "styleProps", "cx", "styles", "main", "baseVariables", "darkTheme", "AUTO", "autoThemeDark", "_cx", "searchActive", "reactionsMenu", "ref", "DarkTheme", "emojiPicker", "position", "flexDirection", "borderWidth", "borderStyle", "borderRadius", "borderColor", "backgroundColor", "boxSizing", "fontFamily", "autoTheme", "<PERSON><PERSON>ilter", "parentWidth", "getBoundingClientRect", "elementWidth", "Math", "floor", "elementLeft", "left", "parentLeft", "elementHeight", "elementTop", "parentTop", "round", "parentHeight", "getRowElements", "elements", "elementsInRow", "lastRow", "firstElementIndex", "lastElementIndex", "slice", "getNextRowElements", "allElements", "currentRow", "nextRow", "rowElements", "index", "nextRowElements", "prevRowElements", "firstVisibleElementInContainer", "maxVisibilityDiffThreshold", "parentBottom", "bottom", "parentTopWithLabel", "getLabelHeight", "visibleElements", "elementBottom", "maxVisibilityDiffPixels", "clientHeight", "elementTopWithAllowedDiff", "elementBottomWithAllowedDiff", "parentNode", "labels", "from", "querySelectorAll", "label", "_i", "_labels", "EmojiButtonSelector", "VisibleEmojiSelector", "visible", "emojiElement", "_emojiElement$closest", "originalUnified", "originalUnifiedFromEmojiElement", "unifiedFromEmojiElement", "isEmojiElement", "matches", "_element$parentElemen", "parentElement", "_element$clientHeight", "emojiTrueOffsetTop", "labelHeight", "elementOffsetTop", "categoryWithoutLabel", "querySelector", "_category$clientHeigh", "_categoryWithoutLabel", "_closestScrollBody$sc", "_closestScrollBody", "_element$closest", "emojiTruOffsetLeft", "elementOffsetLeft", "_element$offsetTop", "offsetTop", "_element$offsetLeft", "offsetLeft", "_elementDataSetKey", "elementDataSetKey", "allUnifiedFromEmojiElement", "_elementDataSet$key", "elementDataSet", "_element$dataset", "dataset", "isVisibleEmoji", "classList", "contains", "isHidden", "_allEmojis$slice", "last", "parseNative<PERSON><PERSON><PERSON>", "hex", "String", "fromCodePoint", "parseInt", "SUGGESTED_LS_KEY", "getSuggested", "mode", "_window$localStorage$", "_window2", "localStorage", "recent", "JSON", "parse", "getItem", "count", "_unused", "setSuggested", "existing", "u", "nextList", "i", "original", "min", "_window3", "setItem", "stringify", "_unused2", "isCustomCategory", "isCustomEmoji", "useMouseDownHandlers", "ContainerRef", "mouseDownTimerRef", "_useActiveSkinToneSta", "_useUpdateSuggested", "activeEmojiStyle", "onClick", "_emojiFromEvent", "emojiFromEvent", "skinToneToUse", "emojiClickOutput", "onMouseDown", "_emojiFromEvent2", "onMouseUp", "confainerRef", "getImageUrl", "imageUrl", "isCustom", "<PERSON><PERSON>", "props", "type", "cursor", "border", "background", "outline", "ClickableEmojiButton", "_ref$showVariations", "showVariations", "hasVariations", "_ref$noBackground", "noBackground", "getAriaLabel", "_emojiNames$", "alignItems", "justifyContent", "max<PERSON><PERSON><PERSON>", "maxHeight", "content", "right", "borderLeft", "borderRight", "transform", "borderBottom", "zIndex", "emojiStyles", "external", "fontSize", "common", "alignSelf", "justifySelf", "EmojiImg", "_ref$lazyLoad", "lazyLoad", "onError", "alt", "emojiImag", "loading", "min<PERSON><PERSON><PERSON>", "minHeight", "padding", "NativeEmoji", "native<PERSON><PERSON>ji", "lineHeight", "textAlign", "letterSpacing", "View<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "size", "_ref$getEmojiUrl", "_useEmojisThatFailedT", "setEmojisThatFailedToLoad", "emojiToRender", "ClickableEmoji", "BtnPlus", "setReactionsMode", "title", "tabIndex", "plusSign", "color", "backgroundImage", "Plus", "backgroundRepeat", "backgroundSize", "Reactions", "reactionsOpen", "list", "reaction", "emojiButton", "listStyle", "margin", "useOnScroll", "onScroll", "useIsEmojiHidden", "emojisThatFailedToLoad", "isEmojiFiltered", "failedToLoad", "filteredOut", "EmojiCategory", "categoryConfig", "categoryName", "gridGap", "gridTemplateColumns", "fontWeight", "textTransform", "isEverMounted", "useIsEverMounted", "isMounted", "setIsMounted", "Suggested", "suggestedEmojisModeConfig", "suggested", "_getSuggested", "suggestedItem", "EmojiList", "renderdCategoriesCountRef", "emojiList", "RenderCategory", "isEmojiHidden", "emojisToPush", "hiddenCounter", "_isEmojiHidden", "isDisallowed", "Direction", "EmojiVariationPicker", "_useVariationPickerTo", "useVariationPickerTop", "getTop", "getMenuDirection", "getPointerStyle", "usePointerStyle", "pointerStyle", "Down", "pointingUp", "pointer", "clientWidth", "direction", "Up", "emojiOffsetTop", "_bodyRef$scrollTop", "buttonHeight", "boxShadow", "transform<PERSON><PERSON>in", "backgroundPosition", "SVGTriangle", "Body", "PICKER", "body", "flex", "overflowY", "overflowX", "detectEmojyPartiallyBelowFold", "buttonRect", "bodyRect", "y", "useEmojiPreviewEvents", "allow", "setPreviewEmoji", "onEscape", "onMouseOver", "onEnter", "onLeave", "e", "_allUnifiedFromEmojiE", "relatedTarget", "belowFoldByPx", "handlePartiallyVisibleElementFocus", "_allUnifiedFromEmojiE2", "_document$activeEleme", "blur", "FlexDirection", "Flex", "_ref$style", "_ref$direction", "ROW", "_stylesheet$create", "COLUMN", "Absolute", "Relative", "BtnSkinToneVariation", "isActive", "skinToneVariation", "tone", "closedTone", "active", "ITEM_SIZE", "SkinTonePickerMenu", "SkinTonePicker", "SkinTonePickerDirection", "VERTICAL", "HORIZONTAL", "isDisabled", "setActiveSkinTone", "fullWidth", "expandedSize", "vertical", "skinTones", "verticalShadow", "flexBasis", "select", "Preview", "preview", "hideOnReactions", "PreviewBody", "previewEmoji", "variationPickerEmoji", "_previewEmoji$unified", "show", "PreviewContent", "defaultText", "borderTop", "categoryNameFromDom", "$category", "_$category$getAttribu", "getAttribute", "useActiveCategoryScrollDetection", "setActiveCategory", "visibleCategories", "Map", "observer", "IntersectionObserver", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "entry", "set", "intersectionRatio", "ratios", "lastCategory", "_ratios", "_ratios$_i", "ratio", "threshold", "el", "observe", "useScrollCategoryIntoView", "scrollCategoryIntoView", "_BodyRef$current", "useShouldHideCustomEmojis", "customCategoryConfig", "CategoryButton", "isActiveCategory", "allowNavigation", "catBtn", "role", "DarkActivePositionY", "DarkPositionY", "DarkInactivePosition", "SVGNavigation", "backgroundPositionX", "CategoryNavigation", "activeCategory", "categoriesConfig", "hideCustomCategory", "nav", "BtnClearSearch", "btnClearSearch", "icnClearnSearch", "HoverDark", "SVGTimes", "SCOPE", "EMOJI_BUTTON", "CATEGORY", "CssSearch", "q", "gen<PERSON><PERSON><PERSON>", "IcnSearch", "icnSearch", "SVGMagnifier", "SearchContainer", "overlay", "Search", "inc", "setInc", "placeholder", "autoFocus", "_useFilter", "input", "searchContainer", "search", "_event$target$value", "_event$target", "visuallyHidden", "clip", "clipPath", "whiteSpace", "Header", "EmojiPicker", "ContentControl", "renderAll", "setRenderAll", "ExpandedPickerContent", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_inherits<PERSON><PERSON>e", "_this", "call", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "_proto", "prototype", "componentDidCatch", "error", "errorInfo", "console", "render", "ExportedEmoji", "_ref$size", "_ref$emojiStyle", "EmojiPickerReact"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAYA,UAqBX;AArBD,WAAYA,UAAU;EACpBA,qDAAuC;EACvCA,gDAAkC;EAClCA,mCAAqB;EACrBA,qCAAuB;EACvBA,mCAAqB;EACrBA,iCAAmB;EACnBA,6CAA+B;EAC/BA,gDAAkC;EAClCA,4DAA8C;EAC9CA,6DAA+C;EAC/CA,qCAAuB;EACvBA,0CAA4B;EAC5BA,0CAA4B;EAC5BA,8CAAgC;EAChCA,+BAAiB;EACjBA,uCAAyB;EACzBA,2CAA6B;EAC7BA,4DAA8C;EAC9CA,0CAA4B;EAC5BA,0CAA4B;AAC9B,CAAC,EArBWA,UAAU,KAAVA,UAAU;SAuBNC,WAAWA;oCAAIC,UAAwB,OAAAC,KAAA,CAAAC,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;IAAxBH,UAAwB,CAAAG,IAAA,IAAAC,SAAA,CAAAD,IAAA;;EACrD,OAAOH,UAAU,CAACK,GAAG,CAAC,UAAAC,CAAC;IAAA,aAAQA,CAAC;GAAE,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAC9C;;ACpBO,IAAMC,UAAU,gBAAGC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;AAElD,IAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,MAAM;EACfC,OAAO,EAAE,GAAG;EACZC,aAAa,EAAE,MAAM;EACrBC,UAAU,EAAE,QAAQ;EACpBC,QAAQ,EAAE;CACX;AAED,AAAO,IAAMC,YAAY,gBAAGR,UAAU,CAACS,MAAM,CAAC;EAC5CP,MAAM,eAAAQ,QAAA;IACJ,GAAG,EAAEpB,UAAU,CAACY;KACbA,MAAM;CAEZ,CAAC;AAEF,AAAO,IAAMS,cAAc,gBAAGC,IAAU,CAAC,SAASD,cAAcA;EAC9D,OACEC;IACEC,wBAAwB;IACxBC,uBAAuB,EAAE;MAAEC,MAAM,EAAEf,UAAU,CAACgB,QAAQ;;IACtD;AAEN,CAAC,CAAC;AAEF,AAAO,IAAMC,uBAAuB,gBAAGjB,UAAU,CAACS,MAAM,CAAC;EACvD,WAAW,EAAE;IACX,qCAAqC,EAAE;MACrCS,WAAW,EAAE;QACX,QAAQ,EAAE;UACRd,OAAO,EAAE,GAAG;UACZe,mBAAmB,EAAE;;OAExB;MACDC,cAAc,eAAAV,QAAA;QACZ,GAAG,EAAEpB,UAAU,CAAC8B;SACblB,MAAM;KAEZ;IACD,+BAA+B,EAAE;MAC/BmB,mBAAmB,EAAEnB;;GAExB;EACDoB,iBAAiB,EAAE;IACjBC,UAAU,EAAE;GACb;EACD,gBAAgB,EAAE;IAChBD,iBAAiB,EAAE;MACjBE,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE,KAAK;MACZrB,OAAO,EAAE,GAAG;MACZC,aAAa,EAAE,MAAM;MACrBE,QAAQ,EAAE;;GAEb;EACD,2CAA2C,EAAE;IAC3CW,WAAW,EAAE;MACX,QAAQ,EAAE;QACRd,OAAO,EAAE,GAAG;QACZe,mBAAmB,EAAE;OACtB;MACD,cAAc,EAAE;QACdf,OAAO,EAAE,GAAG;QACZe,mBAAmB,EAAE;;KAExB;IACDE,mBAAmB,eAAAX,QAAA;MACjB,GAAG,EAAE;OACFR,MAAM;;CAGd,CAAC;AAEF,SAAgBwB,QAAQA,CAACC,GAAW,EAAEC,KAAa;;EACjD,OAAO;IACL,iBAAiB,GAAAC,aAAA,OAAAA,aAAA,CACdF,GAAG,IAAGC,KAAK,EAAAC,aAAA,CACb;IACD,iBAAiB,GAAAC,aAAA,OAAAA,aAAA,CACdH,GAAG,IAAG;MACL,qCAAqC,EAAEC;KACxC,EAAAE,aAAA;GAEJ;AACH;;ACxFA;AACA,SAAgBC,aAAaA,CAACC,IAAkB,EAAEC,IAAkB;;EAClE,IAAMC,gBAAgB,IAAAC,kBAAA,GAAGH,IAAI,CAACI,YAAY,YAAAD,kBAAA,GAAI,EAAE;EAChD,IAAME,gBAAgB,IAAAC,kBAAA,GAAGL,IAAI,CAACG,YAAY,YAAAE,kBAAA,GAAI,EAAE;EAChD,OACEN,IAAI,CAACO,IAAI,KAAKN,IAAI,CAACM,IAAI,IACvBP,IAAI,CAACQ,YAAY,KAAKP,IAAI,CAACO,YAAY,IACvCR,IAAI,CAACS,oBAAoB,KAAKR,IAAI,CAACQ,oBAAoB,IACvDT,IAAI,CAACU,iBAAiB,KAAKT,IAAI,CAACS,iBAAiB,IACjDV,IAAI,CAACW,iBAAiB,KAAKV,IAAI,CAACU,iBAAiB,IACjDX,IAAI,CAACY,eAAe,KAAKX,IAAI,CAACW,eAAe,IAC7CZ,IAAI,CAACa,iBAAiB,KAAKZ,IAAI,CAACY,iBAAiB,IACjDb,IAAI,CAACc,eAAe,KAAKb,IAAI,CAACa,eAAe,IAC7Cd,IAAI,CAACe,UAAU,KAAKd,IAAI,CAACc,UAAU,IACnCf,IAAI,CAACgB,KAAK,KAAKf,IAAI,CAACe,KAAK,IACzBhB,IAAI,CAACiB,mBAAmB,KAAKhB,IAAI,CAACgB,mBAAmB,IACrDjB,IAAI,CAACkB,cAAc,KAAKjB,IAAI,CAACiB,cAAc,IAC3ClB,IAAI,CAACmB,SAAS,KAAKlB,IAAI,CAACkB,SAAS,IACjCnB,IAAI,CAACR,MAAM,KAAKS,IAAI,CAACT,MAAM,IAC3BQ,IAAI,CAACP,KAAK,KAAKQ,IAAI,CAACR,KAAK,IACzBO,IAAI,CAACoB,KAAK,KAAKnB,IAAI,CAACmB,KAAK,IACzBpB,IAAI,CAACqB,cAAc,KAAKpB,IAAI,CAACoB,cAAc,IAC3CrB,IAAI,CAACsB,sBAAsB,KAAKrB,IAAI,CAACqB,sBAAsB,IAC3DpB,gBAAgB,CAACqB,MAAM,KAAKlB,gBAAgB,CAACkB,MAAM;AAEvD;;AC3BO,IAAMC,iBAAiB,GAAG,CAC/B,OAAO,EACP,WAAW,EACX,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO;AAAC,CACT;;ICGWC,cAGX;AAHD,WAAYA,cAAc;EACxBA,mCAAiB;EACjBA,uCAAqB;AACvB,CAAC,EAHWA,cAAc,KAAdA,cAAc;AAK1B,IAAYC,UAMX;AAND,WAAYA,UAAU;EACpBA,+BAAiB;EACjBA,6BAAe;EACfA,iCAAmB;EACnBA,+BAAiB;EACjBA,mCAAqB;AACvB,CAAC,EANWA,UAAU,KAAVA,UAAU;AAQtB,IAAYC,KAIX;AAJD,WAAYA,KAAK;EACfA,sBAAa;EACbA,wBAAe;EACfA,sBAAa;AACf,CAAC,EAJWA,KAAK,KAALA,KAAK;AAMjB,IAAYC,SAOX;AAPD,WAAYA,SAAS;EACnBA,gCAAmB;EACnBA,4BAAe;EACfA,mCAAsB;EACtBA,6BAAgB;EAChBA,kCAAqB;EACrBA,2BAAc;AAChB,CAAC,EAPWA,SAAS,KAATA,SAAS;AASrB,IAAYC,UAWX;AAXD,WAAYA,UAAU;EACpBA,qCAAuB;EACvBA,+BAAiB;EACjBA,+CAAiC;EACjCA,+CAAiC;EACjCA,uCAAyB;EACzBA,6CAA+B;EAC/BA,uCAAyB;EACzBA,iCAAmB;EACnBA,iCAAmB;EACnBA,6BAAe;AACjB,CAAC,EAXWA,UAAU,KAAVA,UAAU;AAatB,IAAYC,sBAGX;AAHD,WAAYA,sBAAsB;EAChCA,2CAAiB;EACjBA,6CAAmB;AACrB,CAAC,EAHWA,sBAAsB,KAAtBA,sBAAsB;;;ACpDlC,AAIA,IAAMC,iBAAiB,GAAiB,CACtCF,UAAU,CAACG,SAAS,EACpBH,UAAU,CAACI,MAAM,EACjBJ,UAAU,CAACK,cAAc,EACzBL,UAAU,CAACM,cAAc,EACzBN,UAAU,CAACO,UAAU,EACrBP,UAAU,CAACQ,aAAa,EACxBR,UAAU,CAACS,UAAU,EACrBT,UAAU,CAACU,OAAO,EAClBV,UAAU,CAACW,OAAO,EAClBX,UAAU,CAACY,KAAK,CACjB;AAED,AAAO,IAAMC,eAAe,GAAmB;EAC7CC,IAAI,EAAE,eAAe;EACrBC,QAAQ,EAAEf,UAAU,CAACG;CACtB;AAOD,IAAMa,gBAAgB,IAAAC,iBAAA,OAAAA,iBAAA,CACnBjB,UAAU,CAACG,SAAS,IAAG;EACtBY,QAAQ,EAAEf,UAAU,CAACG,SAAS;EAC9BW,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACI,MAAM,IAAG;EACnBW,QAAQ,EAAEf,UAAU,CAACI,MAAM;EAC3BU,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACK,cAAc,IAAG;EAC3BU,QAAQ,EAAEf,UAAU,CAACK,cAAc;EACnCS,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACM,cAAc,IAAG;EAC3BS,QAAQ,EAAEf,UAAU,CAACM,cAAc;EACnCQ,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACO,UAAU,IAAG;EACvBQ,QAAQ,EAAEf,UAAU,CAACO,UAAU;EAC/BO,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACQ,aAAa,IAAG;EAC1BO,QAAQ,EAAEf,UAAU,CAACQ,aAAa;EAClCM,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACS,UAAU,IAAG;EACvBM,QAAQ,EAAEf,UAAU,CAACS,UAAU;EAC/BK,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACU,OAAO,IAAG;EACpBK,QAAQ,EAAEf,UAAU,CAACU,OAAO;EAC5BI,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACW,OAAO,IAAG;EACpBI,QAAQ,EAAEf,UAAU,CAACW,OAAO;EAC5BG,IAAI,EAAE;CACP,EAAAG,iBAAA,CACAjB,UAAU,CAACY,KAAK,IAAG;EAClBG,QAAQ,EAAEf,UAAU,CAACY,KAAK;EAC1BE,IAAI,EAAE;CACP,EAAAG,iBAAA,CACF;AAED,SAAgBC,oBAAoBA,CAClCC,SAA8C;EAE9C,OAAOjB,iBAAiB,CAAClE,GAAG,CAAC,UAAA+E,QAAQ;IACnC,OAAAlE,QAAA,KACKmE,gBAAgB,CAACD,QAAQ,CAAC,EACzBI,SAAS,IAAIA,SAAS,CAACJ,QAAQ,CAAC,IAAII,SAAS,CAACJ,QAAQ,CAAC;GAE9D,CAAC;AACJ;AAEA,SAAgBK,0BAA0BA,CAACL,QAAwB;EACjE,OAAOA,QAAQ,CAACA,QAAQ;AAC1B;AAEA,SAAgBM,8BAA8BA,CAACN,QAAwB;EACrE,OAAOA,QAAQ,CAACD,IAAI;AACtB;AAWA,SAAgBQ,qBAAqBA,CACnCC,sBACAJ;;MADAI;IAAAA,uBAA2C,EAAE;;EAAA,IAC7CJ;IAAAA,YAAqC,EAAE;;EAEvC,IAAMK,KAAK,GAAG,EAAwC;EAEtD,IAAIL,SAAS,CAACM,cAAc,KAAK7B,cAAc,CAAC8B,MAAM,EAAE;IACtDF,KAAK,CAACxB,UAAU,CAACG,SAAS,CAAC,GAAGU,eAAe;;EAG/C,IAAMc,IAAI,GAAGT,oBAAoB,CAACM,KAAK,CAAC;EACxC,IAAI,GAAAI,qBAAA,GAACL,oBAAoB,aAApBK,qBAAA,CAAsBlC,MAAM,GAAE;IACjC,OAAOiC,IAAI;;EAGb,OAAOJ,oBAAoB,CAACvF,GAAG,CAAC,UAAA+E,QAAQ;IACtC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOc,uBAAuB,CAACd,QAAQ,EAAES,KAAK,CAACT,QAAQ,CAAC,CAAC;;IAG3D,OAAAlE,QAAA,KACKgF,uBAAuB,CAACd,QAAQ,CAACA,QAAQ,EAAES,KAAK,CAACT,QAAQ,CAACA,QAAQ,CAAC,CAAC,EACpEA,QAAQ;GAEd,CAAC;AACJ;AAEA,SAASc,uBAAuBA,CAC9Bd,QAAoB,EACpBe;MAAAA;IAAAA,WAA2B,EAAoB;;EAE/C,OAAOC,MAAM,CAACC,MAAM,CAAChB,gBAAgB,CAACD,QAAQ,CAAC,EAAEe,QAAQ,CAAC;AAC5D;;AChIA,IAAMG,aAAa,GACjB,mEAAmE;AACrE,IAAMC,gBAAgB,GACpB,yEAAyE;AAC3E,IAAMC,eAAe,GACnB,uEAAuE;AACzE,IAAMC,cAAc,GAClB,qEAAqE;AAEvE,SAAgBC,MAAMA,CAACnD,UAAsB;EAC3C,QAAQA,UAAU;IAChB,KAAKW,UAAU,CAACyC,OAAO;MACrB,OAAOH,eAAe;IACxB,KAAKtC,UAAU,CAAC0C,MAAM;MACpB,OAAOH,cAAc;IACvB,KAAKvC,UAAU,CAAC2C,QAAQ;MACtB,OAAON,gBAAgB;IACzB,KAAKrC,UAAU,CAAC4C,KAAK;IACrB;MACE,OAAOR,aAAa;;AAE1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBA,IAAMS,kBAAkB,GAAG,CACzB3C,SAAS,CAAC4C,OAAO,EACjB5C,SAAS,CAAC6C,KAAK,EACf7C,SAAS,CAAC8C,YAAY,EACtB9C,SAAS,CAAC+C,MAAM,EAChB/C,SAAS,CAACgD,WAAW,EACrBhD,SAAS,CAACiD,IAAI,CACf;AAED,AAAO,IAAMC,cAAc,gBAAGlB,MAAM,CAACmB,OAAO,CAACnD,SAAS,CAAC,CAACoD,MAAM,CAC5D,UAACC,GAAG,EAAAC,IAAA;MAAGvF,GAAG,GAAAuF,IAAA;IAAEtF,KAAK,GAAAsF,IAAA;EACfD,GAAG,CAACrF,KAAK,CAAC,GAAGD,GAAG;EAChB,OAAOsF,GAAG;AACZ,CAAC,EACD,EAA4B,CAC7B;AAED,AAAO,IAAME,eAAe,gBAGxBZ,kBAAkB,CAACS,MAAM,CAC3B,UAACI,MAAM,EAAEC,QAAQ;EAAA,IAAAC,cAAA;EAAA,OACf1B,MAAM,CAACC,MAAM,CAACuB,MAAM,GAAAE,cAAA,OAAAA,cAAA,CACjBD,QAAQ,IAAGA,QAAQ,EAAAC,cAAA,EACpB;AAAA,GACJ,EAAE,CACH;;AC1BD,IAAYC,eAMX;AAND,WAAYA,eAAe;EACzBA,6BAAU;EACVA,gCAAa;EACbA,mCAAgB;EAChBA,iCAAc;EACdA,oCAAiB;AACnB,CAAC,EANWA,eAAe,KAAfA,eAAe;;ACCpB,IAAMC,sBAAsB,GAAc,EAAE;AAEnDC,UAAU,CAAC;EACTC,SAAS,CAACV,MAAM,CAAC,UAACW,WAAW,EAAEC,KAAK;IAClCC,UAAU,CAACD,KAAK,CAAC;IACjB,OAAOD,WAAW;GACnB,EAAEH,sBAAmC,CAAC;AACzC,CAAC,CAAC;AAIF,SAAgBK,UAAUA,CAACD,KAAgB;EACzC,IAAME,gBAAgB,GAAGC,UAAU,CAACH,KAAK,CAAC,CACvCI,IAAI,EAAE,CACNjI,IAAI,CAAC,EAAE,CAAC,CACRkI,WAAW,EAAE,CACbC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAC3BC,KAAK,CAAC,EAAE,CAAC;EAEZL,gBAAgB,CAACM,OAAO,CAAC,UAAAC,KAAI;;IAC3Bb,sBAAsB,CAACa,KAAI,CAAC,IAAAC,qBAAA,GAAGd,sBAAsB,CAACa,KAAI,CAAC,YAAAC,qBAAA,GAAI,EAAE;IAEjEd,sBAAsB,CAACa,KAAI,CAAC,CAACE,YAAY,CAACX,KAAK,CAAC,CAAC,GAAGA,KAAK;GAC1D,CAAC;AACJ;;SCfgBG,UAAUA,CAACH,KAAe;;EACxC,QAAAY,qBAAA,GAAOZ,KAAK,CAACL,eAAe,CAAC5C,IAAI,CAAC,YAAA6D,qBAAA,GAAI,EAAE;AAC1C;AAEA,SAAgBC,OAAOA,CAACb,KAAgB;EACtC,OAAOc,UAAU,CAACd,KAAK,CAACL,eAAe,CAACoB,QAAQ,CAAC,CAAC;AACpD;AAEA,SAAgBC,SAASA,CAAChB,KAAgB;EACxC,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,EAAE;;EAGX,OAAOG,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7B;AAEA,SAAgBiB,sBAAsBA,CAACC,OAAe;EACpD,IAAMC,KAAK,GAAGD,OAAO,CAACX,KAAK,CAAC,GAAG,CAAC;EAChC,IAAAa,aAAA,GAAmBD,KAAK,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAA9B5B,QAAQ,GAAA2B,aAAA;EAEf,IAAI7B,eAAe,CAACE,QAAQ,CAAC,EAAE;IAC7B,OAAO0B,KAAK,CAAChJ,IAAI,CAAC,GAAG,CAAC;;EAGxB,OAAO+I,OAAO;AAChB;AAEA,SAAgBP,YAAYA,CAACX,KAAgB,EAAEP,QAAiB;;EAC9D,IAAMyB,OAAO,GAAGlB,KAAK,CAACL,eAAe,CAACuB,OAAO,CAAC;EAE9C,IAAI,CAACzB,QAAQ,IAAI,CAAC6B,kBAAkB,CAACtB,KAAK,CAAC,EAAE;IAC3C,OAAOkB,OAAO;;EAGhB,QAAAK,qBAAA,GAAOC,qBAAqB,CAACxB,KAAK,EAAEP,QAAQ,CAAC,YAAA8B,qBAAA,GAAIL,OAAO;AAC1D;AAEA,SAAgBO,gBAAgBA,CAACzE,QAAoB;;;EAEnD,QAAA0E,gBAAA,GAAOC,MAAM,oBAANA,MAAM,CAAG3E,QAAQ,CAAC,YAAA0E,gBAAA,GAAI,EAAE;AACjC;AAEA;AACA,SAAgBE,iBAAiBA,CAC/BV,OAAe,EACf/F,UAAsB;EAEtB,YAAUmD,MAAM,CAACnD,UAAU,CAAC,GAAG+F,OAAO;AACxC;AAEA,SAAgBW,eAAeA,CAAC7B,KAAgB;;EAC9C,QAAA8B,sBAAA,GAAO9B,KAAK,CAACL,eAAe,CAACoC,UAAU,CAAC,YAAAD,sBAAA,GAAI,EAAE;AAChD;AAEA,SAAgBR,kBAAkBA,CAACtB,KAAgB;EACjD,OAAO6B,eAAe,CAAC7B,KAAK,CAAC,CAACrE,MAAM,GAAG,CAAC;AAC1C;AAEA,SAAgB6F,qBAAqBA,CACnCxB,KAAgB,EAChBP,QAAiB;EAEjB,OAAOA,QAAQ,GACXoC,eAAe,CAAC7B,KAAK,CAAC,CAACgC,IAAI,CAAC,UAAAC,SAAS;IAAA,OAAIA,SAAS,CAACC,QAAQ,CAACzC,QAAQ,CAAC;IAAC,GACtEkB,YAAY,CAACX,KAAK,CAAC;AACzB;AAEA,SAAgBmC,cAAcA,CAACjB,OAAgB;EAC7C,IAAI,CAACA,OAAO,EAAE;IACZ;;EAGF,IAAIkB,kBAAkB,CAAClB,OAAO,CAAC,EAAE;IAC/B,OAAOkB,kBAAkB,CAAClB,OAAO,CAAC;;EAGpC,IAAMmB,eAAe,GAAGpB,sBAAsB,CAACC,OAAO,CAAC;EACvD,OAAOkB,kBAAkB,CAACC,eAAe,CAAC;AAC5C;AAEA,AAAO,IAAMvC,SAAS,gBAAe9B,MAAM,CAACsE,MAAM,CAACX,MAAM,CAAC,CAACvB,IAAI,EAAE;AAEjE,SAAgBmC,eAAeA,CAAC/H,YAA2B;EACzDmH,MAAM,CAAC1F,UAAU,CAACI,MAAM,CAAC,CAACV,MAAM,GAAG,CAAC;EAEpCnB,YAAY,CAACgG,OAAO,CAAC,UAAAR,KAAK;IACxB,IAAMwC,SAAS,GAAGC,oBAAoB,CAACzC,KAAK,CAAC;IAE7C2B,MAAM,CAAC1F,UAAU,CAACI,MAAM,CAAC,CAACqG,IAAI,CAACF,SAAkB,CAAC;IAElD,IAAIJ,kBAAkB,CAACI,SAAS,CAAC7C,eAAe,CAACuB,OAAO,CAAC,CAAC,EAAE;MAC1D;;IAGFpB,SAAS,CAAC4C,IAAI,CAACF,SAAS,CAAC;IACzBJ,kBAAkB,CAACI,SAAS,CAAC7C,eAAe,CAACuB,OAAO,CAAC,CAAC,GAAGsB,SAAS;IAClEvC,UAAU,CAACuC,SAAS,CAAC;GACtB,CAAC;AACJ;AAEA,SAASC,oBAAoBA,CAACzC,KAAkB;;EAC9C,OAAAV,IAAA,OAAAA,IAAA,CACGK,eAAe,CAAC5C,IAAI,IAAGiD,KAAK,CAAC2C,KAAK,CAAC1K,GAAG,CAAC,UAAA8E,IAAI;IAAA,OAAIA,IAAI,CAACsD,WAAW,EAAE;IAAC,EAAAf,IAAA,CAClEK,eAAe,CAACuB,OAAO,IAAGlB,KAAK,CAAC4C,EAAE,CAACvC,WAAW,EAAE,EAAAf,IAAA,CAChDK,eAAe,CAACoB,QAAQ,IAAG,GAAG,EAAAzB,IAAA,CAC9BK,eAAe,CAACkD,MAAM,IAAG7C,KAAK,CAAC6C,MAAM,EAAAvD,IAAA;AAE1C;AAEA,IAAM8C,kBAAkB,GAEpB,EAAE;AAENvC,UAAU,CAAC;EACTC,SAAS,CAACV,MAAM,CAAC,UAACU,SAAS,EAAEgD,KAAK;IAChChD,SAAS,CAACa,YAAY,CAACmC,KAAK,CAAC,CAAC,GAAGA,KAAK;IAEtC,IAAIxB,kBAAkB,CAACwB,KAAK,CAAC,EAAE;MAC7BjB,eAAe,CAACiB,KAAK,CAAC,CAACtC,OAAO,CAAC,UAAAyB,SAAS;QACtCnC,SAAS,CAACmC,SAAS,CAAC,GAAGa,KAAK;OAC7B,CAAC;;IAGJ,OAAOhD,SAAS;GACjB,EAAEsC,kBAAkB,CAAC;AACxB,CAAC,CAAC;AAEF,SAAgBW,0BAA0BA,CAAC7B,OAAe;EACxD,IAAA8B,cAAA,GAA8B9B,OAAO,CAACX,KAAK,CAAC,GAAG,CAAwB;IAA9D0C,iBAAiB,GAAAD,cAAA;EAC1B,OAAOrE,kBAAkB,CAACuD,QAAQ,CAACe,iBAAiB,CAAC,GACjDA,iBAAiB,GACjB,IAAI;AACV;;ACxHA,IAAMC,oBAAoB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC;AAEpE,AAAO,IAAMC,0BAA0B,GAAG,QAAQ;AAClD,AAAO,IAAMC,+BAA+B,GAAG,kBAAkB;AACjE,AAAO,IAAMC,qBAAqB,GAChC,iDAAiD;AACnD,AAAO,IAAMC,+BAA+B,GAC1C,UAAU,GAAGD,qBAAqB;AACpC,AAAO,IAAME,qCAAqC,GAChD,YAAY,GAAGF,qBAAqB;AAEtC,SAAgBG,WAAWA,CACzBC;;MAAAA;IAAAA,aAA2B,EAAE;;EAE7B,IAAM7F,IAAI,GAAG8F,gBAAgB,EAAE;EAE/B,IAAMC,aAAa,GAAG3F,MAAM,CAACC,MAAM,CACjCL,IAAI,CAAC+F,aAAa,GAAAC,qBAAA,GAClBH,UAAU,CAACE,aAAa,YAAAC,qBAAA,GAAI,EAAE,CAC/B;EACD,IAAMC,MAAM,GAAG7F,MAAM,CAACC,MAAM,CAACL,IAAI,EAAE6F,UAAU,CAAC;EAE9C,IAAMK,UAAU,GAAGvG,qBAAqB,CAACkG,UAAU,CAACK,UAAU,EAAE;IAC9DpG,cAAc,EAAEmG,MAAM,CAACxI;GACxB,CAAC;EAEFwI,MAAM,CAACE,YAAY,CAACvD,OAAO,CAAC,UAACR,KAAK;IAChC6D,MAAM,CAACG,aAAa,CAACC,GAAG,CAACjE,KAAK,CAAC;GAChC,CAAC;EAEFuC,eAAe,EAAA2B,oBAAA,GAACL,MAAM,CAACrJ,YAAY,YAAA0J,oBAAA,GAAI,EAAE,CAAC;EAE1C,IAAMxI,sBAAsB,GAAGmI,MAAM,CAACpI,cAAc,GAChDS,sBAAsB,CAACiI,OAAO,GAC9BN,MAAM,CAACnI,sBAAsB;EAEjC,OAAA5C,QAAA,KACK+K,MAAM;IACTC,UAAU,EAAVA,UAAU;IACVH,aAAa,EAAbA,aAAa;IACbjI,sBAAsB,EAAtBA;;AAEJ;AAEA,SAAgBgI,gBAAgBA;EAC9B,OAAO;IACLxI,eAAe,EAAE,IAAI;IACrB4I,UAAU,EAAE3G,oBAAoB,EAAE;IAClC5B,SAAS,EAAE,EAAE;IACbf,YAAY,EAAE,EAAE;IAChBQ,eAAe,EAAEgB,SAAS,CAAC4C,OAAO;IAClCzD,UAAU,EAAEW,UAAU,CAAC4C,KAAK;IAC5B9D,YAAY,EAAE,IAAI;IAClBwJ,WAAW,EAAExC,iBAAiB;IAC9BhI,MAAM,EAAE,GAAG;IACX0B,cAAc,EAAE,KAAK;IACrBqI,aAAa,EAAA7K,QAAA,KACRuL,iBAAiB,CACrB;IACD5I,cAAc,EAAE,KAAK;IACrBX,iBAAiB,EAAEqI,0BAA0B;IAC7CpI,iBAAiB,EAAEoI,0BAA0B;IAC7CzH,sBAAsB,EAAEQ,sBAAsB,CAACoI,MAAM;IACrDrJ,iBAAiB,EAAE,KAAK;IACxBO,KAAK,EAAE,EAAE;IACTH,mBAAmB,EAAEQ,cAAc,CAAC0I,QAAQ;IAC5CnJ,KAAK,EAAEW,KAAK,CAAC8C,KAAK;IAClBmF,aAAa,EAAE,IAAIQ,GAAG,CAAStB,oBAAoB,CAAC;IACpDrJ,KAAK,EAAE,GAAG;IACVgB,oBAAoB,EAAE,KAAK;IAC3B4J,SAAS,EAAE7I,iBAAiB;IAC5BjB,IAAI,EAAE,IAAI;IACV+J,oBAAoB,EAAE,IAAI;IAC1BX,YAAY,EAAE;GACf;AACH;AAqCA,IAAMM,iBAAiB,GAAkB;EACvCM,YAAY,EAAE,OAAO;EACrBC,cAAc,EAAE,mBAAmB;EACnCC,WAAW,EAAE;CACd;;;AC5ID,AAeA,IAAMC,aAAa,gBAAG9L,aAAmB,eACvC0K,gBAAgB,EAAE,CACnB;AAED,SAAgBqB,oBAAoBA,CAAAzF,IAAA;MAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;IAAKnB,MAAM,GAAAoB,6BAAA,CAAA3F,IAAA,EAAA4F,SAAA;EACxD,IAAMC,YAAY,GAAGC,YAAY,CAACvB,MAAM,CAAC;EAEzC,OACE7K,cAAC8L,aAAa,CAACO,QAAQ;IAACrL,KAAK,EAAEmL;KAC5BH,QAAQ,CACc;AAE7B;AAEA,SAAgBI,YAAYA,CAACvB,MAAoB;;EAC/C,IAAAyB,eAAA,GAAwCtM,QAAc,CAAC;MAAA,OACrDwK,WAAW,CAACK,MAAM,CAAC;MACpB;IAFMsB,YAAY,GAAAG,eAAA;IAAEC,eAAe,GAAAD,eAAA;EAIpCtM,SAAe,CAAC;IACd,IAAImB,aAAa,CAACgL,YAAY,EAAEtB,MAAM,CAAC,EAAE;MACvC;;IAEF0B,eAAe,CAAC/B,WAAW,CAACK,MAAM,CAAC,CAAC;;;GAGrC,EAAE,EAAAK,oBAAA,GACDL,MAAM,CAACrJ,YAAY,qBAAnB0J,oBAAA,CAAqBvI,MAAM,EAC3BkI,MAAM,CAAClJ,IAAI,EACXkJ,MAAM,CAACjJ,YAAY,EACnBiJ,MAAM,CAAChJ,oBAAoB,EAC3BgJ,MAAM,CAAC/I,iBAAiB,EACxB+I,MAAM,CAAC9I,iBAAiB,EACxB8I,MAAM,CAAC7I,eAAe,EACtB6I,MAAM,CAAC5I,iBAAiB,EACxB4I,MAAM,CAAC3I,eAAe,EACtB2I,MAAM,CAAC1I,UAAU,EACjB0I,MAAM,CAACzI,KAAK,EACZyI,MAAM,CAACxI,mBAAmB,EAC1BwI,MAAM,CAACvI,cAAc,EACrBuI,MAAM,CAACtI,SAAS,EAChBsI,MAAM,CAACjK,MAAM,EACbiK,MAAM,CAAChK,KAAK,EACZgK,MAAM,CAACpI,cAAc,EACrBoI,MAAM,CAACnI,sBAAsB,EAC7BmI,MAAM,CAACa,oBAAoB,CAC5B,CAAC;EAEF,OAAOS,YAAY;AACrB;AAEA,SAAgBK,eAAeA;EAC7B,OAAOxM,UAAgB,CAAC8L,aAAa,CAAC;AACxC;;SClEgBW,iBAAiBA,CAC/BC,YAAe,EACfC;MAAAA;IAAAA,QAAgB,CAAC;;EAEjB,IAAAC,SAAA,GAA0BC,QAAQ,CAAIH,YAAY,CAAC;IAA5CI,KAAK,GAAAF,SAAA;IAAEG,QAAQ,GAAAH,SAAA;EACtB,IAAMI,KAAK,GAAGC,MAAM,CAAgB,IAAI,CAAC;EAEzC,SAASC,iBAAiBA,CAAClM,KAAQ;IACjC,OAAO,IAAImM,OAAO,CAAI,UAAAC,OAAO;;MAC3B,IAAIJ,KAAK,CAACK,OAAO,EAAE;QACjBC,YAAY,CAACN,KAAK,CAACK,OAAO,CAAC;;MAG7BL,KAAK,CAACK,OAAO,IAAAE,OAAA,GAAGC,MAAM,qBAAND,OAAA,CAAQ1G,UAAU,CAAC;QACjCkG,QAAQ,CAAC/L,KAAK,CAAC;QACfoM,OAAO,CAACpM,KAAK,CAAC;OACf,EAAE2L,KAAK,CAAC;KACV,CAAC;;EAGJ,OAAO,CAACG,KAAK,EAAEI,iBAAiB,CAAC;AACnC;;SCrBgBO,kBAAkBA;EAC9B,IAAMzC,aAAa,GAAG0C,gBAAgB,EAAE;EACxC,OAAO,UAAC/F,YAAoB;IAAA,OAAKqD,aAAa,CAAC2C,GAAG,CAAChG,YAAY,CAAC;;AAClE;;SCQciG,mBAAmBA;EACjC,IAAMC,mBAAmB,GAAGZ,MAAM,CAA0B,EAAE,CAAC;EAC/D,IAAMa,kBAAkB,GAAGC,qBAAqB,EAAE;EAElD,OAAOC,OAAO,CAAC;IACb,IAAMpM,YAAY,GAAGkG,UAAU,MAAIgG,kBAAoB,CAAC;IAExD,IAAI,CAACA,kBAAkB,IAAIG,MAAM,CAACC,KAAK,CAACtM,YAAY,CAAC,EAAE;MACrD,OAAOiM,mBAAmB,CAACR,OAAO;;IAGpC,OAAOvG,SAAS,CAACV,MAAM,CAAC,UAAC+H,gBAAgB,EAAEnH,KAAK;MAC9C,IAAIoH,mBAAmB,CAACpH,KAAK,EAAEpF,YAAY,CAAC,EAAE;QAC5CuM,gBAAgB,CAACxG,YAAY,CAACX,KAAK,CAAC,CAAC,GAAG,IAAI;;MAG9C,OAAOmH,gBAAgB;KACxB,EAAEN,mBAAmB,CAACR,OAAO,CAAC;GAChC,EAAE,CAACS,kBAAkB,CAAC,CAAC;AAC1B;AAEA,SAAgBO,oBAAoBA;EAClC,IAAMF,gBAAgB,GAAGP,mBAAmB,EAAE;EAC9C,IAAMU,eAAe,GAAGb,kBAAkB,EAAE;EAE5C,OAAO,SAASc,iBAAiBA,CAACvH,KAAgB;IAChD,IAAMkB,OAAO,GAAGD,sBAAsB,CAACN,YAAY,CAACX,KAAK,CAAC,CAAC;IAE3D,OAAOwH,OAAO,CAACL,gBAAgB,CAACjG,OAAO,CAAC,IAAIoG,eAAe,CAACpG,OAAO,CAAC,CAAC;GACtE;AACH;AAEA,SAASkG,mBAAmBA,CAC1BpH,KAAgB,EAChByH,cAAsB;EAEtB,OAAO5G,OAAO,CAACb,KAAK,CAAC,GAAGyH,cAAc;AACxC;;SC/CgBC,kBAAkBA,CAChCC,QAAuD;EAEvDC,SAAS,CAAC;IACRD,QAAQ,CAAC,IAAI,CAAC;GACf,EAAE,CAACA,QAAQ,CAAC,CAAC;AAChB;;SCMgBE,qBAAqBA,CAAAvI,IAAA;MAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;EAC9C,IAAMmC,gBAAgB,GAAGP,mBAAmB,EAAE;EAC9C,IAAM5L,eAAe,GAAG8M,wBAAwB,EAAE;EAClD,IAAMjN,oBAAoB,GAAGkN,sBAAsB,EAAE;;EAGrD,IAAMC,SAAS,GAAGhP,MAAY,CAAc4G,sBAAsB,CAAC;EACnE,IAAMqI,gBAAgB,GAAGjP,MAAY,CAAU,KAAK,CAAC;EACrD,IAAMkP,gBAAgB,GAAGlP,MAAY,CAAU,KAAK,CAAC;EACrD,IAAMmP,mBAAmB,GAAGnP,MAAY,CACtCmO,gBAAgB,CACjB;EAED,IAAMiB,oBAAoB,GAAG3C,iBAAiB,CAAC4C,IAAI,CAACC,GAAG,EAAE,EAAE,GAAG,CAAC;EAC/D,IAAMC,UAAU,GAAG9C,iBAAiB,CAAC,EAAE,EAAE,GAAG,CAAC;EAC7C,IAAM+C,oBAAoB,GAAG3C,QAAQ,CAAU,KAAK,CAAC;EACrD,IAAM4C,cAAc,GAAG5C,QAAQ,CAAY7K,eAAe,CAAC;EAC3D,IAAM0N,mBAAmB,GAAG7C,QAAQ,CAAsB,IAAI,CAAC;EAC/D,IAAM8C,2BAA2B,GAAG9C,QAAQ,CAAc,IAAIrB,GAAG,EAAE,CAAC;EACpE,IAAMoE,yBAAyB,GAAG/C,QAAQ,CAAmB,IAAI,CAAC;EAClE,IAAMgD,kBAAkB,GAAGhD,QAAQ,CAAChL,oBAAoB,CAAC;EACzD,IAAA+K,SAAA,GAAkDC,QAAQ,CAAC,KAAK,CAAC;IAA1DiD,iBAAiB,GAAAlD,SAAA;IAAEmD,oBAAoB,GAAAnD,SAAA;EAE9C8B,kBAAkB,CAACqB,oBAAoB,CAAC;EAExC,OACE/P,cAACgQ,aAAa,CAAC3D,QAAQ;IACrBrL,KAAK,EAAE;MACL0O,mBAAmB,EAAnBA,mBAAmB;MACnBD,cAAc,EAAdA,cAAc;MACdR,gBAAgB,EAAhBA,gBAAgB;MAChBC,gBAAgB,EAAhBA,gBAAgB;MAChBC,mBAAmB,EAAnBA,mBAAmB;MACnBS,yBAAyB,EAAzBA,yBAAyB;MACzBD,2BAA2B,EAA3BA,2BAA2B;MAC3BX,SAAS,EAATA,SAAS;MACTc,iBAAiB,EAAjBA,iBAAiB;MACjBP,UAAU,EAAVA,UAAU;MACVC,oBAAoB,EAApBA,oBAAoB;MACpBJ,oBAAoB,EAApBA,oBAAoB;MACpBS,kBAAkB,EAAlBA;;KAGD7D,QAAQ,CACc;AAE7B;AAIA,IAAMgE,aAAa,gBAAGhQ,aAAmB,CActC;EACD0P,mBAAmB,EAAE,CAAC,IAAI,EAAE,cAAQ,CAAC;EACrCD,cAAc,EAAE,CAACzM,SAAS,CAAC4C,OAAO,EAAE,cAAQ,CAAC;EAC7CqJ,gBAAgB,EAAE;IAAE5B,OAAO,EAAE;GAAO;EACpC6B,gBAAgB,EAAE;IAAE7B,OAAO,EAAE;GAAO;EACpC8B,mBAAmB,EAAE;IAAE9B,OAAO,EAAE;GAAI;EACpCuC,yBAAyB,EAAE,CAAC,IAAI,EAAE,cAAQ,CAAC;EAC3CD,2BAA2B,EAAE,cAAC,IAAInE,GAAG,EAAE,EAAE,cAAQ,CAAC;EAClDwD,SAAS,EAAE;IAAE3B,OAAO,EAAE;GAAI;EAC1ByC,iBAAiB,EAAE,IAAI;EACvBP,UAAU,EAAE,CAAC,EAAE,EAAE;IAAA,OAAM,IAAIpC,OAAO,CAAS;MAAA,OAAM8C,SAAS;MAAC;IAAC;EAC5DT,oBAAoB,EAAE,CAAC,KAAK,EAAE,cAAQ,CAAC;EACvCJ,oBAAoB,EAAE,cAACC,IAAI,CAACC,GAAG,EAAE,EAAE,cAAQ,CAAC;EAC5CO,kBAAkB,EAAE,CAAC,KAAK,EAAE,cAAQ;CACrC,CAAC;AAMF,SAAgBK,YAAYA;EAC1B,IAAAC,iBAAA,GAAsBnQ,UAAgB,CAACgQ,aAAa,CAAC;IAA7ChB,SAAS,GAAAmB,iBAAA,CAATnB,SAAS;EACjB,OAAOA,SAAS;AAClB;AAEA,SAAgBoB,mBAAmBA;EACjC,IAAAC,kBAAA,GAA6BrQ,UAAgB,CAACgQ,aAAa,CAAC;IAApDf,gBAAgB,GAAAoB,kBAAA,CAAhBpB,gBAAgB;EACxB,OAAOA,gBAAgB;AACzB;AAEA,SAAgBqB,mBAAmBA;EACjC,IAAAC,kBAAA,GAA6BvQ,UAAgB,CAACgQ,aAAa,CAAC;IAApDd,gBAAgB,GAAAqB,kBAAA,CAAhBrB,gBAAgB;EACxB,OAAOA,gBAAgB;AACzB;AAEA,SAAgBsB,qBAAqBA;EACnC,IAAAC,kBAAA,GAA+BzQ,UAAgB,CAACgQ,aAAa,CAAC;IAAtDH,kBAAkB,GAAAY,kBAAA,CAAlBZ,kBAAkB;EAC1B,OAAOA,kBAAkB;AAC3B;AAEA,SAAgBa,kBAAkBA;EAChC,IAAAC,kBAAA,GAAuB3Q,UAAgB,CAACgQ,aAAa,CAAC;IAA9CT,UAAU,GAAAoB,kBAAA,CAAVpB,UAAU;EAClB,OAAOA,UAAU;AACnB;AAEA,SAAgBqB,sBAAsBA;EAIpC,IAAAC,kBAAA,GAA2B7Q,UAAgB,CAACgQ,aAAa,CAAC;IAAlDP,cAAc,GAAAoB,kBAAA,CAAdpB,cAAc;EACtB,OAAOA,cAAc;AACvB;AAEA,SAAgBqB,8BAA8BA;EAC5C,IAAAC,kBAAA,GAAwC/Q,UAAgB,CAACgQ,aAAa,CAAC;IAA/DL,2BAA2B,GAAAoB,kBAAA,CAA3BpB,2BAA2B;EACnC,OAAOA,2BAA2B;AACpC;AAEA,SAAgBqB,oBAAoBA;EAClC,IAAAC,kBAAA,GAA8BjR,UAAgB,CAACgQ,aAAa,CAAC;IAArDF,iBAAiB,GAAAmB,kBAAA,CAAjBnB,iBAAiB;EACzB,OAAOA,iBAAiB;AAC1B;AAEA,SAAgBoB,4BAA4BA;EAC1C,IAAAC,kBAAA,GAAsCnR,UAAgB,CAACgQ,aAAa,CAAC;IAA7DJ,yBAAyB,GAAAuB,kBAAA,CAAzBvB,yBAAyB;EACjC,OAAOA,yBAAyB;AAClC;AAEA,SAAgBwB,uBAAuBA;EACrC,IAAAC,mBAAA,GAAiCrR,UAAgB,CAACgQ,aAAa,CAAC;IAAxDR,oBAAoB,GAAA6B,mBAAA,CAApB7B,oBAAoB;EAC5B,OAAOA,oBAAoB;AAC7B;AAEA,SAKgB8B,kBAAkBA;EAChC,IAAAC,mBAAA,GAAiCvR,UAAgB,CAACgQ,aAAa,CAAC;IAAxDZ,oBAAoB,GAAAmC,mBAAA,CAApBnC,oBAAoB;EAE5B,IAAOoC,gBAAgB,GAAwBpC,oBAAoB;IAA1CqC,kBAAkB,GAAIrC,oBAAoB;EACnE,OAAO,CACLoC,gBAAgB,EAChB,SAASE,eAAeA;IACtBD,kBAAkB,CAACpC,IAAI,CAACC,GAAG,EAAE,CAAC;GAC/B,CACF;AACH;;AC7JO,IAAMqC,oBAAoB,gBAAG3R,cAAK,CAAC4R,aAAa,CAErD,EAA2C,CAAC;AAE9C,SAAgBC,gBAAgBA;EAC9B,IAAMC,aAAa,GAAG9R,cAAK,CAAC+R,UAAU,CAACJ,oBAAoB,CAAC;EAC5D,OAAOG,aAAa;AACtB;AAEA,SAAgBE,sBAAsBA,CACpCnH,MAAqB;EAErB,IAAMoH,gBAAgB,GAAGjS,cAAK,CAACiN,MAAM,CAAgB;IACnDiF,YAAY,EAAErH,MAAM,CAACqH,YAAY,IAAIC,SAAS;IAC9CC,eAAe,EAAEvH,MAAM,CAACuH,eAAe,IAAIvH,MAAM,CAACqH,YAAY;IAC9DG,gBAAgB,EAAExH,MAAM,CAACwH,gBAAgB,IAAIF;GAC9C,CAAC;EAEFnS,cAAK,CAAC4O,SAAS,CAAC;IACdqD,gBAAgB,CAAC5E,OAAO,CAAC6E,YAAY,GAAGrH,MAAM,CAACqH,YAAY,IAAIC,SAAS;IACxEF,gBAAgB,CAAC5E,OAAO,CAAC+E,eAAe,GACtCvH,MAAM,CAACuH,eAAe,IAAIvH,MAAM,CAACqH,YAAY;GAChD,EAAE,CAACrH,MAAM,CAACqH,YAAY,EAAErH,MAAM,CAACuH,eAAe,CAAC,CAAC;EAEjDpS,cAAK,CAAC4O,SAAS,CAAC;IACdqD,gBAAgB,CAAC5E,OAAO,CAACgF,gBAAgB,GACvCxH,MAAM,CAACwH,gBAAgB,IAAIF,SAAS;GACvC,EAAE,CAACtH,MAAM,CAACwH,gBAAgB,CAAC,CAAC;EAE7B,OAAOJ,gBAAgB;AACzB;AAEA,SAASE,SAASA;;ACjBlB,IAAYG,kBAGX;AAHD,WAAYA,kBAAkB;EAC5BA,6CAAuB;EACvBA,uCAAiB;AACnB,CAAC,EAHWA,kBAAkB,KAAlBA,kBAAkB;AAK9B,SAAgBC,0BAA0BA;;EACxC,IAAAC,gBAAA,GAAiDhG,eAAe,EAAE;IAA1D1K,iBAAiB,GAAA0Q,gBAAA,CAAjB1Q,iBAAiB;IAAEC,iBAAiB,GAAAyQ,gBAAA,CAAjBzQ,iBAAiB;EAC5C,QAAA0Q,KAAA,GACE,CAAC3Q,iBAAiB,EAAEC,iBAAiB,CAAC,CAACiH,IAAI,CACzC,UAAA0J,CAAC;IAAA,OAAIA,CAAC,KAAKvI,0BAA0B;IACtC,YAAAsI,KAAA,GAAItI,0BAA0B;AAEnC;AAEA,SAAgB2E,wBAAwBA;EACtC,IAAA6D,iBAAA,GAA4BnG,eAAe,EAAE;IAArCxK,eAAe,GAAA2Q,iBAAA,CAAf3Q,eAAe;EACvB,OAAOA,eAAe;AACxB;AAEA,SAAgB4Q,uBAAuBA;EACrC,IAAAC,iBAAA,GAAiCrG,eAAe,EAAE;IAA1Cd,oBAAoB,GAAAmH,iBAAA,CAApBnH,oBAAoB;EAC5B,OAAOA,oBAAoB;AAC7B;AAEA,SAAgBoH,0BAA0BA;EACxC,IAAAC,iBAAA,GAA8BvG,eAAe,EAAE;IAAvCvK,iBAAiB,GAAA8Q,iBAAA,CAAjB9Q,iBAAiB;EACzB,OAAOA,iBAAiB;AAC1B;AAEA,SAAgB+Q,mBAAmBA;EACjC,IAAAC,iBAAA,GAAuBzG,eAAe,EAAE;IAAhCrK,UAAU,GAAA8Q,iBAAA,CAAV9Q,UAAU;EAClB,OAAOA,UAAU;AACnB;AAEA,SAAgB+Q,wBAAwBA;EACtC,IAAAC,iBAAA,GAA4B3G,eAAe,EAAE;IAArCtK,eAAe,GAAAiR,iBAAA,CAAfjR,eAAe;EACvB,OAAOA,eAAe;AACxB;AAEA,SAAgBkR,mBAAmBA;EACjC,IAAAC,iBAAA,GAAuB7G,eAAe,EAAE;IAAhC1B,UAAU,GAAAuI,iBAAA,CAAVvI,UAAU;EAClB,OAAOA,UAAU;AACnB;AAEA,SAAgBwI,qBAAqBA;EACnC,IAAAC,iBAAA,GAAyB/G,eAAe,EAAE;IAAlChL,YAAY,GAAA+R,iBAAA,CAAZ/R,YAAY;EACpB,OAAOA,YAAY;AACrB;AAEA,SAAgBgS,aAAaA;EAC3B,IAAAC,iBAAA,GAAiBjH,eAAe,EAAE;IAA1B7K,IAAI,GAAA8R,iBAAA,CAAJ9R,IAAI;EACZ,OAAOA,IAAI;AACb;AAEA,SAAgB+R,qBAAqBA,CACnCC,gBAAoC;EAEpC,IAAAC,iBAAA,GAAoB/B,gBAAgB,EAAE;IAA9BxE,OAAO,GAAAuG,iBAAA,CAAPvG,OAAO;EACf,IAAAwG,qBAAA,GAA6BrD,qBAAqB,EAAE;IAA3CsD,gBAAgB,GAAAD,qBAAA;EAEzB,IAAME,OAAO,GAAG1G,OAAO,CAAC6E,YAAY,IAAK,cAAS;EAClD,IAAQE,eAAe,GAAK/E,OAAO,CAA3B+E,eAAe;EAEvB,IAAIuB,gBAAgB,KAAKrB,kBAAkB,CAAC0B,SAAS,IAAI5B,eAAe,EAAE;IACxE,OAAO;MAAA,SAAAtT,IAAA,GAAAE,SAAA,CAAA2D,MAAA,EAAIsR,IAAI,OAAApV,KAAA,CAAAC,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;QAAJkV,IAAI,CAAAlV,IAAA,IAAAC,SAAA,CAAAD,IAAA;;MAAA,OACbqT,eAAe,CAAA8B,KAAA,SAAID,IAAI,CAAAE,MAAA,EAAE;QACvBC,mBAAmB,EAAE,SAAAA;UACnBN,gBAAgB,CAAC,UAAAO,CAAC;YAAA,OAAIA,CAAC;YAAC;;OAE3B,GAAC;;;EAGN,OAAO;uCAAIJ,IAAI,OAAApV,KAAA,CAAAyV,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJN,IAAI,CAAAM,KAAA,IAAAvV,SAAA,CAAAuV,KAAA;;IACbR,OAAO,CAAAG,KAAA,SAAID,IAAI,CAAAE,MAAA,EAAE;MACfC,mBAAmB,EAAE,SAAAA;QACnBN,gBAAgB,CAAC,IAAI,CAAC;;KAEzB,GAAC;GACH;AACH;AAEA,SAAgBU,yBAAyBA;EACvC,IAAAC,kBAAA,GAAoB5C,gBAAgB,EAAE;IAA9BxE,OAAO,GAAAoH,kBAAA,CAAPpH,OAAO;EAEf,OAAOA,OAAO,CAACgF,gBAAgB,IAAK,cAAS;AAC/C;AAEA,SAAgBqC,gBAAgBA;EAC9B,IAAAC,kBAAA,GAA0BnI,eAAe,EAAE;IAAnC7B,aAAa,GAAAgK,kBAAA,CAAbhK,aAAa;EACrB,OAAOA,aAAa;AACtB;AAEA,SAAgBiK,cAAcA;EAC5B,IAAAC,kBAAA,GAAkBrI,eAAe,EAAE;IAA3BpK,KAAK,GAAAyS,kBAAA,CAALzS,KAAK;EAEb,OAAOA,KAAK;AACd;AAEA,SAAgB0S,4BAA4BA;EAC1C,IAAAC,kBAAA,GAAgCvI,eAAe,EAAE;IAAzCnK,mBAAmB,GAAA0S,kBAAA,CAAnB1S,mBAAmB;EAC3B,OAAOA,mBAAmB;AAC5B;AAEA,SAAgB2S,uBAAuBA;EACrC,IAAAC,kBAAA,GAA2BzI,eAAe,EAAE;IAApClK,cAAc,GAAA2S,kBAAA,CAAd3S,cAAc;EACtB,OAAOA,cAAc;AACvB;AAEA,SAAgB4S,kBAAkBA;EAChC,IAAAC,kBAAA,GAAsB3I,eAAe,EAAE;IAA/BjK,SAAS,GAAA4S,kBAAA,CAAT5S,SAAS;EACjB,OAAOA,SAAS;AAClB;AAEA,SAAgB6S,cAAcA;EAC5B,IAAAC,kBAAA,GAAiC7I,eAAe,EAAE;IAA1C5L,MAAM,GAAAyU,kBAAA,CAANzU,MAAM;IAAEC,KAAK,GAAAwU,kBAAA,CAALxU,KAAK;IAAE2B,KAAK,GAAA6S,kBAAA,CAAL7S,KAAK;EAC5B,OAAA1C,QAAA;IAASc,MAAM,EAAE0U,YAAY,CAAC1U,MAAM,CAAC;IAAEC,KAAK,EAAEyU,YAAY,CAACzU,KAAK;KAAM2B,KAAK;AAC7E;AAEA,SAAgBuM,sBAAsBA;EACpC,IAAAwG,kBAAA,GAAiC/I,eAAe,EAAE;IAA1C3K,oBAAoB,GAAA0T,kBAAA,CAApB1T,oBAAoB;EAC5B,OAAOA,oBAAoB;AAC7B;AAEA,SAAgBkM,qBAAqBA;EACnC,IAAAyH,kBAAA,GAAyBhJ,eAAe,EAAE;IAAlC5K,YAAY,GAAA4T,kBAAA,CAAZ5T,YAAY;EACpB,OAAOA,YAAY;AACrB;AAEA,SAAgB6T,uBAAuBA;EACrC,IAAAC,kBAAA,GAA2BlJ,eAAe,EAAE;IAApC/J,cAAc,GAAAiT,kBAAA,CAAdjT,cAAc;EACtB,OAAOA,cAAc;AACvB;AAEA,SAAgBkT,+BAA+BA;EAC7C,IAAAC,kBAAA,GAAmCpJ,eAAe,EAAE;IAA5C9J,sBAAsB,GAAAkT,kBAAA,CAAtBlT,sBAAsB;EAC9B,OAAOA,sBAAsB;AAC/B;AAEA,SAAgBgL,gBAAgBA;EAC9B,IAAAmI,kBAAA,GAA0BrJ,eAAe,EAAE;IAAnCxB,aAAa,GAAA6K,kBAAA,CAAb7K,aAAa;EACrB,OAAOA,aAAa;AACtB;AAEA,SAAgB8K,kBAAkBA;EAChC,IAAAC,kBAAA,GAAsBvJ,eAAe,EAAE;IAA/Bf,SAAS,GAAAsK,kBAAA,CAATtK,SAAS;EACjB,OAAOA,SAAS;AAClB;AAEA,SAAgBuK,oBAAoBA;EAIlC,IAAAC,kBAAA,GAAwBzJ,eAAe,EAAE;IAAjCpB,WAAW,GAAA6K,kBAAA,CAAX7K,WAAW;EACnB,OAAOA,WAAW;AACpB;AAEA,SAASkK,YAAYA,CAACY,eAAiC;EACrD,OAAO,OAAOA,eAAe,KAAK,QAAQ,GACnCA,eAAe,UAClBA,eAAe;AACrB;AAEA,SAAgBC,sBAAsBA,CAACC,kBAA0B;EAC/D,IAAMC,UAAU,GAAGD,kBAAkB,GAAG,CAAC;EACzC,IAAME,QAAQ,GAAGF,kBAAkB,GAAG,CAAC;EAEvC,IAAIC,UAAU,EAAE;IACd,OAAOC,QAAQ,GACX/L,qCAAqC,CAACjD,OAAO,CAC3C,IAAI,EACJ8O,kBAAkB,CAACG,QAAQ,EAAE,CAC9B,GACDjM,+BAA+B;;EAGrC,OAAOF,+BAA+B;AACxC;;SCvMwBoM,eAAeA;EACrC,IAAAC,mBAAA,GAAqB/F,kBAAkB,EAAE;IAAlCnB,UAAU,GAAAkH,mBAAA;EAEjB,OAAO,CAAC,CAAClH,UAAU;AACrB;;SCJgBmH,YAAYA,CAACC,OAAwB;EACnD,IAAI,CAACA,OAAO,EAAE;IACZ;;EAGFC,qBAAqB,CAAC;IACpBD,OAAO,CAACE,KAAK,EAAE;GAChB,CAAC;AACJ;AAEA,SAAgBC,uBAAuBA,CAACH,OAAwB;EAC9D,IAAI,CAACA,OAAO,EAAE;EAEd,IAAMvV,IAAI,GAAGuV,OAAO,CAACI,sBAAqC;EAE1DL,YAAY,CAACtV,IAAI,CAAC;AACpB;AAEA,SAAgB4V,uBAAuBA,CAACL,OAAwB;EAC9D,IAAI,CAACA,OAAO,EAAE;EAEd,IAAMtV,IAAI,GAAGsV,OAAO,CAACM,kBAAiC;EAEtDP,YAAY,CAACrV,IAAI,CAAC;AACpB;AAEA,SAAgB6V,sBAAsBA,CAACP,OAAwB;EAC7D,IAAI,CAACA,OAAO,EAAE;EAEd,IAAMQ,KAAK,GAAGR,OAAO,CAACS,iBAAgC;EAEtDV,YAAY,CAACS,KAAK,CAAC;AACrB;;SChCgBE,gBAAgBA;EAC9B,OAAOC,QAAQ,CAACC,aAAgC;AAClD;;SCCgBC,yBAAyBA,CAAAlR,IAAA;MACvC0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;EAIR,IAAMyL,aAAa,GAAGzX,MAAY,CAAc,IAAI,CAAC;EACrD,IAAM0X,gBAAgB,GAAG1X,MAAY,CAAc,IAAI,CAAC;EACxD,IAAM2X,OAAO,GAAG3X,MAAY,CAAiB,IAAI,CAAC;EAClD,IAAM4X,cAAc,GAAG5X,MAAY,CAAmB,IAAI,CAAC;EAC3D,IAAM6X,iBAAiB,GAAG7X,MAAY,CAAiB,IAAI,CAAC;EAC5D,IAAM8X,qBAAqB,GAAG9X,MAAY,CAAiB,IAAI,CAAC;EAChE,IAAM+X,kBAAkB,GAAG/X,MAAY,CAAiB,IAAI,CAAC;EAC7D,IAAMgY,YAAY,GAAGhY,MAAY,CAAmB,IAAI,CAAC;EAEzD,OACEA,cAACiY,iBAAiB,CAAC5L,QAAQ;IACzBrL,KAAK,EAAE;MACL0W,gBAAgB,EAAhBA,gBAAgB;MAChBC,OAAO,EAAPA,OAAO;MACPG,qBAAqB,EAArBA,qBAAqB;MACrBL,aAAa,EAAbA,aAAa;MACbG,cAAc,EAAdA,cAAc;MACdC,iBAAiB,EAAjBA,iBAAiB;MACjBE,kBAAkB,EAAlBA,kBAAkB;MAClBC,YAAY,EAAZA;;KAGDhM,QAAQ,CACkB;AAEjC;AAiBA,IAAMiM,iBAAiB,gBAAGjY,aAAmB,CAAc;EACzD0X,gBAAgB,eAAE1X,SAAe,EAAE;EACnC2X,OAAO,eAAE3X,SAAe,EAAE;EAC1B8X,qBAAqB,eAAE9X,SAAe,EAAE;EACxCyX,aAAa,eAAEzX,SAAe,EAAE;EAChC4X,cAAc,eAAE5X,SAAe,EAAE;EACjC6X,iBAAiB,eAAE7X,SAAe,EAAE;EACpC+X,kBAAkB,eAAE/X,SAAe,EAAE;EACrCgY,YAAY,eAAEhY,SAAe;CAC9B,CAAC;AAEF,SAASkY,aAAaA;EACpB,OAAOlY,UAAgB,CAACiY,iBAAiB,CAAC;AAC5C;AAEA,SAAgBE,gBAAgBA;EAC9B,OAAOD,aAAa,EAAE,CAAC,eAAe,CAAC;AACzC;AAEA,SAAgBE,mBAAmBA;EACjC,OAAOF,aAAa,EAAE,CAAC,kBAAkB,CAAC;AAC5C;AAEA,SAAgBG,sBAAsBA;EACpC,IAAMX,gBAAgB,GAAGU,mBAAmB,EAAE;EAC9C,OAAO,UAACE,MAAuB;IAC7B,IAAIA,MAAM,KAAK,IAAI,IAAIZ,gBAAgB,CAACrK,OAAO,KAAK,IAAI,EAAE;MACxDqJ,YAAY,CAACgB,gBAAgB,CAACrK,OAAO,CAAC;;IAGxCqK,gBAAgB,CAACrK,OAAO,GAAGiL,MAAM;GAClC;AACH;AAEA,SAAgBC,UAAUA;EACxB,OAAOL,aAAa,EAAE,CAAC,SAAS,CAAC;AACnC;AAEA,SAAgBM,eAAeA;EAC7B,OAAON,aAAa,EAAE,CAAC,cAAc,CAAC;AACxC;AAEA,SAAgBO,iBAAiBA;EAC/B,OAAOP,aAAa,EAAE,CAAC,gBAAgB,CAAC;AAC1C;AAEA,SAAgBQ,oBAAoBA;EAClC,OAAOR,aAAa,EAAE,CAAC,mBAAmB,CAAC;AAC7C;AAEA,SAAgBS,wBAAwBA;EACtC,OAAOT,aAAa,EAAE,CAAC,uBAAuB,CAAC;AACjD;AAEA,SAAgBU,qBAAqBA;EACnC,OAAOV,aAAa,EAAE,CAAC,oBAAoB,CAAC;AAC9C;;SC7FgBW,QAAQA,CAACC,IAAqB,EAAEC;MAAAA;IAAAA,MAAc,CAAC;;EAC7D,IAAMC,QAAQ,GAAGC,eAAe,CAACH,IAAI,CAAC;EAEtC,IAAI,CAACE,QAAQ,EAAE;IACb;;EAGFpC,qBAAqB,CAAC;IACpBoC,QAAQ,CAACE,SAAS,GAAGH,GAAG;GACzB,CAAC;AACJ;AAEA,SAAgBI,QAAQA,CAACL,IAAqB,EAAEM,EAAU;EACxD,IAAMJ,QAAQ,GAAGC,eAAe,CAACH,IAAI,CAAC;EAEtC,IAAI,CAACE,QAAQ,EAAE;IACb;;EAGFpC,qBAAqB,CAAC;IACpBoC,QAAQ,CAACE,SAAS,GAAGF,QAAQ,CAACE,SAAS,GAAGE,EAAE;GAC7C,CAAC;AACJ;AAEA,SAAgBC,WAAWA;EACzB,IAAM1B,OAAO,GAAGY,UAAU,EAAE;EAE5B,OAAOe,WAAW,CAChB,UAACP,GAAW;IACVnC,qBAAqB,CAAC;MACpB,IAAIe,OAAO,CAACtK,OAAO,EAAE;QACnBsK,OAAO,CAACtK,OAAO,CAAC6L,SAAS,GAAGH,GAAG;;KAElC,CAAC;GACH,EACD,CAACpB,OAAO,CAAC,CACV;AACH;AAEA,SAAgB4B,qBAAqBA,CAACvS,KAAsB;EAC1D,IAAI,CAACA,KAAK,IAAI,CAACwS,kBAAkB,CAACxS,KAAK,CAAC,EAAE;IACxC;;EAGF,IAAIA,KAAK,CAACyS,OAAO,CAAC9a,WAAW,CAACD,UAAU,CAACgb,eAAe,CAAC,CAAC,EAAE;IAC1D;;EAGF,IAAMC,UAAU,GAAGC,iBAAiB,CAAC5S,KAAK,CAAC;EAC3C,IAAMoS,EAAE,GAAGS,0BAA0B,CAAC7S,KAAK,CAAC;EAC5CmS,QAAQ,CAACQ,UAAU,EAAE,EAAEG,mBAAmB,CAACC,eAAe,CAAC/S,KAAK,CAAC,CAAC,GAAGoS,EAAE,CAAC,CAAC;AAC3E;;SC1CgBY,sBAAsBA,CAACC,MAAuB;EAC5D,IAAMjT,KAAK,GAAGkT,iBAAiB,CAACD,MAAM,CAAC;EACvCvD,YAAY,CAAC1P,KAAK,CAAC;EACnBuS,qBAAqB,CAACvS,KAAK,CAAC;AAC9B;AAEA,SAAgBmT,8BAA8BA,CAACF,MAAuB;EACpE,IAAMG,UAAU,GAAGF,iBAAiB,CAACD,MAAM,CAAC;EAE5CvD,YAAY,CAAC0D,UAAU,CAAC;EACxBA,UAAU,oBAAVA,UAAU,CAAEC,KAAK,EAAE;AACrB;AAEA,SAAgBC,qBAAqBA,CAACL,MAAuB;EAC3DvD,YAAY,CAAC6D,gBAAgB,CAACN,MAAM,CAAC,CAAC;AACxC;AAEA,SAAgBO,qBAAqBA,CAAC7D,OAAwB;EAC5D,IAAI,CAACA,OAAO,EAAE;IACZ;;EAGF,IAAMtV,IAAI,GAAGoZ,gBAAgB,CAAC9D,OAAO,CAAC;EAEtC,IAAI,CAACtV,IAAI,EAAE;IACT,OAAO2Y,sBAAsB,CAACU,YAAY,CAAC/D,OAAO,CAAC,CAAC;;EAGtDD,YAAY,CAACrV,IAAI,CAAC;EAClBkY,qBAAqB,CAAClY,IAAI,CAAC;AAC7B;AAEA,SAAgBsZ,qBAAqBA,CAAChE,OAAwB;EAC5D,IAAI,CAACA,OAAO,EAAE;IACZ;;EAGF,IAAMvV,IAAI,GAAGwZ,gBAAgB,CAACjE,OAAO,CAAC;EAEtC,IAAI,CAACvV,IAAI,EAAE;IACT,OAAOkZ,qBAAqB,CAACO,YAAY,CAAClE,OAAO,CAAC,CAAC;;EAGrDD,YAAY,CAACtV,IAAI,CAAC;EAClBmY,qBAAqB,CAACnY,IAAI,CAAC;AAC7B;AAEA,SAAgB0Z,yBAAyBA,CACvCnE,OAAwB,EACxBoE,MAAkB;EAElB,IAAI,CAACpE,OAAO,EAAE;IACZ;;EAGF,IAAMvV,IAAI,GAAG4Z,oBAAoB,CAACrE,OAAO,CAAC;EAE1C,IAAI,CAACvV,IAAI,EAAE;IACT,OAAO2Z,MAAM,EAAE;;EAGjBrE,YAAY,CAACtV,IAAI,CAAC;EAClBmY,qBAAqB,CAACnY,IAAI,CAAC;AAC7B;AAEA,SAAgB6Z,2BAA2BA,CAACtE,OAAwB;EAClE,IAAI,CAACA,OAAO,EAAE;IACZ;;EAGF,IAAMtV,IAAI,GAAG6Z,sBAAsB,CAACvE,OAAO,CAAC;EAE5C,OAAOD,YAAY,CAACrV,IAAI,CAAC;AAC3B;AAEA,SAAS2Z,oBAAoBA,CAACrE,OAAoB;EAChD,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;;EAGb,IAAMwE,eAAe,GAAGC,sBAAsB,CAACzE,OAAO,CAAC;EACvD,IAAM3S,QAAQ,GAAG+V,eAAe,CAACoB,eAAe,CAAC;EACjD,IAAME,UAAU,GAAGC,iBAAiB,CAACH,eAAe,EAAExE,OAAO,CAAC;EAC9D,IAAM4E,GAAG,GAAGC,SAAS,CAACL,eAAe,EAAExE,OAAO,CAAC;EAC/C,IAAM8E,UAAU,GAAGC,iBAAiB,CAACP,eAAe,EAAExE,OAAO,CAAC;EAE9D,IAAI4E,GAAG,KAAK,CAAC,EAAE;IACb,IAAMI,mBAAmB,GAAGd,YAAY,CAAC7W,QAAQ,CAAC;IAElD,IAAI,CAAC2X,mBAAmB,EAAE;MACxB,OAAO,IAAI;;IAGb,OAAOC,eAAe,CACpBC,gBAAgB,CAACF,mBAAmB,CAAC,EACrC,CAAC,CAAC;;IACFF,UAAU,EACVJ,UAAU,CACX;;EAGH,OAAOS,mBAAmB,CACxBD,gBAAgB,CAACV,eAAe,CAAC,EACjCI,GAAG,EACHE,UAAU,EACVJ,UAAU,CACX;AACH;AAEA,SAASH,sBAAsBA,CAACvE,OAAoB;EAClD,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;;EAGb,IAAMwE,eAAe,GAAGC,sBAAsB,CAACzE,OAAO,CAAC;EACvD,IAAM3S,QAAQ,GAAG+V,eAAe,CAACoB,eAAe,CAAC;EACjD,IAAME,UAAU,GAAGC,iBAAiB,CAACH,eAAe,EAAExE,OAAO,CAAC;EAC9D,IAAM4E,GAAG,GAAGC,SAAS,CAACL,eAAe,EAAExE,OAAO,CAAC;EAC/C,IAAM8E,UAAU,GAAGC,iBAAiB,CAACP,eAAe,EAAExE,OAAO,CAAC;EAC9D,IAAI,CAACoF,UAAU,CAACZ,eAAe,EAAExE,OAAO,CAAC,EAAE;IACzC,IAAMqF,mBAAmB,GAAGtB,YAAY,CAAC1W,QAAQ,CAAC;IAElD,IAAI,CAACgY,mBAAmB,EAAE;MACxB,OAAO,IAAI;;IAGb,OAAOJ,eAAe,CACpBC,gBAAgB,CAACG,mBAAmB,CAAC,EACrC,CAAC,EACDP,UAAU,EACVJ,UAAU,CACX;;EAGH,IAAMY,aAAa,GAAGC,mBAAmB,CACvCL,gBAAgB,CAACV,eAAe,CAAC,EACjCI,GAAG,EACHE,UAAU,EACVJ,UAAU,CACX;EAED,OAAOY,aAAa;AACtB;;SC/JgBE,sBAAsBA;EACpC,IAAAC,qBAAA,GAA8ClL,4BAA4B,EAAE;IAArEwI,eAAe,GAAA0C,qBAAA;IAAEC,kBAAkB,GAAAD,qBAAA;EAC1C,IAAAE,qBAAA,GAA8ClL,uBAAuB,EAAE;IAAhEmL,eAAe,GAAAD,qBAAA;IAAEE,kBAAkB,GAAAF,qBAAA;EAE1C,IAAMG,mBAAmB,GAAGnD,WAAW,CAAC;IACtC,IAAII,eAAe,EAAE;MACnB2C,kBAAkB,CAAC,IAAI,CAAC;;IAG1B,IAAIE,eAAe,EAAE;MACnBC,kBAAkB,CAAC,KAAK,CAAC;;GAE5B,EAAE,CACD9C,eAAe,EACf6C,eAAe,EACfF,kBAAkB,EAClBG,kBAAkB,CACnB,CAAC;EAEF,OAAOC,mBAAmB;AAC5B;AAEA,SAAgBC,iBAAiBA;EAC/B,IAAAC,sBAAA,GAA0BzL,4BAA4B,EAAE;IAAjDwI,eAAe,GAAAiD,sBAAA;EACtB,IAAAC,sBAAA,GAA0BxL,uBAAuB,EAAE;IAA5CmL,eAAe,GAAAK,sBAAA;EAEtB,OAAO,SAASC,cAAcA;IAC5B,OAAO,CAAC,CAACnD,eAAe,IAAI6C,eAAe;GAC5C;AACH;;SC/BgBO,oBAAoBA;EAClC,IAAMC,gBAAgB,GAAGzM,mBAAmB,EAAE;EAC9C,OAAO,SAAS0M,iBAAiBA;IAC/BD,gBAAgB,CAAC1P,OAAO,GAAG,IAAI;GAChC;AACH;AAEA,SAAgB4P,iBAAiBA;EAC/B,IAAMF,gBAAgB,GAAGzM,mBAAmB,EAAE;EAC9C,OAAO,SAAS4M,cAAcA;IAC5BH,gBAAgB,CAAC1P,OAAO,GAAG,KAAK;GACjC;AACH;AAEA,SAAgB8P,oBAAoBA;EAClC,IAAMJ,gBAAgB,GAAGzM,mBAAmB,EAAE;EAC9C,OAAO,SAAS8M,iBAAiBA;IAC/B,OAAOL,gBAAgB,CAAC1P,OAAO;GAChC;AACH;AAEA,SAAgBgQ,cAAcA;EAC5B,IAAM1F,OAAO,GAAGY,UAAU,EAAE;EAC5B,IAAM2E,cAAc,GAAGD,iBAAiB,EAAE;EAC1C,IAAMG,iBAAiB,GAAGD,oBAAoB,EAAE;EAEhDvO,SAAS,CAAC;IACR,IAAM0O,OAAO,GAAG3F,OAAO,CAACtK,OAAO;IAC/BiQ,OAAO,oBAAPA,OAAO,CAAEC,gBAAgB,CAAC,WAAW,EAAEC,WAAW,EAAE;MAClDC,OAAO,EAAE;KACV,CAAC;IAEF,SAASD,WAAWA;MAClB,IAAIJ,iBAAiB,EAAE,EAAE;QACvBF,cAAc,EAAE;;;IAGpB,OAAO;MACLI,OAAO,oBAAPA,OAAO,CAAEI,mBAAmB,CAAC,WAAW,EAAEF,WAAW,CAAC;KACvD;GACF,EAAE,CAAC7F,OAAO,EAAEuF,cAAc,EAAEE,iBAAiB,CAAC,CAAC;AAClD;;SCrCgBO,mBAAmBA;EACjC,IAAM/F,cAAc,GAAGa,iBAAiB,EAAE;EAE1C,OAAOa,WAAW,CAAC;IACjB5C,YAAY,CAACkB,cAAc,CAACvK,OAAO,CAAC;GACrC,EAAE,CAACuK,cAAc,CAAC,CAAC;AACtB;AAEA,SAAgBgG,sBAAsBA;EACpC,IAAM/F,iBAAiB,GAAGa,oBAAoB,EAAE;EAEhD,OAAOY,WAAW,CAAC;IACjB,IAAI,CAACzB,iBAAiB,CAACxK,OAAO,EAAE;MAC9B;;IAGF6J,sBAAsB,CAACW,iBAAiB,CAACxK,OAAO,CAAC;GAClD,EAAE,CAACwK,iBAAiB,CAAC,CAAC;AACzB;AAEA,SAAgBgG,0BAA0BA;EACxC,IAAM/F,qBAAqB,GAAGa,wBAAwB,EAAE;EAExD,OAAOW,WAAW,CAAC;IACjB,IAAI,CAACxB,qBAAqB,CAACzK,OAAO,EAAE;MAClC;;IAGF6J,sBAAsB,CAACY,qBAAqB,CAACzK,OAAO,CAAC;GACtD,EAAE,CAACyK,qBAAqB,CAAC,CAAC;AAC7B;;ACvBA,SAASgG,eAAeA;EACtB,IAAM9O,SAAS,GAAGkB,YAAY,EAAE;EAEhC,OAAO,SAAS6N,SAASA,CACvBC,MAA6D;IAE7D,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOD,SAAS,CAACC,MAAM,CAAChP,SAAS,CAAC3B,OAAO,CAAC,CAAC;;IAG7C2B,SAAS,CAAC3B,OAAO,GAAG2Q,MAAM;GAC3B;AACH;AAEA,SAAgBC,cAAcA;EAC5B,IAAMC,WAAW,GAAGC,cAAc,EAAE;EACpC,IAAMvG,cAAc,GAAGa,iBAAiB,EAAE;EAC1C,IAAM2F,gBAAgB,GAAGT,mBAAmB,EAAE;EAE9C,OAAO,SAASU,WAAWA;IACzB,IAAIzG,cAAc,CAACvK,OAAO,EAAE;MAC1BuK,cAAc,CAACvK,OAAO,CAACrM,KAAK,GAAG,EAAE;;IAGnCkd,WAAW,CAAC,EAAE,CAAC;IACfE,gBAAgB,EAAE;GACnB;AACH;AAEA,SAAgBE,eAAeA;EAC7B,IAAM1G,cAAc,GAAGa,iBAAiB,EAAE;EAC1C,IAAMyF,WAAW,GAAGC,cAAc,EAAE;EAEpC,OAAO,SAASI,YAAYA,CAACC,GAAW;IACtC,IAAI5G,cAAc,CAACvK,OAAO,EAAE;MAC1BuK,cAAc,CAACvK,OAAO,CAACrM,KAAK,QAAM4W,cAAc,CAACvK,OAAO,CAACrM,KAAK,GAAGwd,GAAK;MACtEN,WAAW,CAACO,uBAAuB,CAAC7G,cAAc,CAACvK,OAAO,CAACrM,KAAK,CAAC,CAAC;KACnE,MAAM;MACLkd,WAAW,CAACO,uBAAuB,CAACD,GAAG,CAAC,CAAC;;GAE5C;AACH;AAEA,SAAgBE,SAASA;EACvB,IAAM9G,cAAc,GAAGa,iBAAiB,EAAE;EAC1C,IAAMzJ,SAAS,GAAGkB,YAAY,EAAE;EAChC,IAAMyO,YAAY,GAAGb,eAAe,EAAE;EACtC,IAAMI,WAAW,GAAGC,cAAc,EAAE;EAEpC,IAAA1H,mBAAA,GAAqB/F,kBAAkB,EAAE;IAAlCnB,UAAU,GAAAkH,mBAAA;EACjB,IAAMmI,mBAAmB,GAAGC,sBAAsB,CAChD7P,SAAS,CAAC3B,OAAO,EACjBkC,UAAU,CACX;EAED,OAAO;IACLuP,QAAQ,EAARA,QAAQ;IACRvP,UAAU,EAAVA,UAAU;IACVqI,cAAc,EAAdA,cAAc;IACdgH,mBAAmB,EAAnBA;GACD;EAED,SAASE,QAAQA,CAACC,UAAkB;IAClC,IAAMC,MAAM,GAAGhQ,SAAS,CAAC3B,OAAO;IAEhC,IAAM4R,SAAS,GAAGF,UAAU,CAAC1X,WAAW,EAAE;IAE1C,IAAI2X,MAAM,YAANA,MAAM,CAAGC,SAAS,CAAC,IAAIA,SAAS,CAACtc,MAAM,IAAI,CAAC,EAAE;MAChD,OAAOub,WAAW,CAACe,SAAS,CAAC;;IAG/B,IAAMC,YAAY,GAAGC,gBAAgB,CAACF,SAAS,EAAED,MAAM,CAAC;IAExD,IAAI,CAACE,YAAY,EAAE;;;MAGjB,OAAOhB,WAAW,CAACe,SAAS,CAAC;;IAG/BN,YAAY,CAAC,UAAAtR,OAAO;MAAA,IAAA3G,cAAA;MAAA,OAClB1B,MAAM,CAACC,MAAM,CAACoI,OAAO,GAAA3G,cAAA,OAAAA,cAAA,CAClBuY,SAAS,IAAGG,0BAA0B,CAACF,YAAY,EAAED,SAAS,CAAC,EAAAvY,cAAA,EAChE;MACH;IACDwX,WAAW,CAACe,SAAS,CAAC;;AAE1B;AAEA,SAASd,cAAcA;EACrB,IAAAkB,oBAAA,GAA0B3O,kBAAkB,EAAE;IAArC4O,aAAa,GAAAD,oBAAA;EACtB,IAAM5H,aAAa,GAAGU,gBAAgB,EAAE;EAExC,OAAO,SAAS+F,WAAWA,CAAC3O,UAAkB;IAC5CqH,qBAAqB,CAAC;MACpB0I,aAAa,CAAC/P,UAAU,GAAGA,UAAU,oBAAVA,UAAU,CAAElI,WAAW,EAAE,GAAGkI,UAAU,CAAC,CAACgQ,IAAI,CACrE;QACE1G,QAAQ,CAACpB,aAAa,CAACpK,OAAO,EAAE,CAAC,CAAC;OACnC,CACF;KACF,CAAC;GACH;AACH;AAEA,SAAS+R,0BAA0BA,CACjCzW,MAAkB,EAClB6W,OAAe;EAEf,IAAMC,QAAQ,GAAe,EAAE;EAE/B,KAAK,IAAMvX,OAAO,IAAIS,MAAM,EAAE;IAC5B,IAAM3B,KAAK,GAAG2B,MAAM,CAACT,OAAO,CAAC;IAE7B,IAAIwX,QAAQ,CAAC1Y,KAAK,EAAEwY,OAAO,CAAC,EAAE;MAC5BC,QAAQ,CAACvX,OAAO,CAAC,GAAGlB,KAAK;;;EAI7B,OAAOyY,QAAQ;AACjB;AAEA,SAASC,QAAQA,CAAC1Y,KAAgB,EAAEwY,OAAe;EACjD,OAAOrY,UAAU,CAACH,KAAK,CAAC,CAAC2Y,IAAI,CAAC,UAAA5b,IAAI;IAAA,OAAIA,IAAI,CAACmF,QAAQ,CAACsW,OAAO,CAAC;IAAC;AAC/D;AAEA,SAAgBI,kBAAkBA;EAChC,IAAAC,aAAA,GAA4B3P,YAAY,EAAE;IAAzB8O,MAAM,GAAAa,aAAA,CAAfxS,OAAO;EACf,IAAAyS,oBAAA,GAAqBpP,kBAAkB,EAAE;IAAlCnB,UAAU,GAAAuQ,oBAAA;EAEjB,OAAO,UAAA5X,OAAO;IAAA,OAAI6X,2BAA2B,CAAC7X,OAAO,EAAE8W,MAAM,EAAEzP,UAAU,CAAC;;AAC5E;AAEA,SAASwQ,2BAA2BA,CAClC7X,OAAe,EACf8W,MAAmB,EACnBzP,UAAkB;;EAElB,IAAI,CAACyP,MAAM,IAAI,CAACzP,UAAU,EAAE;IAC1B,OAAO,KAAK;;EAGd,OAAO,GAAAyQ,kBAAA,GAAChB,MAAM,CAACzP,UAAU,CAAC,aAAlByQ,kBAAA,CAAqB9X,OAAO,CAAC;AACvC;AAIA,SAASiX,gBAAgBA,CACvBK,OAAe,EACfS,IAAuC;EAEvC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,IAAI;;EAGb,IAAIA,IAAI,CAACT,OAAO,CAAC,EAAE;IACjB,OAAOS,IAAI,CAACT,OAAO,CAAC;;EAGtB,IAAMU,kBAAkB,GAAGlb,MAAM,CAACmb,IAAI,CAACF,IAAI,CAAC,CACzCG,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;IAAA,OAAKA,CAAC,CAAC3d,MAAM,GAAG0d,CAAC,CAAC1d,MAAM;IAAC,CACnCqG,IAAI,CAAC,UAAAjI,GAAG;IAAA,OAAIye,OAAO,CAACtW,QAAQ,CAACnI,GAAG,CAAC;IAAC;EAErC,IAAImf,kBAAkB,EAAE;IACtB,OAAOD,IAAI,CAACC,kBAAkB,CAAC;;EAGjC,OAAO,IAAI;AACb;AAEA,SAAgBzB,uBAAuBA,CAACD,GAAW;EACjD,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACnC,OAAO,EAAE;;EAGX,OAAOA,GAAG,CAAC+B,IAAI,EAAE,CAAClZ,WAAW,EAAE;AACjC;AAEA,SAASwX,sBAAsBA,CAC7B2B,WAAwB,EACxBjR,UAAkB;;EAElB,IAAI,EAACiR,WAAW,YAAXA,WAAW,CAAGjR,UAAU,CAAC,GAAE,OAAO,EAAE;EAEzC,IAAM6G,kBAAkB,GACtB,EAAAqK,eAAA,GAAAzb,MAAM,CAACmB,OAAO,CAACqa,WAAW,oBAAXA,WAAW,CAAGjR,UAAU,CAAC,CAAC,qBAAzCkR,eAAA,CAA2C9d,MAAM,KAAI,CAAC;;EAExD,OAAOwT,sBAAsB,CAACC,kBAAkB,CAAC;AACnD;;SCtMwBsK,qBAAqBA;EAC3C,IAAMC,mBAAmB,GAAGtI,sBAAsB,EAAE;EACpD,IAAA+D,qBAAA,GAAoClL,4BAA4B,EAAE;IAAzD0P,uBAAuB,GAAAxE,qBAAA;EAEhC,OAAO,SAASC,kBAAkBA,CAAC1F,OAAwB;IACzD,IAAAkK,iBAAA,GAAgBC,gBAAgB,CAACnK,OAAO,CAAC;MAAlC3P,KAAK,GAAA6Z,iBAAA;IAEZ,IAAI7Z,KAAK,EAAE;MACT2Z,mBAAmB,CAAChK,OAAO,CAAC;MAC5BiK,uBAAuB,CAAC5Z,KAAK,CAAC;;GAEjC;AACH;;SCLgB+Z,qBAAqBA;EACnC,IAAMC,4BAA4B,GAAGrL,+BAA+B,EAAE;EAEtE,OAAOqL,4BAA4B,KAAK9d,sBAAsB,CAACoI,MAAM;AACvE;AAEA,SAAgB2V,sBAAsBA;EACpC,IAAMD,4BAA4B,GAAGrL,+BAA+B,EAAE;EAEtE,OAAOqL,4BAA4B,KAAK9d,sBAAsB,CAACiI,OAAO;AACxE;;ACyBA,IAAK+V,cAQJ;AARD,WAAKA,cAAc;EACjBA,yCAAuB;EACvBA,qCAAmB;EACnBA,yCAAuB;EACvBA,2CAAyB;EACzBA,mCAAiB;EACjBA,iCAAe;EACfA,6BAAW;AACb,CAAC,EARIA,cAAc,KAAdA,cAAc;AAUnB,SAAgBC,qBAAqBA;EACnCC,2BAA2B,EAAE;EAC7BC,4BAA4B,EAAE;EAC9BC,+BAA+B,EAAE;EACjCC,mCAAmC,EAAE;EACrCC,qBAAqB,EAAE;AACzB;AAEA,SAASJ,2BAA2BA;EAClC,IAAM3J,aAAa,GAAGU,gBAAgB,EAAE;EACxC,IAAMkG,WAAW,GAAGJ,cAAc,EAAE;EACpC,IAAMpF,QAAQ,GAAGQ,WAAW,EAAE;EAC9B,IAAMzB,cAAc,GAAGa,iBAAiB,EAAE;EAC1C,IAAM2F,gBAAgB,GAAGT,mBAAmB,EAAE;EAC9C,IAAMd,cAAc,GAAGH,iBAAiB,EAAE;EAC1C,IAAMM,iBAAiB,GAAGF,oBAAoB,EAAE;EAEhD,IAAML,mBAAmB,GAAGN,sBAAsB,EAAE;EAEpD,IAAMsF,SAAS,GAAGzT,OAAO,CACvB;IAAA,OACE,SAASyT,SAASA,CAACC,KAAoB;MACrC,IAAQ3gB,GAAG,GAAK2gB,KAAK,CAAb3gB,GAAG;MAEXic,iBAAiB,EAAE;MACnB,QAAQjc,GAAG;;QAET,KAAKmgB,cAAc,CAACS,MAAM;UACxBD,KAAK,CAACE,cAAc,EAAE;UACtB,IAAI/E,cAAc,EAAE,EAAE;YACpBJ,mBAAmB,EAAE;YACrB;;UAEF4B,WAAW,EAAE;UACbxF,QAAQ,CAAC,CAAC,CAAC;UACXuF,gBAAgB,EAAE;UAClB;;KAEL;KACH,CACEvF,QAAQ,EACRwF,WAAW,EACX5B,mBAAmB,EACnB2B,gBAAgB,EAChBvB,cAAc,EACdG,iBAAiB,CAClB,CACF;EAEDpO,SAAS,CAAC;IACR,IAAMvB,OAAO,GAAGoK,aAAa,CAACpK,OAAO;IAErC,IAAI,CAACA,OAAO,EAAE;MACZ;;IAGFA,OAAO,CAACkQ,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;IAE9C,OAAO;MACLpU,OAAO,CAACqQ,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;KAClD;GACF,EAAE,CAAChK,aAAa,EAAEG,cAAc,EAAEiB,QAAQ,EAAE4I,SAAS,CAAC,CAAC;AAC1D;AAEA,SAASJ,4BAA4BA;EACnC,IAAMQ,mBAAmB,GAAGjE,sBAAsB,EAAE;EACpD,IAAMnG,aAAa,GAAGU,gBAAgB,EAAE;EACxC,IAAMR,OAAO,GAAGY,UAAU,EAAE;EAC5B,IAAMX,cAAc,GAAGa,iBAAiB,EAAE;EAC1C,IAAA6D,qBAAA,GAAoClL,uBAAuB,EAAE;IAApD0Q,uBAAuB,GAAAxF,qBAAA;EAChC,IAAMyF,qBAAqB,GAAGC,wBAAwB,EAAE;EACxD,IAAMC,kBAAkB,GAAGlB,qBAAqB,EAAE;EAElD,IAAMU,SAAS,GAAGzT,OAAO,CACvB;IAAA,OACE,SAASyT,SAASA,CAACC,KAAoB;MACrC,IAAQ3gB,GAAG,GAAK2gB,KAAK,CAAb3gB,GAAG;MAEX,QAAQA,GAAG;QACT,KAAKmgB,cAAc,CAACgB,UAAU;UAC5B,IAAI,CAACD,kBAAkB,EAAE;YACvB;;UAEFP,KAAK,CAACE,cAAc,EAAE;UACtBE,uBAAuB,CAAC,IAAI,CAAC;UAC7BD,mBAAmB,EAAE;UACrB;QACF,KAAKX,cAAc,CAACiB,SAAS;UAC3BT,KAAK,CAACE,cAAc,EAAE;UACtBG,qBAAqB,EAAE;UACvB;QACF,KAAKb,cAAc,CAACkB,KAAK;UACvBV,KAAK,CAACE,cAAc,EAAE;UACtBzH,8BAA8B,CAACxC,OAAO,CAACtK,OAAO,CAAC;UAC/C;;KAEL;KACH,CACEwU,mBAAmB,EACnBE,qBAAqB,EACrBD,uBAAuB,EACvBnK,OAAO,EACPsK,kBAAkB,CACnB,CACF;EAEDrT,SAAS,CAAC;IACR,IAAMvB,OAAO,GAAGuK,cAAc,CAACvK,OAAO;IAEtC,IAAI,CAACA,OAAO,EAAE;MACZ;;IAGFA,OAAO,CAACkQ,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;IAE9C,OAAO;MACLpU,OAAO,CAACqQ,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;KAClD;GACF,EAAE,CAAChK,aAAa,EAAEG,cAAc,EAAE6J,SAAS,CAAC,CAAC;AAChD;AAEA,SAASH,+BAA+BA;EACtC,IAAMzJ,iBAAiB,GAAGa,oBAAoB,EAAE;EAChD,IAAM0F,gBAAgB,GAAGT,mBAAmB,EAAE;EAC9C,IAAM/F,cAAc,GAAGa,iBAAiB,EAAE;EAC1C,IAAMsJ,qBAAqB,GAAGC,wBAAwB,EAAE;EACxD,IAAApF,sBAAA,GAA4BxL,uBAAuB,EAAE;IAA9CiR,MAAM,GAAAzF,sBAAA;IAAE0F,SAAS,GAAA1F,sBAAA;EACxB,IAAM2F,mBAAmB,GAAGtB,sBAAsB,EAAE;EACpD,IAAMgB,kBAAkB,GAAGlB,qBAAqB,EAAE;EAClD,IAAMyB,MAAM,GAAGC,SAAS,EAAE;EAE1B,IAAMhB,SAAS,GAAGzT,OAAO,CACvB;IAAA;;MAEE,SAASyT,SAASA,CAACC,KAAoB;QACrC,IAAQ3gB,GAAG,GAAK2gB,KAAK,CAAb3gB,GAAG;QAEX,IAAIkhB,kBAAkB,EAAE;UACtB,QAAQlhB,GAAG;YACT,KAAKmgB,cAAc,CAACwB,SAAS;cAC3BhB,KAAK,CAACE,cAAc,EAAE;cACtB,IAAI,CAACS,MAAM,EAAE;gBACX,OAAOjE,gBAAgB,EAAE;;cAE3BuE,iBAAiB,CAACvE,gBAAgB,CAAC;cACnC;YACF,KAAK8C,cAAc,CAACgB,UAAU;cAC5BR,KAAK,CAACE,cAAc,EAAE;cACtB,IAAI,CAACS,MAAM,EAAE;gBACX,OAAOjE,gBAAgB,EAAE;;cAE3BwE,iBAAiB,EAAE;cACnB;YACF,KAAK1B,cAAc,CAACiB,SAAS;cAC3BT,KAAK,CAACE,cAAc,EAAE;cACtB,IAAIS,MAAM,EAAE;gBACVC,SAAS,CAAC,KAAK,CAAC;;cAElBP,qBAAqB,EAAE;cACvB;YACF;cACES,MAAM,CAACd,KAAK,CAAC;cACb;;;QAIN,IAAIa,mBAAmB,EAAE;UACvB,QAAQxhB,GAAG;YACT,KAAKmgB,cAAc,CAAC2B,OAAO;cACzBnB,KAAK,CAACE,cAAc,EAAE;cACtB,IAAI,CAACS,MAAM,EAAE;gBACX,OAAOjE,gBAAgB,EAAE;;cAE3BuE,iBAAiB,CAACvE,gBAAgB,CAAC;cACnC;YACF,KAAK8C,cAAc,CAACiB,SAAS;cAC3BT,KAAK,CAACE,cAAc,EAAE;cACtB,IAAI,CAACS,MAAM,EAAE;gBACX,OAAOjE,gBAAgB,EAAE;;cAE3BwE,iBAAiB,EAAE;cACnB;YACF;cACEJ,MAAM,CAACd,KAAK,CAAC;cACb;;;;;KAIV,CACEW,MAAM,EACNjE,gBAAgB,EAChBkE,SAAS,EACTP,qBAAqB,EACrBS,MAAM,EACND,mBAAmB,EACnBN,kBAAkB,CACnB,CACF;EAEDrT,SAAS,CAAC;IACR,IAAMvB,OAAO,GAAGwK,iBAAiB,CAACxK,OAAO;IAEzC,IAAI,CAACA,OAAO,EAAE;MACZ;;IAGFA,OAAO,CAACkQ,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;IAE9C,OAAO;MACLpU,OAAO,CAACqQ,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;KAClD;GACF,EAAE,CAAC5J,iBAAiB,EAAED,cAAc,EAAEyK,MAAM,EAAEZ,SAAS,CAAC,CAAC;AAC5D;AAEA,SAASF,mCAAmCA;EAC1C,IAAMnD,gBAAgB,GAAGT,mBAAmB,EAAE;EAC9C,IAAM7F,qBAAqB,GAAGa,wBAAwB,EAAE;EACxD,IAAMhB,OAAO,GAAGY,UAAU,EAAE;EAC5B,IAAMiK,MAAM,GAAGC,SAAS,EAAE;EAE1B,IAAMhB,SAAS,GAAGzT,OAAO,CACvB;IAAA,OACE,SAASyT,SAASA,CAACC,KAAoB;MACrC,IAAQ3gB,GAAG,GAAK2gB,KAAK,CAAb3gB,GAAG;MAEX,QAAQA,GAAG;QACT,KAAKmgB,cAAc,CAAC2B,OAAO;UACzBnB,KAAK,CAACE,cAAc,EAAE;UACtBxD,gBAAgB,EAAE;UAClB;QACF,KAAK8C,cAAc,CAACgB,UAAU;UAC5BR,KAAK,CAACE,cAAc,EAAE;UACtB5K,uBAAuB,CAACK,gBAAgB,EAAE,CAAC;UAC3C;QACF,KAAK6J,cAAc,CAACwB,SAAS;UAC3BhB,KAAK,CAACE,cAAc,EAAE;UACtB9K,uBAAuB,CAACO,gBAAgB,EAAE,CAAC;UAC3C;QACF,KAAK6J,cAAc,CAACiB,SAAS;UAC3BT,KAAK,CAACE,cAAc,EAAE;UACtB5H,sBAAsB,CAACrC,OAAO,CAACtK,OAAO,CAAC;UACvC;QACF;UACEmV,MAAM,CAACd,KAAK,CAAC;UACb;;KAEL;KACH,CAAC/J,OAAO,EAAEyG,gBAAgB,EAAEoE,MAAM,CAAC,CACpC;EAED5T,SAAS,CAAC;IACR,IAAMvB,OAAO,GAAGyK,qBAAqB,CAACzK,OAAO;IAE7C,IAAI,CAACA,OAAO,EAAE;MACZ;;IAGFA,OAAO,CAACkQ,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;IAE9C,OAAO;MACLpU,OAAO,CAACqQ,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;KAClD;GACF,EAAE,CAAC3J,qBAAqB,EAAEH,OAAO,EAAE8J,SAAS,CAAC,CAAC;AACjD;AAEA,SAASD,qBAAqBA;EAC5B,IAAM7J,OAAO,GAAGY,UAAU,EAAE;EAC5B,IAAMuK,YAAY,GAAGC,eAAe,EAAE;EACtC,IAAM1G,kBAAkB,GAAGqE,qBAAqB,EAAE;EAClD,IAAM7D,cAAc,GAAGH,iBAAiB,EAAE;EAC1C,IAAMD,mBAAmB,GAAGN,sBAAsB,EAAE;EAEpD,IAAMqG,MAAM,GAAGC,SAAS,EAAE;EAE1B,IAAMhB,SAAS,GAAGzT,OAAO,CACvB;IAAA;;MAEE,SAASyT,SAASA,CAACC,KAAoB;QACrC,IAAQ3gB,GAAG,GAAK2gB,KAAK,CAAb3gB,GAAG;QAEX,IAAMwW,aAAa,GAAGyL,gBAAgB,CAAC3L,gBAAgB,EAAE,CAAC;QAE1D,QAAQtW,GAAG;UACT,KAAKmgB,cAAc,CAACgB,UAAU;YAC5BR,KAAK,CAACE,cAAc,EAAE;YACtBpH,qBAAqB,CAACjD,aAAa,CAAC;YACpC;UACF,KAAK2J,cAAc,CAACwB,SAAS;YAC3BhB,KAAK,CAACE,cAAc,EAAE;YACtBjH,qBAAqB,CAACpD,aAAa,CAAC;YACpC;UACF,KAAK2J,cAAc,CAACiB,SAAS;YAC3BT,KAAK,CAACE,cAAc,EAAE;YACtB,IAAI/E,cAAc,EAAE,EAAE;cACpBJ,mBAAmB,EAAE;cACrB;;YAEFxB,2BAA2B,CAAC1D,aAAa,CAAC;YAC1C;UACF,KAAK2J,cAAc,CAAC2B,OAAO;YACzBnB,KAAK,CAACE,cAAc,EAAE;YACtB,IAAI/E,cAAc,EAAE,EAAE;cACpBJ,mBAAmB,EAAE;cACrB;;YAEF3B,yBAAyB,CAACvD,aAAa,EAAEuL,YAAY,CAAC;YACtD;UACF,KAAK5B,cAAc,CAAC+B,KAAK;YACvBvB,KAAK,CAACE,cAAc,EAAE;YACtBvF,kBAAkB,CAACqF,KAAK,CAACpJ,MAAqB,CAAC;YAC/C;UACF;YACEkK,MAAM,CAACd,KAAK,CAAC;YACb;;;;KAGR,CACEoB,YAAY,EACZN,MAAM,EACNnG,kBAAkB,EAClBQ,cAAc,EACdJ,mBAAmB,CACpB,CACF;EAED7N,SAAS,CAAC;IACR,IAAMvB,OAAO,GAAGsK,OAAO,CAACtK,OAAO;IAE/B,IAAI,CAACA,OAAO,EAAE;MACZ;;IAGFA,OAAO,CAACkQ,gBAAgB,CAAC,SAAS,EAAEkE,SAAS,CAAC;IAE9C,OAAO;MACLpU,OAAO,CAACqQ,mBAAmB,CAAC,SAAS,EAAE+D,SAAS,CAAC;KAClD;GACF,EAAE,CAAC9J,OAAO,EAAE8J,SAAS,CAAC,CAAC;AAC1B;AAEA,SAASO,wBAAwBA;EAC/B,IAAMkB,uBAAuB,GAAGrF,0BAA0B,EAAE;EAC5D,IAAMsF,YAAY,GAAG3M,eAAe,EAAE;EACtC,IAAMmB,OAAO,GAAGY,UAAU,EAAE;EAE5B,OAAOe,WAAW,CAChB,SAASyI,qBAAqBA;IAC5B,IAAIoB,YAAY,EAAE;MAChB,OAAOnJ,sBAAsB,CAACrC,OAAO,CAACtK,OAAO,CAAC;;IAEhD,OAAO6V,uBAAuB,EAAE;GACjC,EACD,CAACvL,OAAO,EAAEuL,uBAAuB,EAAEC,YAAY,CAAC,CACjD;AACH;AAEA,SAASJ,eAAeA;EACtB,IAAM3E,gBAAgB,GAAGT,mBAAmB,EAAE;EAC9C,IAAMuF,uBAAuB,GAAGrF,0BAA0B,EAAE;EAC5D,IAAMsF,YAAY,GAAG3M,eAAe,EAAE;EAEtC,OAAO8C,WAAW,CAChB,SAAS8J,aAAaA;IACpB,IAAID,YAAY,EAAE;MAChB,OAAO/E,gBAAgB,EAAE;;IAE3B,OAAO8E,uBAAuB,EAAE;GACjC,EACD,CAAC9E,gBAAgB,EAAE+E,YAAY,EAAED,uBAAuB,CAAC,CAC1D;AACH;AAEA,SAASP,iBAAiBA,CAACU,QAAoB;EAC7C,IAAMC,eAAe,GAAGjM,gBAAgB,EAAE;EAE1C,IAAI,CAACiM,eAAe,EAAE;IACpB;;EAGF,IAAI,CAACC,qBAAqB,CAACD,eAAe,CAAC,EAAE;IAC3CD,QAAQ,EAAE;;EAGZrM,uBAAuB,CAACsM,eAAe,CAAC;AAC1C;AAEA,SAASV,iBAAiBA;EACxB,IAAMU,eAAe,GAAGjM,gBAAgB,EAAE;EAE1C,IAAI,CAACiM,eAAe,EAAE;IACpB;;EAGFxM,uBAAuB,CAACwM,eAAe,CAAC;AAC1C;AAEA,SAASb,SAASA;EAChB,IAAMlE,YAAY,GAAGD,eAAe,EAAE;EACtC,IAAMF,gBAAgB,GAAGT,mBAAmB,EAAE;EAC9C,IAAMlb,cAAc,GAAGgT,uBAAuB,EAAE;EAChD,IAAMgH,mBAAmB,GAAGN,sBAAsB,EAAE;EAEpD,OAAO,SAASqG,MAAMA,CAACd,KAAoB;IACzC,IAAQ3gB,GAAG,GAAK2gB,KAAK,CAAb3gB,GAAG;IAEX,IAAIyiB,WAAW,CAAC9B,KAAK,CAAC,IAAIjf,cAAc,EAAE;MACxC;;IAGF,IAAI1B,GAAG,CAAC0iB,KAAK,CAAC,oBAAoB,CAAC,EAAE;MACnC/B,KAAK,CAACE,cAAc,EAAE;MACtBnF,mBAAmB,EAAE;MACrB2B,gBAAgB,EAAE;MAClBG,YAAY,CAACxd,GAAG,CAAC;;GAEpB;AACH;AAEA,SAASyiB,WAAWA,CAAC9B,KAAoB;EACvC,IAAQgC,OAAO,GAAsBhC,KAAK,CAAlCgC,OAAO;IAAEC,OAAO,GAAajC,KAAK,CAAzBiC,OAAO;IAAEC,MAAM,GAAKlC,KAAK,CAAhBkC,MAAM;EAEhC,OAAOF,OAAO,IAAIC,OAAO,IAAIC,MAAM;AACrC;;SCzdgBC,YAAYA,CAC1BzY,WAAwB,EACxBpE,KAA4B,EAC5B7E,UAAsB;EAEtB,IAAI,CAAC6E,KAAK,EAAE;IACV;;EAGF,IAAI7E,UAAU,KAAKW,UAAU,CAACghB,MAAM,EAAE;IACpC;;EAGF,IAAM5b,OAAO,GAAGP,YAAY,CAACX,KAAK,CAAC;EAEnC,IAAI+c,cAAc,CAACpW,GAAG,CAACzF,OAAO,CAAC,EAAE;IAC/B;;EAGFW,eAAe,CAAC7B,KAAK,CAAC,CAACQ,OAAO,CAAC,UAACyB,SAAS;IACvC,IAAM+a,QAAQ,GAAG5Y,WAAW,CAACnC,SAAS,EAAE9G,UAAU,CAAC;IACnD8hB,YAAY,CAACD,QAAQ,CAAC;GACvB,CAAC;EAEFD,cAAc,CAAC9Y,GAAG,CAAC/C,OAAO,CAAC;AAC7B;AAEA,AAAO,IAAM6b,cAAc,gBAAgB,IAAIvY,GAAG,EAAE;AAEpD,SAASyY,YAAYA,CAACC,GAAW;EAC/B,IAAMC,KAAK,GAAG,IAAIC,KAAK,EAAE;EACzBD,KAAK,CAACE,GAAG,GAAGH,GAAG;AACjB;;SC3BgBI,UAAUA;EACxB,IAAM3M,OAAO,GAAGY,UAAU,EAAE;EAC5B,IAAMpW,UAAU,GAAG6Q,mBAAmB,EAAE;EACxC,IAAM5H,WAAW,GAAG4K,oBAAoB,EAAE;EAE1CpH,SAAS,CAAC;IACR,IAAIzM,UAAU,KAAKW,UAAU,CAACghB,MAAM,EAAE;MACpC;;IAGF,IAAMxG,OAAO,GAAG3F,OAAO,CAACtK,OAAO;IAE/BiQ,OAAO,oBAAPA,OAAO,CAAEC,gBAAgB,CAAC,SAAS,EAAEgH,OAAO,CAAC;IAE7C,OAAO;MACLjH,OAAO,oBAAPA,OAAO,CAAEI,mBAAmB,CAAC,SAAS,EAAE6G,OAAO,CAAC;KACjD;IAED,SAASA,OAAOA,CAAC7C,KAAiB;MAChC,IAAM8C,MAAM,GAAGxB,gBAAgB,CAACtB,KAAK,CAACpJ,MAAqB,CAAC;MAE5D,IAAI,CAACkM,MAAM,EAAE;QACX;;MAGF,IAAA3D,iBAAA,GAAgBC,gBAAgB,CAAC0D,MAAM,CAAC;QAAjCxd,KAAK,GAAA6Z,iBAAA;MAEZ,IAAI,CAAC7Z,KAAK,EAAE;QACV;;MAGF,IAAIsB,kBAAkB,CAACtB,KAAK,CAAC,EAAE;QAC7B6c,YAAY,CAACzY,WAAW,EAAEpE,KAAK,EAAE7E,UAAU,CAAC;;;GAGjD,EAAE,CAACwV,OAAO,EAAExV,UAAU,EAAEiJ,WAAW,CAAC,CAAC;AACxC;;;AC9CA,AAwBO,IAAMqZ,oBAAoB,GAAG,EAAE;AAEtC,SAAwBC,UAAUA,CAAApe,IAAA;MAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;EAC3C,OACEhM,cAAC6O,qBAAqB,QACpB7O,cAAC2kB,iBAAiB,QAAE3Y,QAAQ,CAAqB,CAC3B;AAE5B;AAQA,SAAS2Y,iBAAiBA,CAAAC,KAAA;;MAAG5Y,QAAQ,GAAA4Y,KAAA,CAAR5Y,QAAQ;EACnC,IAAA6H,qBAAA,GAAwBrD,qBAAqB,EAAE;IAAxCqU,aAAa,GAAAhR,qBAAA;EACpB,IAAMzR,KAAK,GAAGwS,cAAc,EAAE;EAC9B,IAAMkQ,gBAAgB,GAAGtO,eAAe,EAAE;EAC1C,IAAMiB,aAAa,GAAGU,gBAAgB,EAAE;EACxC,IAAM5V,SAAS,GAAG2S,kBAAkB,EAAE;EACtC,IAAM1S,KAAK,GAAG4S,cAAc,EAAE;EAE9B+L,qBAAqB,EAAE;EACvBmD,UAAU,EAAE;EAEZ,IAAAS,KAAA,GAAyCviB,KAAK,IAAI,EAAE;IAA5C3B,KAAK,GAAAkkB,KAAA,CAALlkB,KAAK;IAAED,MAAM,GAAAmkB,KAAA,CAANnkB,MAAM;IAAKokB,UAAU,GAAA/Y,6BAAA,CAAA8Y,KAAA,EAAA7Y,WAAA;EAEpC,OACElM;IACEuC,SAAS,EAAE0iB,EAAE,CACXC,MAAM,CAACC,IAAI,EACXD,MAAM,CAACE,aAAa,EACpBhjB,KAAK,KAAKW,KAAK,CAACkD,IAAI,IAAIif,MAAM,CAACG,SAAS,EACxCjjB,KAAK,KAAKW,KAAK,CAACuiB,IAAI,IAAIJ,MAAM,CAACK,aAAa,GAAAC,GAAA,OAAAA,GAAA,CAEzC9mB,UAAU,CAAC+mB,YAAY,IAAGX,gBAAgB,EAAAU,GAAA,GAE7CX,aAAa,IAAIK,MAAM,CAACQ,aAAa,EACrCnjB,SAAS,CACV;IACDojB,GAAG,EAAElO,aAAa;IAClBjV,KAAK,EAAA1C,QAAA,KACAklB,UAAU,EACT,CAACH,aAAa,IAAI;MAAEjkB,MAAM,EAANA,MAAM;MAAEC,KAAK,EAALA;KAAO;KAGxCmL,QAAQ,CACH;AAEZ;AAEA,IAAM4Z,SAAS,GAAG;EAChB,uCAAuC,EACrC,iDAAiD;EACnD,sCAAsC,EACpC,gDAAgD;EAClD,uBAAuB,EAAE,iCAAiC;EAC1D,kBAAkB,EAAE,4BAA4B;EAChD,sBAAsB,EAAE,gCAAgC;EACxD,sBAAsB,EAAE,gCAAgC;EACxD,6BAA6B,EAAE,uCAAuC;EACtE,+BAA+B,EAAE,yCAAyC;EAC1E,2BAA2B,EAAE,qCAAqC;EAClE,gBAAgB,EAAE,0BAA0B;EAC5C,0BAA0B,EAAE,oCAAoC;EAChE,oCAAoC,EAClC,8CAA8C;EAChD,uCAAuC,EACrC,iDAAiD;EACnD,kCAAkC,EAChC,4CAA4C;EAC9C,mCAAmC,EACjC,6CAA6C;EAC/C,oCAAoC,EAAE,8CAA8C;EACpF,oCAAoC,EAAE;CACvC;AAED,IAAMV,MAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/BslB,IAAI,EAAE;IACJ,GAAG,EAAE,CAAC,UAAU,EAAEzmB,UAAU,CAACmnB,WAAW,CAAC;IACzCC,QAAQ,EAAE,UAAU;IACpBvmB,OAAO,EAAE,MAAM;IACfwmB,aAAa,EAAE,QAAQ;IACvBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,OAAO;IACpBC,YAAY,EAAE,iCAAiC;IAC/CC,WAAW,EAAE,gCAAgC;IAC7CC,eAAe,EAAE,qBAAqB;IACtCzmB,QAAQ,EAAE,QAAQ;IAClBgB,UAAU,EAAE,yDAAyD;IACrE,GAAG,EAAE;MACH0lB,SAAS,EAAE,YAAY;MACvBC,UAAU,EAAE;;GAEf;EACDlB,aAAa,EAAE;IACb,IAAI,EAAE;MACJ,uBAAuB,EAAE,SAAS;MAClC,sBAAsB,EAAE,SAAS;MACjC,sCAAsC,EAAE,WAAW;MACnD,sBAAsB,EAAE,SAAS;MACjC,kBAAkB,EAAE,SAAS;MAC7B,6BAA6B,EAAE,SAAS;MACxC,2BAA2B,EAAE,SAAS;MACtC,gBAAgB,EAAE,MAAM;MACxB,0BAA0B,EAAE,WAAW;MACvC,kCAAkC,EAAE,SAAS;MAC7C,mCAAmC,EAAE,WAAW;MAChD,oCAAoC,EAAE,SAAS;MAC/C,oCAAoC,EAAE,qBAAqB;MAE3D,0BAA0B,EAAE,MAAM;MAElC,4BAA4B,EAAE,KAAK;;MAGnC,2BAA2B,EAAE,4BAA4B;MACzD,sBAAsB,EAAE,oCAAoC;;MAG5D,+CAA+C,EAC7C,4BAA4B;MAC9B,+BAA+B,EAAE,2BAA2B;;MAG5D,oCAAoC,EAAE,kCAAkC;MACxE,4BAA4B,EAAE,QAAQ;MACtC,kCAAkC,EAAE,KAAK;MACzC,2BAA2B,EAAE,MAAM;MACnC,+BAA+B,EAAE,uBAAuB;MACxD,sCAAsC,EAAE,uBAAuB;MAC/D,gCAAgC,EAAE,+BAA+B;;MAGjE,uCAAuC,EAAE,MAAM;;MAG/C,qCAAqC,EAAE,MAAM;MAC7C,uCAAuC,EAAE,qBAAqB;;MAG9D,sBAAsB,EAAE,MAAM;MAC9B,yBAAyB,EAAE,MAAM;MACjC,4BAA4B,EAAE,iCAAiC;MAC/D,4BAA4B,EAAE,gCAAgC;MAC9D,0BAA0B,EAAE,uBAAuB;;MAGnD,wBAAwB,EAAE,iCAAiC;;MAG3D,+BAA+B,EAAE,WAAW;MAC5C,iCAAiC,EAAE,uBAAuB;MAC1D,8BAA8B,EAAE,iCAAiC;MACjE,6BAA6B,EAAKX,oBAAoB,OAAI;;MAG1D,kBAAkB,EAAE,MAAM;MAC1B,qBAAqB,EAAE,KAAK;MAC5B,sBAAsB,EACpB,4DAA4D;MAC9D,yBAAyB,EAAE,2BAA2B;MACtD,uCAAuC,EAAE,gCAAgC;MACzE,6CAA6C,EAAE,uBAAuB;;MAGtE,8BAA8B,EAAE,GAAG;MACnC,2CAA2C,EAAE,GAAG;MAChD,8BAA8B,EAAE,GAAG;MACnC,qCAAqC,EAAE,GAAG;MAC1C,uBAAuB,EAAE,GAAG;;MAG5B,YAAY,EAAE,MAAM;MACpB,4CAA4C,EAAE,iBAAiB;MAC/D,4BAA4B,EAAE,SAAS;MACvC,uBAAuB,EAAE,4BAA4B;MACrD,2BAA2B,EAAE,WAAW;MACxC,2CAA2C,EAAE,WAAW;MACxD,2BAA2B,EAAE,SAAS;MACtC,kCAAkC,EAAE,SAAS;MAC7C,oCAAoC,EAAE,WAAW;MACjD,gCAAgC,EAAE,SAAS;MAC3C,qBAAqB,EAAE,SAAS;MAChC,+BAA+B,EAAE,WAAW;MAC5C,yCAAyC,EAAE,iBAAiB;MAC5D,4CAA4C,EAAE,MAAM;MACpD,uCAAuC,EAAE,SAAS;MAClD,wCAAwC,EAAE,WAAW;MACrD,yCAAyC,EAAE,qCAAqC;MAChF,yCAAyC,EAAE;;GAE9C;EACDc,aAAa,EAAE;IACb,GAAG,EAAE7mB,UAAU,CAAC6nB,SAAS;IACzB,qCAAqC,EAAE;MACrC,IAAI,EAAEX;;GAET;EACDP,SAAS,EAAE;IACT,GAAG,EAAE3mB,UAAU,CAAC2mB,SAAS;IACzB,IAAI,EAAEO;GACP;EACDF,aAAa,EAAE;IACb,GAAG,EAAE,eAAe;IACpB9kB,MAAM,EAAE,MAAM;IACdrB,OAAO,EAAE,aAAa;IACtB6mB,eAAe,EAAE,+BAA+B;;IAEhDI,cAAc,EAAE,WAAW;IAC3B,IAAI,EAAE;MACJ,4BAA4B,EAAE;;;CAGnC,CAAC;;SC3Oc9K,iBAAiBA,CAC/BzB,MAAuB,EACvBtD,OAAwB;EAExB,IAAI,CAACsD,MAAM,IAAI,CAACtD,OAAO,EAAE;IACvB,OAAO,CAAC;;EAGV,IAAM8P,WAAW,GAAGxM,MAAM,CAACyM,qBAAqB,EAAE,CAAC7lB,KAAK;EACxD,IAAM8lB,YAAY,GAAGhQ,OAAO,CAAC+P,qBAAqB,EAAE,CAAC7lB,KAAK;EAC1D,OAAO+lB,IAAI,CAACC,KAAK,CAACJ,WAAW,GAAGE,YAAY,CAAC;AAC/C;AAEA,SAAgBrL,iBAAiBA,CAC/BrB,MAAuB,EACvBtD,OAAwB;EAExB,IAAI,CAACsD,MAAM,IAAI,CAACtD,OAAO,EAAE;IACvB,OAAO,CAAC;;EAGV,IAAMgQ,YAAY,GAAGhQ,OAAO,CAAC+P,qBAAqB,EAAE,CAAC7lB,KAAK;EAC1D,IAAMimB,WAAW,GAAGnQ,OAAO,CAAC+P,qBAAqB,EAAE,CAACK,IAAI;EACxD,IAAMC,UAAU,GAAG/M,MAAM,CAACyM,qBAAqB,EAAE,CAACK,IAAI;EAEtD,OAAOH,IAAI,CAACC,KAAK,CAAC,CAACC,WAAW,GAAGE,UAAU,IAAIL,YAAY,CAAC;AAC9D;AAEA,SAAgBnL,SAASA,CACvBvB,MAAuB,EACvBtD,OAAwB;EAExB,IAAI,CAACsD,MAAM,IAAI,CAACtD,OAAO,EAAE;IACvB,OAAO,CAAC;;EAGV,IAAMsQ,aAAa,GAAGtQ,OAAO,CAAC+P,qBAAqB,EAAE,CAAC9lB,MAAM;EAC5D,IAAMsmB,UAAU,GAAGvQ,OAAO,CAAC+P,qBAAqB,EAAE,CAAC3N,GAAG;EACtD,IAAMoO,SAAS,GAAGlN,MAAM,CAACyM,qBAAqB,EAAE,CAAC3N,GAAG;EACpD,OAAO6N,IAAI,CAACQ,KAAK,CAAC,CAACF,UAAU,GAAGC,SAAS,IAAIF,aAAa,CAAC;AAC7D;AAEA,SAAgBlL,UAAUA,CACxB9B,MAAuB,EACvBtD,OAAwB;EAExB,IAAI,CAACsD,MAAM,IAAI,CAACtD,OAAO,EAAE;IACvB,OAAO,KAAK;;EAGd,IAAMsQ,aAAa,GAAGtQ,OAAO,CAAC+P,qBAAqB,EAAE,CAAC9lB,MAAM;EAC5D,IAAMsmB,UAAU,GAAGvQ,OAAO,CAAC+P,qBAAqB,EAAE,CAAC3N,GAAG;EACtD,IAAMoO,SAAS,GAAGlN,MAAM,CAACyM,qBAAqB,EAAE,CAAC3N,GAAG;EACpD,IAAMsO,YAAY,GAAGpN,MAAM,CAACyM,qBAAqB,EAAE,CAAC9lB,MAAM;EAE1D,OAAOgmB,IAAI,CAACQ,KAAK,CAACF,UAAU,GAAGC,SAAS,GAAGF,aAAa,CAAC,GAAGI,YAAY;AAC1E;AAEA,SAASC,cAAcA,CACrBC,QAAuB,EACvBhM,GAAW,EACXiM,aAAqB;EAErB,IAAIjM,GAAG,KAAK,CAAC,CAAC,EAAE;IACd,IAAMkM,OAAO,GAAGb,IAAI,CAACC,KAAK,CAAC,CAACU,QAAQ,CAAC5kB,MAAM,GAAG,CAAC,IAAI6kB,aAAa,CAAC;IACjE,IAAME,iBAAiB,GAAGD,OAAO,GAAGD,aAAa;IACjD,IAAMG,gBAAgB,GAAGJ,QAAQ,CAAC5kB,MAAM,GAAG,CAAC;IAC5C,OAAO4kB,QAAQ,CAACK,KAAK,CAACF,iBAAiB,EAAEC,gBAAgB,GAAG,CAAC,CAAC;;EAGhE,OAAOJ,QAAQ,CAACK,KAAK,CAACrM,GAAG,GAAGiM,aAAa,EAAE,CAACjM,GAAG,GAAG,CAAC,IAAIiM,aAAa,CAAC;AACvE;AAEA,SAASK,kBAAkBA,CACzBC,WAA0B,EAC1BC,UAAkB,EAClBP,aAAqB;EAErB,IAAMQ,OAAO,GAAGD,UAAU,GAAG,CAAC;EAE9B,IAAIC,OAAO,GAAGR,aAAa,GAAGM,WAAW,CAACnlB,MAAM,EAAE;IAChD,OAAO,EAAE;;EAGX,OAAO2kB,cAAc,CAACQ,WAAW,EAAEE,OAAO,EAAER,aAAa,CAAC;AAC5D;AAEA,SAAgB5L,eAAeA,CAC7B2L,QAAuB,EACvBhM,GAAW,EACXiM,aAAqB,EACrBnM,UAAkB;EAElB,IAAM4M,WAAW,GAAGX,cAAc,CAACC,QAAQ,EAAEhM,GAAG,EAAEiM,aAAa,CAAC;;EAEhE,OAAOS,WAAW,CAAC5M,UAAU,CAAC,IAAI4M,WAAW,CAACA,WAAW,CAACtlB,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;AAC/E;AAEA,SAAgBuZ,mBAAmBA,CACjC4L,WAA0B,EAC1BC,UAAkB,EAClBP,aAAqB,EACrBU,KAAa;EAEb,IAAMC,eAAe,GAAGN,kBAAkB,CACxCC,WAAW,EACXC,UAAU,EACVP,aAAa,CACd;;EAGD,OACEW,eAAe,CAACD,KAAK,CAAC,IACtBC,eAAe,CAACA,eAAe,CAACxlB,MAAM,GAAG,CAAC,CAAC,IAC3C,IAAI;AAER;AAEA,SAAgBmZ,mBAAmBA,CACjCgM,WAA0B,EAC1BC,UAAkB,EAClBP,aAAqB,EACrBU,KAAa;EAEb,IAAME,eAAe,GAAGd,cAAc,CACpCQ,WAAW,EACXC,UAAU,GAAG,CAAC,EACdP,aAAa,CACd;;EAGD,OACEY,eAAe,CAACF,KAAK,CAAC,IACtBE,eAAe,CAACA,eAAe,CAACzlB,MAAM,GAAG,CAAC,CAAC,IAC3C,IAAI;AAER;AAEA,SAAgB0lB,8BAA8BA,CAC5CpO,MAAuB,EACvBsN,QAAuB,EACvBe,0BAA0B;MAA1BA,0BAA0B;IAA1BA,0BAA0B,GAAG,CAAC;;EAE9B,IAAI,CAACrO,MAAM,IAAI,CAACsN,QAAQ,CAAC5kB,MAAM,EAAE;IAC/B,OAAO,IAAI;;EAGb,IAAMwkB,SAAS,GAAGlN,MAAM,CAACyM,qBAAqB,EAAE,CAAC3N,GAAG;EACpD,IAAMwP,YAAY,GAAGtO,MAAM,CAACyM,qBAAqB,EAAE,CAAC8B,MAAM;EAC1D,IAAMC,kBAAkB,GAAGtB,SAAS,GAAGuB,cAAc,CAACzO,MAAM,CAAC;EAE7D,IAAM0O,eAAe,GAAGpB,QAAQ,CAACve,IAAI,CAAC,UAAA2N,OAAO;IAC3C,IAAMuQ,UAAU,GAAGvQ,OAAO,CAAC+P,qBAAqB,EAAE,CAAC3N,GAAG;IACtD,IAAM6P,aAAa,GAAGjS,OAAO,CAAC+P,qBAAqB,EAAE,CAAC8B,MAAM;IAC5D,IAAMK,uBAAuB,GAC3BlS,OAAO,CAACmS,YAAY,GAAGR,0BAA0B;IAEnD,IAAMS,yBAAyB,GAAG7B,UAAU,GAAG2B,uBAAuB;IACtE,IAAMG,4BAA4B,GAChCJ,aAAa,GAAGC,uBAAuB;IAEzC,IAAIE,yBAAyB,GAAGN,kBAAkB,EAAE;MAClD,OAAO,KAAK;;IAGd,OACGM,yBAAyB,IAAI5B,SAAS,IACrC4B,yBAAyB,IAAIR,YAAY,IAC1CS,4BAA4B,IAAI7B,SAAS,IACxC6B,4BAA4B,IAAIT,YAAa;GAElD,CAAC;EAEF,OAAOI,eAAe,IAAI,IAAI;AAChC;AAEA,SAAgBpF,qBAAqBA,CAAC5M,OAAoB;EACxD,OAAO,CAAC,CAACA,OAAO,CAACM,kBAAkB;AACrC;AAEA,SAASyR,cAAcA,CAACO,UAAuB;EAC7C,IAAMC,MAAM,GAAGrqB,KAAK,CAACsqB,IAAI,CACvBF,UAAU,CAACG,gBAAgB,CAACzqB,WAAW,CAACD,UAAU,CAAC2qB,KAAK,CAAC,CAAC,CAC3D;EAED,SAAAC,EAAA,MAAAC,OAAA,GAAoBL,MAAM,EAAAI,EAAA,GAAAC,OAAA,CAAA5mB,MAAA,EAAA2mB,EAAA,IAAE;IAAvB,IAAMD,KAAK,GAAAE,OAAA,CAAAD,EAAA;IACd,IAAM1oB,MAAM,GAAGyoB,KAAK,CAAC3C,qBAAqB,EAAE,CAAC9lB,MAAM;;IAEnD,IAAIA,MAAM,GAAG,CAAC,EAAE;MACd,OAAOA,MAAM;;;EAIjB,OAAO6jB,oBAAoB;AAC7B;;AC5LO,IAAM+E,mBAAmB,2BAAY7qB,WAAW,CAACD,UAAU,CAACsI,KAAK,CAAG;AAC3E,AAAO,IAAMyiB,oBAAoB,gBAAG,CAClCD,mBAAmB,EACnB7qB,WAAW,CAACD,UAAU,CAACgrB,OAAO,CAAC,YACvB/qB,WAAW,CAACD,UAAU,CAACY,MAAM,CAAC,OACvC,CAACH,IAAI,CAAC,EAAE,CAAC;AAEV,SAAgB6jB,gBAAgBA,CAC9B2G,YAA6B;;EAE7B,QAAAC,qBAAA,GAAOD,YAAY,oBAAZA,YAAY,CAAElQ,OAAO,CAAC+P,mBAAmB,CAAC,YAAAI,qBAAA,GAAI,IAAI;AAC3D;AAEA,SAQgB9I,gBAAgBA,CAC9BnK,OAAwB;EAExB,IAAMkT,eAAe,GAAGC,+BAA+B,CAACnT,OAAO,CAAC;EAChE,IAAMzO,OAAO,GAAG6hB,uBAAuB,CAACpT,OAAO,CAAC;EAEhD,IAAI,CAACkT,eAAe,EAAE;IACpB,OAAO,EAAE;;EAGX,IAAM7iB,KAAK,GAAGmC,cAAc,CAACjB,OAAO,WAAPA,OAAO,GAAI2hB,eAAe,CAAC;EAExD,IAAI,CAAC7iB,KAAK,EAAE;IACV,OAAO,EAAE;;EAGX,OAAO,CAACA,KAAK,EAAEkB,OAAiB,CAAC;AACnC;AAEA,SAAgB8hB,cAAcA,CAACrT,OAAwB;;EACrD,OAAOnI,OAAO,CACZ,CAAAmI,OAAO,oBAAPA,OAAO,CAAEsT,OAAO,CAACT,mBAAmB,CAAC,MACnC7S,OAAO,qBAAAuT,qBAAA,GAAPvT,OAAO,CAAEwT,aAAa,qBAAtBD,qBAAA,CAAwBD,OAAO,CAACT,mBAAmB,CAAC,EACvD;AACH;AAEA,SAagBvC,aAAaA,CAACtQ,OAAwB;;EACpD,QAAAyT,qBAAA,GAAOzT,OAAO,oBAAPA,OAAO,CAAEmS,YAAY,YAAAsB,qBAAA,GAAI,CAAC;AACnC;AAEA,SAAgBC,kBAAkBA,CAAC1T,OAAwB;EACzD,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,CAAC;;EAGV,IAAM6N,MAAM,GAAGxB,gBAAgB,CAACrM,OAAO,CAAC;EACxC,IAAM3S,QAAQ,GAAG+V,eAAe,CAACyK,MAAM,CAAC;;EAGxC,IAAM8F,WAAW,GAAGxQ,mBAAmB,CAAC9V,QAAQ,CAAC;EAEjD,OAAOumB,gBAAgB,CAAC/F,MAAM,CAAC,GAAG+F,gBAAgB,CAACvmB,QAAQ,CAAC,GAAGsmB,WAAW;AAC5E;AAEA,SAAgBxQ,mBAAmBA,CAAC9V,QAAyB;;EAC3D,IAAI,CAACA,QAAQ,EAAE;IACb,OAAO,CAAC;;EAGV,IAAMwmB,oBAAoB,GAAGxmB,QAAQ,CAACymB,aAAa,CACjD9rB,WAAW,CAACD,UAAU,CAACyc,eAAe,CAAC,CACxC;EAED,OACE,EAAAuP,qBAAA,GAAC1mB,QAAQ,oBAARA,QAAQ,CAAE8kB,YAAY,YAAA4B,qBAAA,GAAI,CAAC,MAAAC,qBAAA,GAAKH,oBAAoB,oBAApBA,oBAAoB,CAAE1B,YAAY,YAAA6B,qBAAA,GAAI,CAAC,CAAC;AAE7E;AAEA,SAAgBnR,kBAAkBA,CAACxS,KAAsB;EACvD,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,KAAK;;EAGd,OACE6S,0BAA0B,CAAC7S,KAAK,CAAC,GACjC8S,mBAAmB,CAACC,eAAe,CAAC/S,KAAK,CAAC,CAAC;AAE/C;AAEA,SAAgBiS,eAAeA,CAACH,IAAqB;EACnD,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;EAEtB,OAAOA,IAAI,CAACmR,OAAO,CAACtrB,WAAW,CAACD,UAAU,CAACib,UAAU,CAAC,CAAC,GACnDb,IAAI,GACJA,IAAI,CAAC2R,aAAa,CAAC9rB,WAAW,CAACD,UAAU,CAACib,UAAU,CAAC,CAAC;AAC5D;AAEA,SAAgBE,0BAA0BA,CAAC7S,KAAsB;;EAC/D,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,CAAC;;EAGV,OAAOqjB,kBAAkB,CAACrjB,KAAK,CAAC,KAAA4jB,qBAAA,IAAAC,kBAAA,GAAIjR,iBAAiB,CAAC5S,KAAK,CAAC,qBAAxB6jB,kBAAA,CAA0B3R,SAAS,YAAA0R,qBAAA,GAAI,CAAC,CAAC;AAC/E;AAEA,SAAgBhR,iBAAiBA,CAACjD,OAAwB;;EACxD,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;;EAGb,QAAAmU,gBAAA,GAAOnU,OAAO,CAAC8C,OAAO,CAAC9a,WAAW,CAACD,UAAU,CAACib,UAAU,CAAC,CAAC,YAAAmR,gBAAA,GAAI,IAAI;AACpE;AAEA,SAAgBC,kBAAkBA,CAACpU,OAAwB;EACzD,IAAM6N,MAAM,GAAGxB,gBAAgB,CAACrM,OAAO,CAAC;EACxC,IAAM3S,QAAQ,GAAG+V,eAAe,CAACyK,MAAM,CAAC;EAExC,OAAOwG,iBAAiB,CAACxG,MAAM,CAAC,GAAGwG,iBAAiB,CAAChnB,QAAQ,CAAC;AAChE;AAEA,SAASumB,gBAAgBA,CAAC5T,OAAwB;;EAChD,QAAAsU,kBAAA,GAAOtU,OAAO,oBAAPA,OAAO,CAAEuU,SAAS,YAAAD,kBAAA,GAAI,CAAC;AAChC;AAEA,SAASD,iBAAiBA,CAACrU,OAAwB;;EACjD,QAAAwU,mBAAA,GAAOxU,OAAO,oBAAPA,OAAO,CAAEyU,UAAU,YAAAD,mBAAA,GAAI,CAAC;AACjC;AAEA,SAAgBpB,uBAAuBA,CAAC/iB,KAAsB;;EAC5D,QAAAqkB,kBAAA,GAAOC,iBAAiB,CAACtI,gBAAgB,CAAChc,KAAK,CAAC,EAAE,SAAS,CAAC,YAAAqkB,kBAAA,GAAI,IAAI;AACtE;AAEA,SAAgBvB,+BAA+BA,CAC7C9iB,KAAsB;EAEtB,IAAMkB,OAAO,GAAG6hB,uBAAuB,CAAC/iB,KAAK,CAAC;EAE9C,IAAIkB,OAAO,EAAE;IACX,OAAOD,sBAAsB,CAACC,OAAO,CAAC;;EAExC,OAAO,IAAI;AACb;AAEA,SAAgBqjB,0BAA0BA,CACxCvkB,KAAsB;EAEtB,IAAI,CAACA,KAAK,EAAE;IACV,OAAO;MACLkB,OAAO,EAAE,IAAI;MACb2hB,eAAe,EAAE;KAClB;;EAGH,OAAO;IACL3hB,OAAO,EAAE6hB,uBAAuB,CAAC/iB,KAAK,CAAC;IACvC6iB,eAAe,EAAEC,+BAA+B,CAAC9iB,KAAK;GACvD;AACH;AAEA,SAASskB,iBAAiBA,CACxB3U,OAAwB,EACxB5V,GAAW;;EAEX,QAAAyqB,mBAAA,GAAOC,cAAc,CAAC9U,OAAO,CAAC,CAAC5V,GAAG,CAAC,YAAAyqB,mBAAA,GAAI,IAAI;AAC7C;AAEA,SAASC,cAAcA,CAAC9U,OAAwB;;EAC9C,QAAA+U,gBAAA,GAAO/U,OAAO,oBAAPA,OAAO,CAAEgV,OAAO,YAAAD,gBAAA,GAAI,EAAE;AAC/B;AAEA,SAAgBE,cAAcA,CAACjV,OAAoB;EACjD,OAAOA,OAAO,CAACkV,SAAS,CAACC,QAAQ,CAACptB,UAAU,CAACgrB,OAAO,CAAC;AACvD;AAEA,SAAgBqC,QAAQA,CAACpV,OAAwB;EAC/C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;EAEzB,OAAOA,OAAO,CAACkV,SAAS,CAACC,QAAQ,CAACptB,UAAU,CAACY,MAAM,CAAC;AACtD;AAEA,SAAgBuc,gBAAgBA,CAAC5B,MAAuB;EACtD,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,EAAE;;EAGX,OAAOpb,KAAK,CAACsqB,IAAI,CACflP,MAAM,CAACmP,gBAAgB,CAACK,oBAAoB,CAAC,CAC7B;AACpB;AAEA,SAAgBlP,gBAAgBA,CAAC5D,OAAwB;EACvD,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;EAEzB,IAAM7P,SAAS,GAAG+U,gBAAgB,CAAClF,OAAO,CAAC;EAC3C,IAAAqV,gBAAA,GAAellB,SAAS,CAAC8gB,KAAK,CAAC,CAAC,CAAC,CAAC;IAA3BqE,IAAI,GAAAD,gBAAA;EACX,IAAI,CAACC,IAAI,EAAE;IACT,OAAO,IAAI;;EAGb,IAAI,CAACL,cAAc,CAACK,IAAI,CAAC,EAAE;IACzB,OAAOrR,gBAAgB,CAACqR,IAAI,CAAC;;EAG/B,OAAOA,IAAI;AACb;AAEA,SAAgBxR,gBAAgBA,CAAC9D,OAAoB;EACnD,IAAMtV,IAAI,GAAGsV,OAAO,CAACM,kBAAiC;EAEtD,IAAI,CAAC5V,IAAI,EAAE;IACT,OAAO6Y,iBAAiB,CAACQ,YAAY,CAAC/D,OAAO,CAAC,CAAC;;EAGjD,IAAI,CAACiV,cAAc,CAACvqB,IAAI,CAAC,EAAE;IACzB,OAAOoZ,gBAAgB,CAACpZ,IAAI,CAAC;;EAG/B,OAAOA,IAAI;AACb;AAEA,SAAgBuZ,gBAAgBA,CAACjE,OAAoB;EACnD,IAAMvV,IAAI,GAAGuV,OAAO,CAACI,sBAAqC;EAE1D,IAAI,CAAC3V,IAAI,EAAE;IACT,OAAOmZ,gBAAgB,CAACM,YAAY,CAAClE,OAAO,CAAC,CAAC;;EAGhD,IAAI,CAACiV,cAAc,CAACxqB,IAAI,CAAC,EAAE;IACzB,OAAOwZ,gBAAgB,CAACxZ,IAAI,CAAC;;EAG/B,OAAOA,IAAI;AACb;AAEA,SAAgB8Y,iBAAiBA,CAACD,MAAuB;EACvD,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,IAAI;;EAGb,IAAMnT,SAAS,GAAG+U,gBAAgB,CAAC5B,MAAM,CAAC;EAE1C,OAAOoO,8BAA8B,CAACpO,MAAM,EAAEnT,SAAS,EAAE,GAAG,CAAC;AAC/D;AAEA,SAAgB+T,YAAYA,CAAClE,OAAwB;EACnD,IAAM3S,QAAQ,GAAG+V,eAAe,CAACpD,OAAO,CAAC;EAEzC,IAAI,CAAC3S,QAAQ,EAAE;IACb,OAAO,IAAI;;EAGb,IAAM5C,IAAI,GAAG4C,QAAQ,CAAC+S,sBAAqC;EAE3D,IAAI,CAAC3V,IAAI,EAAE;IACT,OAAO,IAAI;;EAGb,IAAI2qB,QAAQ,CAAC3qB,IAAI,CAAC,EAAE;IAClB,OAAOyZ,YAAY,CAACzZ,IAAI,CAAC;;EAG3B,OAAOA,IAAI;AACb;AAEA,SAAgBsZ,YAAYA,CAAC/D,OAAwB;EACnD,IAAM3S,QAAQ,GAAG+V,eAAe,CAACpD,OAAO,CAAC;EAEzC,IAAI,CAAC3S,QAAQ,EAAE;IACb,OAAO,IAAI;;EAGb,IAAM3C,IAAI,GAAG2C,QAAQ,CAACiT,kBAAiC;EAEvD,IAAI,CAAC5V,IAAI,EAAE;IACT,OAAO,IAAI;;EAGb,IAAI0qB,QAAQ,CAAC1qB,IAAI,CAAC,EAAE;IAClB,OAAOqZ,YAAY,CAACrZ,IAAI,CAAC;;EAG3B,OAAOA,IAAI;AACb;AAEA,SAAgB0Y,eAAeA,CAACpD,OAAwB;EACtD,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;;EAEb,OAAOA,OAAO,CAAC8C,OAAO,CAAC9a,WAAW,CAACD,UAAU,CAACsF,QAAQ,CAAC,CAAgB;AACzE;AAEA,SAAgBoX,sBAAsBA,CAACzE,OAAwB;EAC7D,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;;EAEb,OAAOA,OAAO,CAAC8C,OAAO,CACpB9a,WAAW,CAACD,UAAU,CAACyc,eAAe,CAAC,CACzB;AAClB;;SCnUgB+Q,gBAAgBA,CAAChkB,OAAe;EAC9C,OAAOA,OAAO,CACXX,KAAK,CAAC,GAAG,CAAC,CACVtI,GAAG,CAAC,UAAAktB,GAAG;IAAA,OAAIC,MAAM,CAACC,aAAa,CAACC,QAAQ,CAACH,GAAG,EAAE,EAAE,CAAC,CAAC;IAAC,CACnDhtB,IAAI,CAAC,EAAE,CAAC;AACb;;ACAA,IAAMotB,gBAAgB,GAAG,eAAe;AAUxC,SAAgBC,YAAYA,CAACC,IAAqB;EAChD,IAAI;IAAA,IAAAlf,OAAA,EAAAmf,qBAAA,EAAAC,QAAA;IACF,IAAI,GAAApf,OAAA,GAACC,MAAM,aAAND,OAAA,CAAQqf,YAAY,GAAE;MACzB,OAAO,EAAE;;IAEX,IAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,EAAAL,qBAAA,IAAAC,QAAA,GACvBnf,MAAM,qBAANmf,QAAA,CAAQC,YAAY,CAACI,OAAO,CAACT,gBAAgB,CAAC,YAAAG,qBAAA,GAAI,IAAI,CAC1C;IAEd,IAAID,IAAI,KAAK5pB,cAAc,CAAC0I,QAAQ,EAAE;MACpC,OAAOshB,MAAM,CAACzM,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKA,CAAC,CAAC2M,KAAK,GAAG5M,CAAC,CAAC4M,KAAK;QAAC;;IAGjD,OAAOJ,MAAM;GACd,CAAC,OAAAK,OAAA,EAAM;IACN,OAAO,EAAE;;AAEb;AAEA,SAAgBC,YAAYA,CAACnmB,KAAgB,EAAEP,QAAmB;EAChE,IAAMomB,MAAM,GAAGL,YAAY,EAAE;EAE7B,IAAMtkB,OAAO,GAAGP,YAAY,CAACX,KAAK,EAAEP,QAAQ,CAAC;EAC7C,IAAMojB,eAAe,GAAGliB,YAAY,CAACX,KAAK,CAAC;EAE3C,IAAIomB,QAAQ,GAAGP,MAAM,CAAC7jB,IAAI,CAAC,UAAA1C,IAAA;IAAA,IAAY+mB,CAAC,GAAA/mB,IAAA,CAAV4B,OAAO;IAAA,OAAUmlB,CAAC,KAAKnlB,OAAO;IAAC;EAE7D,IAAIolB,QAAyB;EAE7B,IAAIF,QAAQ,EAAE;IACZE,QAAQ,GAAG,CAACF,QAAQ,CAAC,CAACjZ,MAAM,CAAC0Y,MAAM,CAAC7N,MAAM,CAAC,UAAAuO,CAAC;MAAA,OAAIA,CAAC,KAAKH,QAAQ;MAAC,CAAC;GACjE,MAAM;IACLA,QAAQ,GAAG;MACTllB,OAAO,EAAPA,OAAO;MACPslB,QAAQ,EAAE3D,eAAe;MACzBoD,KAAK,EAAE;KACR;IACDK,QAAQ,IAAIF,QAAQ,EAAAjZ,MAAA,CAAK0Y,MAAM,CAAC;;EAGlCO,QAAQ,CAACH,KAAK,EAAE;EAEhBK,QAAQ,CAAC3qB,MAAM,GAAGikB,IAAI,CAAC6G,GAAG,CAACH,QAAQ,CAAC3qB,MAAM,EAAE,EAAE,CAAC;EAE/C,IAAI;IAAA,IAAA+qB,QAAA;IACF,CAAAA,QAAA,GAAAlgB,MAAM,qBAANkgB,QAAA,CAAQd,YAAY,CAACe,OAAO,CAACpB,gBAAgB,EAAEO,IAAI,CAACc,SAAS,CAACN,QAAQ,CAAC,CAAC;;GAEzE,CAAC,OAAAO,QAAA,EAAM;;;AAGV;;SCzDgBC,gBAAgBA,CAC9B9pB,QAA+C;EAE/C,OAAOA,QAAQ,CAACA,QAAQ,KAAKf,UAAU,CAACI,MAAM;AAChD;AAEA,SAAgB0qB,aAAaA,CAAC/mB,KAAyB;EACrD,OAAOA,KAAK,CAAC6C,MAAM,KAAKoG,SAAS;AACnC;;SCoBgB+d,oBAAoBA,CAClCC,YAAqD,EACrDta,gBAAoC;EAEpC,IAAMua,iBAAiB,GAAGjhB,MAAM,EAAsB;EACtD,IAAMoP,kBAAkB,GAAGqE,qBAAqB,EAAE;EAClD,IAAMzR,gBAAgB,GAAGmB,mBAAmB,EAAE;EAC9C,IAAAgM,qBAAA,GAAoClL,4BAA4B,EAAE;IAAzD0P,uBAAuB,GAAAxE,qBAAA;EAChC,IAAMK,mBAAmB,GAAGN,sBAAsB,EAAE;EACpD,IAAAgS,qBAAA,GAAyBvd,sBAAsB,EAAE;IAA1CnB,cAAc,GAAA0e,qBAAA;EACrB,IAAMjc,YAAY,GAAGwB,qBAAqB,CAACC,gBAAgB,CAAC;EAC5D,IAAAya,mBAAA,GAA4B9c,kBAAkB,EAAE;IAAvCI,eAAe,GAAA0c,mBAAA;EACxB,IAAMhjB,WAAW,GAAG4K,oBAAoB,EAAE;EAC1C,IAAMqY,gBAAgB,GAAGrb,mBAAmB,EAAE;EAE9C,IAAMsb,OAAO,GAAGtuB,WAAiB,CAC/B,SAASsuB,OAAOA,CAAC5M,KAAiB;IAChC,IAAIzS,gBAAgB,CAAC5B,OAAO,EAAE;MAC5B;;IAGFoP,mBAAmB,EAAE;IAErB,IAAA8R,eAAA,GAAyBC,cAAc,CAAC9M,KAAK,CAAC;MAAvC1a,KAAK,GAAAunB,eAAA;MAAErmB,OAAO,GAAAqmB,eAAA;IAErB,IAAI,CAACvnB,KAAK,IAAI,CAACkB,OAAO,EAAE;MACtB;;IAGF,IAAMumB,aAAa,GACjB1kB,0BAA0B,CAAC7B,OAAO,CAAC,IAAIuH,cAAc;IAEvDiC,eAAe,EAAE;IACjByb,YAAY,CAACnmB,KAAK,EAAEynB,aAAa,CAAC;IAClCvc,YAAY,CACVwc,gBAAgB,CAAC1nB,KAAK,EAAEynB,aAAa,EAAEJ,gBAAgB,EAAEjjB,WAAW,CAAC,EACrEsW,KAAK,CACN;GACF,EACD,CACEjS,cAAc,EACdgN,mBAAmB,EACnBxN,gBAAgB,EAChBiD,YAAY,EACZR,eAAe,EACftG,WAAW,EACXijB,gBAAgB,CACjB,CACF;EAED,IAAMM,WAAW,GAAG3uB,WAAiB,CACnC,SAAS2uB,WAAWA,CAACjN,KAAiB;;IACpC,IAAIwM,iBAAiB,CAAC7gB,OAAO,EAAE;MAC7BC,YAAY,CAAC4gB,iBAAiB,CAAC7gB,OAAO,CAAC;;IAGzC,IAAAuhB,gBAAA,GAAgBJ,cAAc,CAAC9M,KAAK,CAAC;MAA9B1a,KAAK,GAAA4nB,gBAAA;IAEZ,IAAI,CAAC5nB,KAAK,IAAI,CAACsB,kBAAkB,CAACtB,KAAK,CAAC,EAAE;MACxC;;IAGFknB,iBAAiB,CAAC7gB,OAAO,IAAAE,OAAA,GAAGC,MAAM,qBAAND,OAAA,CAAQ1G,UAAU,CAAC;MAC7CoI,gBAAgB,CAAC5B,OAAO,GAAG,IAAI;MAC/B6gB,iBAAiB,CAAC7gB,OAAO,GAAG4C,SAAS;MACrCwM,mBAAmB,EAAE;MACrBJ,kBAAkB,CAACqF,KAAK,CAACpJ,MAAqB,CAAC;MAC/CsI,uBAAuB,CAAC5Z,KAAK,CAAC;KAC/B,EAAE,GAAG,CAAC;GACR,EACD,CACEiI,gBAAgB,EAChBwN,mBAAmB,EACnBJ,kBAAkB,EAClBuE,uBAAuB,CACxB,CACF;EACD,IAAMiO,SAAS,GAAG7uB,WAAiB,CACjC,SAAS6uB,SAASA;IAChB,IAAIX,iBAAiB,CAAC7gB,OAAO,EAAE;MAC7BC,YAAY,CAAC4gB,iBAAiB,CAAC7gB,OAAO,CAAC;MACvC6gB,iBAAiB,CAAC7gB,OAAO,GAAG4C,SAAS;KACtC,MAAM,IAAIhB,gBAAgB,CAAC5B,OAAO,EAAE;;;;;;MAOnCuJ,qBAAqB,CAAC;QACpB3H,gBAAgB,CAAC5B,OAAO,GAAG,KAAK;OACjC,CAAC;;GAEL,EACD,CAAC4B,gBAAgB,CAAC,CACnB;EAEDL,SAAS,CAAC;IACR,IAAI,CAACqf,YAAY,CAAC5gB,OAAO,EAAE;MACzB;;IAEF,IAAMyhB,YAAY,GAAGb,YAAY,CAAC5gB,OAAO;IACzCyhB,YAAY,CAACvR,gBAAgB,CAAC,OAAO,EAAE+Q,OAAO,EAAE;MAC9C7Q,OAAO,EAAE;KACV,CAAC;IAEFqR,YAAY,CAACvR,gBAAgB,CAAC,WAAW,EAAEoR,WAAW,EAAE;MACtDlR,OAAO,EAAE;KACV,CAAC;IACFqR,YAAY,CAACvR,gBAAgB,CAAC,SAAS,EAAEsR,SAAS,EAAE;MAClDpR,OAAO,EAAE;KACV,CAAC;IAEF,OAAO;MACLqR,YAAY,oBAAZA,YAAY,CAAEpR,mBAAmB,CAAC,OAAO,EAAE4Q,OAAO,CAAC;MACnDQ,YAAY,oBAAZA,YAAY,CAAEpR,mBAAmB,CAAC,WAAW,EAAEiR,WAAW,CAAC;MAC3DG,YAAY,oBAAZA,YAAY,CAAEpR,mBAAmB,CAAC,SAAS,EAAEmR,SAAS,CAAC;KACxD;GACF,EAAE,CAACZ,YAAY,EAAEK,OAAO,EAAEK,WAAW,EAAEE,SAAS,CAAC,CAAC;AACrD;AAEA,SAASL,cAAcA,CAAC9M,KAAiB;EACvC,IAAMpJ,MAAM,GAAGoJ,KAAK,oBAALA,KAAK,CAAEpJ,MAAqB;EAC3C,IAAI,CAAC0R,cAAc,CAAC1R,MAAM,CAAC,EAAE;IAC3B,OAAO,EAAE;;EAGX,OAAOwI,gBAAgB,CAACxI,MAAM,CAAC;AACjC;AAEA,SAASoW,gBAAgBA,CACvB1nB,KAAgB,EAChByI,cAAyB,EACzB4e,gBAA4B,EAC5BjjB,WAAwB;EAExB,IAAMzB,KAAK,GAAGxC,UAAU,CAACH,KAAK,CAAC;EAE/B,IAAI+mB,aAAa,CAAC/mB,KAAK,CAAC,EAAE;IACxB,IAAMkB,QAAO,GAAGP,YAAY,CAACX,KAAK,CAAC;IACnC,OAAO;MACLyI,cAAc,EAAdA,cAAc;MACdzI,KAAK,EAAEkB,QAAO;MACd6mB,WAAW,WAAAA;QACT,OAAO/nB,KAAK,CAAC6C,MAAM;OACpB;MACDmlB,QAAQ,EAAEhoB,KAAK,CAAC6C,MAAM;MACtBolB,QAAQ,EAAE,IAAI;MACdtlB,KAAK,EAALA,KAAK;MACLzB,OAAO,EAAPA,QAAO;MACPD,sBAAsB,EAAEC;KACzB;;EAEH,IAAMA,OAAO,GAAGP,YAAY,CAACX,KAAK,EAAEyI,cAAc,CAAC;EAEnD,OAAO;IACLA,cAAc,EAAdA,cAAc;IACdzI,KAAK,EAAEklB,gBAAgB,CAAChkB,OAAO,CAAC;IAChC6mB,WAAW,WAAAA,YAAC5sB;UAAAA;QAAAA,aAAyBksB,gBAAgB,WAAhBA,gBAAgB,GAAIvrB,UAAU,CAAC4C,KAAK;;MACvE,OAAO0F,WAAW,CAAClD,OAAO,EAAE/F,UAAU,CAAC;KACxC;IACD6sB,QAAQ,EAAE5jB,WAAW,CAAClD,OAAO,EAAEmmB,gBAAgB,WAAhBA,gBAAgB,GAAIvrB,UAAU,CAAC4C,KAAK,CAAC;IACpEupB,QAAQ,EAAE,KAAK;IACftlB,KAAK,EAALA,KAAK;IACLzB,OAAO,EAAPA,OAAO;IACPD,sBAAsB,EAAEN,YAAY,CAACX,KAAK;GAC3C;AACH;;SC9LgBkoB,MAAMA,CAACC,KAAY;EACjC,OACEnvB;IACEovB,IAAI,EAAC;KACDD,KAAK;IACT5sB,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACV,MAAM,EAAE2K,KAAK,CAAC5sB,SAAS;MAE3C4sB,KAAK,CAACnjB,QAAQ,CACR;AAEb;AAEA,IAAMkZ,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/B2kB,MAAM,EAAE;IACN,GAAG,EAAE,SAAS;IACd6K,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAE;;CAEZ,CAAC;;SCVcC,oBAAoBA,CAAAnpB,IAAA;;MAClCa,UAAU,GAAAb,IAAA,CAAVa,UAAU;IACVe,OAAO,GAAA5B,IAAA,CAAP4B,OAAO;IACP5I,MAAM,GAAAgH,IAAA,CAANhH,MAAM;IACNkB,cAAc,GAAA8F,IAAA,CAAd9F,cAAc;IAAAkvB,mBAAA,GAAAppB,IAAA,CACdqpB,cAAc;IAAdA,cAAc,GAAAD,mBAAA,cAAG,IAAI,GAAAA,mBAAA;IACrBE,aAAa,GAAAtpB,IAAA,CAAbspB,aAAa;IACb5jB,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;IACRzJ,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;IAAAstB,iBAAA,GAAAvpB,IAAA,CACTwpB,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,KAAK,GAAAA,iBAAA;EAEpB,OACE7vB,cAACkvB,MAAM;IACL3sB,SAAS,EAAE0iB,EAAE,CACXC,QAAM,CAACle,KAAK,EACZ1H,MAAM,IAAIM,YAAY,CAACN,MAAM,EAC7BkB,cAAc,IAAIH,uBAAuB,CAACG,cAAc,GAAAglB,GAAA,OAAAA,GAAA,CAErD9mB,UAAU,CAACgrB,OAAO,IAAG,CAACpqB,MAAM,IAAI,CAACkB,cAAc,EAAAglB,GAAA,GAElD,CAAC,EAAEoK,aAAa,IAAID,cAAc,CAAC,IAAIzK,QAAM,CAAC0K,aAAa,EAC3DE,YAAY,IAAI5K,QAAM,CAAC4K,YAAY,EACnCvtB,SAAS,CACV;oBACa2F,OAAO;kBACT6nB,YAAY,CAAC5oB,UAAU,CAAC;sBACpBA;KAEf6E,QAAQ,CACF;AAEb;AAEA,SAAS+jB,YAAYA,CAAC5oB,UAAoB;;EACxC,OAAOA,UAAU,CAAC,CAAC,CAAC,CAACsc,KAAK,CAAC,OAAO,CAAC,IAAAuM,YAAA,GAC/B7oB,UAAU,CAAC,CAAC,CAAC,YAAA6oB,YAAA,GAAI7oB,UAAU,CAAC,CAAC,CAAC,GAC9BA,UAAU,CAAC,CAAC,CAAC;AACnB;AAEA,IAAM+d,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/BmH,KAAK,EAAE;IACL,GAAG,EAAEtI,UAAU,CAACsI,KAAK;IACrB8e,QAAQ,EAAE,UAAU;IACpBjlB,KAAK,EAAE,2BAA2B;IAClCD,MAAM,EAAE,2BAA2B;IACnCylB,SAAS,EAAE,YAAY;IACvB9mB,OAAO,EAAE,MAAM;IACf0wB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,QAAQ,EAAE,2BAA2B;IACrCC,SAAS,EAAE,2BAA2B;IACtClK,YAAY,EAAE,KAAK;IACnBvmB,QAAQ,EAAE,QAAQ;IAClBgB,UAAU,EAAE,uBAAuB;IACnC,QAAQ,EAAE;MACRylB,eAAe,EAAE;KAClB;IACD,QAAQ,EAAE;MACRA,eAAe,EAAE;;GAEpB;EACD0J,YAAY,EAAE;IACZP,UAAU,EAAE,MAAM;IAClB,QAAQ,EAAE;MACRnJ,eAAe,EAAE,aAAa;MAC9BmJ,UAAU,EAAE;KACb;IACD,QAAQ,EAAE;MACRnJ,eAAe,EAAE,aAAa;MAC9BmJ,UAAU,EAAE;;GAEf;EACDK,aAAa,EAAE;IACb,GAAG,EAAElxB,UAAU,CAAC4J,kBAAkB;IAClC,QAAQ,EAAE;MACR+nB,OAAO,EAAE,EAAE;MACX9wB,OAAO,EAAE,OAAO;MAChBsB,KAAK,EAAE,GAAG;MACVD,MAAM,EAAE,GAAG;MACX0vB,KAAK,EAAE,KAAK;MACZ9H,MAAM,EAAE,KAAK;MACb1C,QAAQ,EAAE,UAAU;MACpByK,UAAU,EAAE,uBAAuB;MACnCC,WAAW,EAAE,uBAAuB;MACpCC,SAAS,EAAE,gBAAgB;MAC3BC,YAAY,EAAE,sDAAsD;MACpEC,MAAM,EAAE;KACT;IACD,cAAc,EAAE;MACdD,YAAY,EAAE;;;CAGnB,CAAC;;AChHK,IAAME,WAAW,gBAAGxxB,UAAU,CAACS,MAAM,CAAC;EAC3CgxB,QAAQ,EAAE;IACR,GAAG,EAAEnyB,UAAU,CAACmyB,QAAQ;IACxBC,QAAQ,EAAE;GACX;EACDC,MAAM,EAAE;IACNC,SAAS,EAAE,QAAQ;IACnBC,WAAW,EAAE,QAAQ;IACrB1xB,OAAO,EAAE;;CAEZ,CAAC;;SCLc2xB,QAAQA,CAAA5qB,IAAA;MACtB0B,SAAS,GAAA1B,IAAA,CAAT0B,SAAS;IACTxF,KAAK,GAAA8D,IAAA,CAAL9D,KAAK;IAAA2uB,aAAA,GAAA7qB,IAAA,CACL8qB,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAChBtnB,MAAM,GAAAvD,IAAA,CAANuD,MAAM;IACNwnB,OAAO,GAAA/qB,IAAA,CAAP+qB,OAAO;IACP9uB,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;EAUT,OACEvC;IACEqkB,GAAG,EAAExa,MAAM;IACXynB,GAAG,EAAEtpB,SAAS;IACdzF,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACqM,SAAS,EAAEX,WAAW,CAACC,QAAQ,EAAED,WAAW,CAACG,MAAM,EAAExuB,SAAS,CAAC;IACpFivB,OAAO,EAAEJ,QAAQ,GAAG,MAAM,GAAG,OAAO;IACpCC,OAAO,EAAEA,OAAO;IAChB7uB,KAAK,EAAEA;IACP;AAEN;AAEA,IAAM0iB,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/B0xB,SAAS,EAAE;IACT,GAAG,EAAE,eAAe;IACpBpB,QAAQ,EAAE,2BAA2B;IACrCC,SAAS,EAAE,2BAA2B;IACtCqB,QAAQ,EAAE,2BAA2B;IACrCC,SAAS,EAAE,2BAA2B;IACtCC,OAAO,EAAE;;CAEZ,CAAC;;SCrCcC,WAAWA,CAAAtrB,IAAA;MACzB4B,OAAO,GAAA5B,IAAA,CAAP4B,OAAO;IACP1F,KAAK,GAAA8D,IAAA,CAAL9D,KAAK;IACLD,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;EAMT,OACEvC;IACEuC,SAAS,EAAE0iB,EAAE,CACXC,QAAM,CAAC2M,WAAW,EAClBjB,WAAW,CAACG,MAAM,EAClBH,WAAW,CAACC,QAAQ,EACpBtuB,SAAS,CACV;oBACa2F,OAAO;IACrB1F,KAAK,EAAEA;KAEN0pB,gBAAgB,CAAChkB,OAAO,CAAC,CACrB;AAEX;AAEA,IAAMgd,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/BgyB,WAAW,EAAE;IACX,GAAG,EAAE,kBAAkB;IACvBvL,UAAU,EACR,0JAA0J;IAC5JR,QAAQ,EAAE,UAAU;IACpBgM,UAAU,EAAE,MAAM;IAClBhB,QAAQ,EAAE,uBAAuB;IACjCiB,SAAS,EAAE,QAAQ;IACnBf,SAAS,EAAE,QAAQ;IACnBC,WAAW,EAAE,QAAQ;IACrBe,aAAa,EAAE,GAAG;IAClBL,OAAO,EAAE;;CAEZ,CAAC;;SChCcM,aAAaA,CAAA3rB,IAAA;MAC3BU,KAAK,GAAAV,IAAA,CAALU,KAAK;IACLkB,OAAO,GAAA5B,IAAA,CAAP4B,OAAO;IACP/F,UAAU,GAAAmE,IAAA,CAAVnE,UAAU;IACV+vB,IAAI,GAAA5rB,IAAA,CAAJ4rB,IAAI;IACJd,QAAQ,GAAA9qB,IAAA,CAAR8qB,QAAQ;IAAAe,gBAAA,GAAA7rB,IAAA,CACR8E,WAAW;IAAXA,WAAW,GAAA+mB,gBAAA,cAAGvpB,iBAAiB,GAAAupB,gBAAA;IAC/B5vB,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;EAET,IAAA6vB,qBAAA,GAAsCthB,8BAA8B,EAAE;IAA7DuhB,yBAAyB,GAAAD,qBAAA;EAElC,IAAM5vB,KAAK,GAAG,EAAyB;EACvC,IAAI0vB,IAAI,EAAE;IACR1vB,KAAK,CAAC3B,KAAK,GAAG2B,KAAK,CAAC5B,MAAM,GAAG4B,KAAK,CAACsuB,QAAQ,GAAMoB,IAAI,OAAI;;EAG3D,IAAMI,aAAa,GAAGtrB,KAAK,GAAGA,KAAK,GAAGmC,cAAc,CAACjB,OAAO,CAAC;EAE7D,IAAI,CAACoqB,aAAa,EAAE;IAClB,OAAO,IAAI;;EAGb,IAAIvE,aAAa,CAACuE,aAAa,CAAC,EAAE;IAChC,OACEtyB,cAACkxB,QAAQ;MACP1uB,KAAK,EAAEA,KAAK;MACZwF,SAAS,EAAEE,OAAO;MAClB/F,UAAU,EAAEW,UAAU,CAACghB,MAAM;MAC7BsN,QAAQ,EAAEA,QAAQ;MAClBvnB,MAAM,EAAEyoB,aAAa,CAACzoB,MAAM;MAC5BwnB,OAAO,EAAEA,OAAO;MAChB9uB,SAAS,EAAEA;MACX;;EAIN,OACEvC,8BACGmC,UAAU,KAAKW,UAAU,CAACghB,MAAM,GAC/B9jB,cAAC4xB,WAAW;IAAC1pB,OAAO,EAAEA,OAAO;IAAE1F,KAAK,EAAEA,KAAK;IAAED,SAAS,EAAEA;IAAa,GAErEvC,cAACkxB,QAAQ;IACP1uB,KAAK,EAAEA,KAAK;IACZwF,SAAS,EAAEA,SAAS,CAACsqB,aAAa,CAAC;IACnCnwB,UAAU,EAAEA,UAAU;IACtBivB,QAAQ,EAAEA,QAAQ;IAClBvnB,MAAM,EAAEuB,WAAW,CAAClD,OAAO,EAAE/F,UAAU,CAAC;IACxCkvB,OAAO,EAAEA,OAAO;IAChB9uB,SAAS,EAAEA;IAEd,CACA;EAGL,SAAS8uB,OAAOA;IACdgB,yBAAyB,CAAC,UAAAjxB,IAAI;MAAA,OAAI,IAAIoK,GAAG,CAACpK,IAAI,CAAC,CAAC6J,GAAG,CAAC/C,OAAO,CAAC;MAAC;;AAEjE;;SCpDgBqqB,cAAcA,CAAAjsB,IAAA;MAC5BU,KAAK,GAAAV,IAAA,CAALU,KAAK;IACLkB,OAAO,GAAA5B,IAAA,CAAP4B,OAAO;IACP5I,MAAM,GAAAgH,IAAA,CAANhH,MAAM;IACNkB,cAAc,GAAA8F,IAAA,CAAd9F,cAAc;IACd2B,UAAU,GAAAmE,IAAA,CAAVnE,UAAU;IAAAutB,mBAAA,GAAAppB,IAAA,CACVqpB,cAAc;IAAdA,cAAc,GAAAD,mBAAA,cAAG,IAAI,GAAAA,mBAAA;IACrBwC,IAAI,GAAA5rB,IAAA,CAAJ4rB,IAAI;IACJd,QAAQ,GAAA9qB,IAAA,CAAR8qB,QAAQ;IACRhmB,WAAW,GAAA9E,IAAA,CAAX8E,WAAW;IACX7I,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;IAAAstB,iBAAA,GAAAvpB,IAAA,CACTwpB,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,KAAK,GAAAA,iBAAA;EAEpB,IAAMD,aAAa,GAAGtnB,kBAAkB,CAACtB,KAAK,CAAC;EAE/C,OACEhH,cAACyvB,oBAAoB;IACnBG,aAAa,EAAEA,aAAa;IAC5BD,cAAc,EAAEA,cAAc;IAC9BrwB,MAAM,EAAEA,MAAM;IACdkB,cAAc,EAAEA,cAAc;IAC9B2G,UAAU,EAAEA,UAAU,CAACH,KAAK,CAAC;IAC7BkB,OAAO,EAAEA,OAAO;IAChB4nB,YAAY,EAAEA;KAEd9vB,cAACiyB,aAAa;IACZ/pB,OAAO,EAAEA,OAAO;IAChBlB,KAAK,EAAEA,KAAK;IACZkrB,IAAI,EAAEA,IAAI;IACV/vB,UAAU,EAAEA,UAAU;IACtBivB,QAAQ,EAAEA,QAAQ;IAClBhmB,WAAW,EAAEA,WAAW;IACxB7I,SAAS,EAAEA;IACX,CACmB;AAE3B;;;;SC/CgBiwB,OAAOA;EACrB,IAAA3e,qBAAA,GAA6BrD,qBAAqB,EAAE;IAA3CiiB,gBAAgB,GAAA5e,qBAAA;EACzB,OACE7T,cAACkvB,MAAM;kBACM,iBAAiB;IAC5BwD,KAAK,EAAC,iBAAiB;IACvBC,QAAQ,EAAE,CAAC;IACXpwB,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAAC0N,QAAQ,CAAC;IAC9BtE,OAAO,EAAE,SAAAA;MAAA,OAAMmE,gBAAgB,CAAC,KAAK,CAAC;;IACtC;AAEN;AAEA,IAAMvN,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,eAAAC,QAAA;EAC9B8yB,QAAQ,EAAE;IACR9B,QAAQ,EAAE,MAAM;IAChBa,OAAO,EAAE,MAAM;IACfkB,KAAK,EAAE,uBAAuB;IAC9B3M,YAAY,EAAE,KAAK;IACnB6L,SAAS,EAAE,QAAQ;IACnBD,UAAU,EAAE,MAAM;IAClBjxB,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdrB,OAAO,EAAE,MAAM;IACf2wB,cAAc,EAAE,QAAQ;IACxBD,UAAU,EAAE,QAAQ;IACpBtvB,UAAU,EAAE,mCAAmC;IAC/C,QAAQ,EAAE;MACR0vB,OAAO,EAAE,EAAE;MACXoB,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE,MAAM;MACjBoB,eAAe,WAASC,IAAI,MAAG;MAC/B3M,eAAe,EAAE,aAAa;MAC9B4M,gBAAgB,EAAE,WAAW;MAC7BC,cAAc,EAAE,MAAM;MACtB1yB,mBAAmB,EAAE;KACtB;IACD,QAAQ,EAAE;MACRsyB,KAAK,EAAE,4BAA4B;MACnCzM,eAAe,EAAE,2CAA2C;MAC5D,QAAQ,EAAE;QACR7lB,mBAAmB,EAAE;;KAExB;IACD,QAAQ,EAAE;MACRsyB,KAAK,EAAE,4BAA4B;MACnCzM,eAAe,EAAE,2CAA2C;MAC5D,QAAQ,EAAE;QACR7lB,mBAAmB,EAAE;;;;AAG1B,gBACEO,QAAQ,CAAC,UAAU,EAAE;EACtB,QAAQ,EAAE;IAAEP,mBAAmB,EAAE;GAAS;EAC1C,cAAc,EAAE;IAAEA,mBAAmB,EAAE;;CACxC,CAAC,CACH,CAAC;;SC7Cc2yB,SAASA;EACvB,IAAArf,qBAAA,GAAwBrD,qBAAqB,EAAE;IAAxC2iB,aAAa,GAAAtf,qBAAA;EACpB,IAAMmE,YAAY,GAAGQ,eAAe,EAAE;EACtC,IAAM/M,SAAS,GAAGqK,kBAAkB,EAAE;EACtCkY,oBAAoB,CAAChW,YAAY,EAAE1F,kBAAkB,CAAC0B,SAAS,CAAC;EAChE,IAAM7R,UAAU,GAAG6Q,mBAAmB,EAAE;EACxC,IAAMtH,oBAAoB,GAAGkH,uBAAuB,EAAE;EACtD,IAAMxH,WAAW,GAAG4K,oBAAoB,EAAE;EAE1C,IAAI,CAACmd,aAAa,EAAE;IAClB,OAAO,IAAI;;EAGb,OACEnzB;IACEuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACkO,IAAI,EAAE,CAACD,aAAa,IAAIvzB,YAAY,CAACN,MAAM,CAAC;IACjEqmB,GAAG,EAAE3N;KAEJvM,SAAS,CAACxM,GAAG,CAAC,UAAAo0B,QAAQ;IAAA,OACrBrzB;MAAIe,GAAG,EAAEsyB;OACPrzB,cAACuyB,cAAc;MACbvrB,KAAK,EAAEmC,cAAc,CAACkqB,QAAQ,CAAc;MAC5ClxB,UAAU,EAAEA,UAAU;MACtB+F,OAAO,EAAEmrB,QAAQ;MACjB1D,cAAc,EAAE,KAAK;MACrBptB,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACoO,WAAW,CAAC;MACjCxD,YAAY;MACZ1kB,WAAW,EAAEA;MACb,CACC;GACN,CAAC,EACDM,oBAAoB,GACnB1L,0BACEA,cAACwyB,OAAO,OAAG,CACR,GACH,IAAI,CACL;AAET;AAEA,IAAMtN,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/BuzB,IAAI,EAAE;IACJG,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,GAAG;IACX7B,OAAO,EAAE,OAAO;IAChBpyB,OAAO,EAAE,MAAM;IACf2wB,cAAc,EAAE,eAAe;IAC/BD,UAAU,EAAE,QAAQ;IACpBrvB,MAAM,EAAE;GACT;EACD0yB,WAAW,EAAE;IACX,QAAQ,EAAE;MACR7C,SAAS,EAAE;KACZ;IACD,QAAQ,EAAE;MACRA,SAAS,EAAE;KACZ;IACD,SAAS,EAAE;MACTA,SAAS,EAAE;KACZ;IACD9vB,UAAU,EAAE;;CAEf,CAAC;;SC5Ec8yB,WAAWA,CAAC9b,OAAmB;EAC7C,IAAM8E,mBAAmB,GAAGN,sBAAsB,EAAE;EAEpDvN,SAAS,CAAC;IACR,IAAM0O,OAAO,GAAG3F,OAAO,CAACtK,OAAO;IAC/B,IAAI,CAACiQ,OAAO,EAAE;MACZ;;IAGFA,OAAO,CAACC,gBAAgB,CAAC,QAAQ,EAAEmW,QAAQ,EAAE;MAC3CjW,OAAO,EAAE;KACV,CAAC;IAEF,SAASiW,QAAQA;MACfjX,mBAAmB,EAAE;;IAGvB,OAAO;MACLa,OAAO,oBAAPA,OAAO,CAAEI,mBAAmB,CAAC,QAAQ,EAAEgW,QAAQ,CAAC;KACjD;GACF,EAAE,CAAC/b,OAAO,EAAE8E,mBAAmB,CAAC,CAAC;AACpC;;SCrBgBkX,gBAAgBA;EAC9B,IAAAvB,qBAAA,GAAiCthB,8BAA8B,EAAE;IAA1D8iB,sBAAsB,GAAAxB,qBAAA;EAC7B,IAAMyB,eAAe,GAAGjU,kBAAkB,EAAE;EAE5C,OAAO,UAAC5Y,KAAgB;IACtB,IAAMkB,OAAO,GAAGP,YAAY,CAACX,KAAK,CAAC;IAEnC,IAAM8sB,YAAY,GAAGF,sBAAsB,CAACjmB,GAAG,CAACzF,OAAO,CAAC;IACxD,IAAM6rB,WAAW,GAAGF,eAAe,CAAC3rB,OAAO,CAAC;IAE5C,OAAO;MACL4rB,YAAY,EAAZA,YAAY;MACZC,WAAW,EAAXA,WAAW;MACXz0B,MAAM,EAAEw0B,YAAY,IAAIC;KACzB;GACF;AACH;;SCAgBC,aAAaA,CAAA1tB,IAAA;MAC3B2tB,cAAc,GAAA3tB,IAAA,CAAd2tB,cAAc;IACdjoB,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;IACR1M,MAAM,GAAAgH,IAAA,CAANhH,MAAM;IACNkB,cAAc,GAAA8F,IAAA,CAAd9F,cAAc;EAEd,IAAMwD,QAAQ,GAAGK,0BAA0B,CAAC4vB,cAAc,CAAC;EAC3D,IAAMC,YAAY,GAAG5vB,8BAA8B,CAAC2vB,cAAc,CAAC;EAEnE,OACEj0B;IACEuC,SAAS,EAAE0iB,EAAE,CACXC,QAAM,CAAClhB,QAAQ,EACf1E,MAAM,IAAIM,YAAY,CAACN,MAAM,EAC7BkB,cAAc,IAAIH,uBAAuB,CAACG,cAAc,CACzD;iBACUwD,QAAQ;kBACPkwB;KAEZl0B;IAAIuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACmE,KAAK;KAAI6K,YAAY,CAAM,EACpDl0B;IAAKuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAAC/J,eAAe;KAAInP,QAAQ,CAAO,CACzD;AAET;AAEA,IAAMkZ,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/BmE,QAAQ,EAAE;IACR,GAAG,EAAEtF,UAAU,CAACsF,QAAQ;IACxB,0BAA0B,EAAE;MAC1BzE,OAAO,EAAE;;GAEZ;EACD4b,eAAe,EAAE;IACf,GAAG,EAAEzc,UAAU,CAACyc,eAAe;IAC/B5b,OAAO,EAAE,MAAM;IACf40B,OAAO,EAAE,GAAG;IACZC,mBAAmB,EAAE,8CAA8C;IACnElE,cAAc,EAAE,eAAe;IAC/BsD,MAAM,EAAE,6BAA6B;IACrC1N,QAAQ,EAAE;GACX;EACDuD,KAAK,EAAE;IACL,GAAG,EAAE3qB,UAAU,CAAC2qB,KAAK;IACrB4G,UAAU,EAAE,QAAQ;;IAEpBzJ,cAAc,EAAE,WAAW;IAC3BJ,eAAe,EAAE,oCAAoC;IACrDyM,KAAK,EAAE,sCAAsC;IAC7CtzB,OAAO,EAAE,MAAM;IACfuxB,QAAQ,EAAE,MAAM;IAChBuD,UAAU,EAAE,MAAM;IAClBzzB,MAAM,EAAE,kCAAkC;IAC1C4yB,MAAM,EAAE,GAAG;IACX7B,OAAO,EAAE,mCAAmC;IAC5C7L,QAAQ,EAAE,QAAQ;IAClBwO,aAAa,EAAE,YAAY;IAC3Bvb,GAAG,EAAE,GAAG;IACRlY,KAAK,EAAE,MAAM;IACb8vB,MAAM,EAAE;;CAEX,CAAC;;AChFF,IAAI4D,aAAa,GAAG,KAAK;AAEzB,SAAgBC,gBAAgBA;EAC9B,IAAAloB,eAAA,GAAkCtM,QAAc,CAACu0B,aAAa,CAAC;IAAxDE,SAAS,GAAAnoB,eAAA;IAAEooB,YAAY,GAAApoB,eAAA;EAE9BtM,SAAe,CAAC;IACd00B,YAAY,CAAC,IAAI,CAAC;IAClBH,aAAa,GAAG,IAAI;GACrB,EAAE,EAAE,CAAC;EAEN,OAAOE,SAAS,IAAIF,aAAa;AACnC;;SCQgBI,SAASA,CAAAruB,IAAA;MAAG2tB,cAAc,GAAA3tB,IAAA,CAAd2tB,cAAc;EACxC,IAAA7F,mBAAA,GAA2B9c,kBAAkB,EAAE;IAAxCE,gBAAgB,GAAA4c,mBAAA;EACvB,IAAMqG,SAAS,GAAGD,gBAAgB,EAAE;EACpC,IAAMI,yBAAyB,GAAG9f,4BAA4B,EAAE;EAChE,IAAM1J,WAAW,GAAG4K,oBAAoB,EAAE;EAC1C,IAAM6e,SAAS,GAAG70B,OAAa,CAC7B;IAAA,IAAA80B,aAAA;IAAA,QAAAA,aAAA,GAAMtI,YAAY,CAACoI,yBAAyB,CAAC,YAAAE,aAAA,GAAI,EAAE;;;EAEnD,CAACtjB,gBAAgB,EAAEojB,yBAAyB,CAAC,CAC9C;EACD,IAAMzyB,UAAU,GAAG6Q,mBAAmB,EAAE;EACxC,IAAMzE,iBAAiB,GAAGF,oBAAoB,EAAE;EAEhD,IAAI,CAAComB,SAAS,EAAE;IACd,OAAO,IAAI;;EAGb,OACEz0B,cAACg0B,aAAa;IACZC,cAAc,EAAEA,cAAc;IAC9BzzB,cAAc;IACdlB,MAAM,EAAEu1B,SAAS,CAAClyB,MAAM,KAAK;KAE5BkyB,SAAS,CAAC51B,GAAG,CAAC,UAAA81B,aAAa;IAC1B,IAAM/tB,KAAK,GAAGmC,cAAc,CAAC4rB,aAAa,CAACvH,QAAQ,CAAC;IAEpD,IAAI,CAACxmB,KAAK,EAAE;MACV,OAAO,IAAI;;IAGb,IAAIuH,iBAAiB,CAACvH,KAAK,CAAC,EAAE;MAC5B,OAAO,IAAI;;IAGb,OACEhH,cAACuyB,cAAc;MACb5C,cAAc,EAAE,KAAK;MACrBznB,OAAO,EAAE6sB,aAAa,CAAC7sB,OAAO;MAC9B/F,UAAU,EAAEA,UAAU;MACtB6E,KAAK,EAAEA,KAAK;MACZjG,GAAG,EAAEg0B,aAAa,CAAC7sB,OAAO;MAC1BkD,WAAW,EAAEA;MACb;GAEL,CAAC,CACY;AAEpB;;SCvCgB4pB,SAASA;EACvB,IAAMlqB,UAAU,GAAGsI,mBAAmB,EAAE;EACxC,IAAM6hB,yBAAyB,GAAGj1B,MAAY,CAAC,CAAC,CAAC;EAEjD,OACEA;IAAIuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACgQ,SAAS;KAC/BpqB,UAAU,CAAC7L,GAAG,CAAC,UAAAg1B,cAAc;IAC5B,IAAMjwB,QAAQ,GAAGK,0BAA0B,CAAC4vB,cAAc,CAAC;IAE3D,IAAIjwB,QAAQ,KAAKf,UAAU,CAACG,SAAS,EAAE;MACrC,OAAOpD,cAAC20B,SAAS;QAAC5zB,GAAG,EAAEiD,QAAQ;QAAEiwB,cAAc,EAAEA;QAAkB;;IAGrE,OACEj0B,cAACA,QAAc;MAACe,GAAG,EAAEiD;OACnBhE,cAACm1B,cAAc;MACbnxB,QAAQ,EAAEA,QAAQ;MAClBiwB,cAAc,EAAEA,cAAc;MAC9BgB,yBAAyB,EAAEA;MAC3B,CACa;GAEpB,CAAC,CACC;AAET;AAEA,SAASE,cAAcA,CAAA7uB,IAAA;MACrBtC,QAAQ,GAAAsC,IAAA,CAARtC,QAAQ;IACRiwB,cAAc,GAAA3tB,IAAA,CAAd2tB,cAAc;IACdgB,yBAAyB,GAAA3uB,IAAA,CAAzB2uB,yBAAyB;EAMzB,IAAMG,aAAa,GAAGzB,gBAAgB,EAAE;EACxC,IAAMrxB,cAAc,GAAG0S,uBAAuB,EAAE;EAChD,IAAM7S,UAAU,GAAG6Q,mBAAmB,EAAE;EACxC,IAAMlD,iBAAiB,GAAGkB,oBAAoB,EAAE;EAChD,IAAAmd,qBAAA,GAAyBvd,sBAAsB,EAAE;IAA1CnB,cAAc,GAAA0e,qBAAA;EACrB,IAAM5f,iBAAiB,GAAGF,oBAAoB,EAAE;EAChD,IAAMjD,WAAW,GAAG4K,oBAAoB,EAAE;EAC1C,IAAM2Z,cAAc,GAAG,CAAC7c,0BAA0B,EAAE;;;EAIpD,IAAMuiB,YAAY,GAChB,CAACvlB,iBAAiB,IAAImlB,yBAAyB,CAAC5nB,OAAO,GAAG,CAAC,GACvD,EAAE,GACF5E,gBAAgB,CAACzE,QAAQ,CAAC;EAEhC,IAAIqxB,YAAY,CAAC1yB,MAAM,GAAG,CAAC,EAAE;IAC3BsyB,yBAAyB,CAAC5nB,OAAO,EAAE;;EAGrC,IAAIioB,aAAa,GAAG,CAAC;EAErB,IAAM3sB,MAAM,GAAG0sB,YAAY,CAACp2B,GAAG,CAAC,UAAA+H,KAAK;IACnC,IAAMkB,OAAO,GAAGP,YAAY,CAACX,KAAK,EAAEyI,cAAc,CAAC;IACnD,IAAA8lB,cAAA,GAA8CH,aAAa,CAACpuB,KAAK,CAAC;MAA1D8sB,YAAY,GAAAyB,cAAA,CAAZzB,YAAY;MAAEC,WAAW,GAAAwB,cAAA,CAAXxB,WAAW;MAAEz0B,MAAM,GAAAi2B,cAAA,CAANj2B,MAAM;IAEzC,IAAMk2B,YAAY,GAAGjnB,iBAAiB,CAACvH,KAAK,CAAC;IAE7C,IAAI1H,MAAM,IAAIk2B,YAAY,EAAE;MAC1BF,aAAa,EAAE;;IAGjB,IAAIE,YAAY,EAAE;MAChB,OAAO,IAAI;;IAGb,OACEx1B,cAACuyB,cAAc;MACb5C,cAAc,EAAEA,cAAc;MAC9B5uB,GAAG,EAAEmH,OAAO;MACZlB,KAAK,EAAEA,KAAK;MACZkB,OAAO,EAAEA,OAAO;MAChB5I,MAAM,EAAEw0B,YAAY;MACpBtzB,cAAc,EAAEuzB,WAAW;MAC3B5xB,UAAU,EAAEA,UAAU;MACtBivB,QAAQ,EAAE9uB,cAAc;MACxB8I,WAAW,EAAEA;MACb;GAEL,CAAC;EAEF,OACEpL,cAACg0B,aAAa;IACZC,cAAc,EAAEA,cAAc;;;IAG9B30B,MAAM,EAAEg2B,aAAa,KAAK3sB,MAAM,CAAChG;KAEhCgG,MAAM,CACO;AAEpB;AAEA,IAAMuc,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/Bq1B,SAAS,EAAE;IACT,GAAG,EAAEx2B,UAAU,CAACw2B,SAAS;IACzB3B,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,GAAG;IACX7B,OAAO,EAAE;;CAEZ,CAAC;;;;ACtGF,IAAK8D,SAGJ;AAHD,WAAKA,SAAS;EACZA,qCAAE;EACFA,yCAAI;AACN,CAAC,EAHIA,SAAS,KAATA,SAAS;AAKd;AACA,SAAgBC,oBAAoBA;EAClC,IAAMhe,gBAAgB,GAAGU,mBAAmB,EAAE;EAC9C,IAAML,kBAAkB,GAAGa,qBAAqB,EAAE;EAClD,IAAAwD,qBAAA,GAAgBlL,4BAA4B,EAAE;IAAvClK,KAAK,GAAAoV,qBAAA;EACZ,IAAMja,UAAU,GAAG6Q,mBAAmB,EAAE;EAExC,IAAA2iB,qBAAA,GAAqCC,qBAAqB,CACxD7d,kBAAkB,CACnB;IAFO8d,MAAM,GAAAF,qBAAA,CAANE,MAAM;IAAEC,gBAAgB,GAAAH,qBAAA,CAAhBG,gBAAgB;EAGhC,IAAMnV,mBAAmB,GAAGtI,sBAAsB,EAAE;EACpD,IAAM0d,eAAe,GAAGC,eAAe,CAACje,kBAAkB,CAAC;EAC3D,IAAM3M,WAAW,GAAG4K,oBAAoB,EAAE;EAE1C,IAAMwO,MAAM,GAAGxB,gBAAgB,CAACtL,gBAAgB,CAACrK,OAAO,CAAC;EAEzD,IAAMqc,OAAO,GAAGlb,OAAO,CACrBxH,KAAK,IACHwd,MAAM,IACNlc,kBAAkB,CAACtB,KAAK,CAAC,IACzBwd,MAAM,CAACqH,SAAS,CAACC,QAAQ,CAACptB,UAAU,CAAC4J,kBAAkB,CAAC,CAC3D;EAEDsG,SAAS,CAAC;IACR,IAAI,CAAC8a,OAAO,EAAE;MACZ;;IAGF1P,sBAAsB,CAACjC,kBAAkB,CAAC1K,OAAO,CAAC;GACnD,EAAE,CAAC0K,kBAAkB,EAAE2R,OAAO,EAAEhS,gBAAgB,CAAC,CAAC;EAEnD,IAAIqB,GAAG,EAAEkd,YAAY;EAErB,IAAI,CAACvM,OAAO,IAAIhS,gBAAgB,CAACrK,OAAO,EAAE;IACxCsT,mBAAmB,CAAC,IAAI,CAAC;GAC1B,MAAM;IACL5H,GAAG,GAAG8c,MAAM,EAAE;IACdI,YAAY,GAAGF,eAAe,EAAE;;EAGlC,OACE/1B;IACE2lB,GAAG,EAAE5N,kBAAkB;IACvBxV,SAAS,EAAE0iB,EAAE,CACXC,QAAM,CAACxL,eAAe,EACtBoc,gBAAgB,EAAE,KAAKL,SAAS,CAACS,IAAI,IAAIhR,QAAM,CAACiR,UAAU,EAC1DzM,OAAO,IAAIxE,QAAM,CAACwE,OAAO,CAC1B;IACDlnB,KAAK,EAAE;MAAEuW,GAAG,EAAHA;;KAER2Q,OAAO,IAAI1iB,KAAK,GACb,CAACW,YAAY,CAACX,KAAK,CAAC,CAAC,CAClBmN,MAAM,CAACtL,eAAe,CAAC7B,KAAK,CAAC,CAAC,CAC9B4gB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACX3oB,GAAG,CAAC,UAAAiJ,OAAO;IAAA,OACVlI,cAACuyB,cAAc;MACbxxB,GAAG,EAAEmH,OAAO;MACZlB,KAAK,EAAEA,KAAK;MACZkB,OAAO,EAAEA,OAAO;MAChB/F,UAAU,EAAEA,UAAU;MACtBwtB,cAAc,EAAE,KAAK;MACrBvkB,WAAW,EAAEA;MACb;GACH,CAAC,GACJ,IAAI,EACRpL;IAAKuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACkR,OAAO,CAAC;IAAE5zB,KAAK,EAAEyzB;IAAgB,CACvD;AAEV;AAEA,SAASD,eAAeA,CAACje,kBAAgD;EACvE,IAAML,gBAAgB,GAAGU,mBAAmB,EAAE;EAC9C,OAAO,SAAS2d,eAAeA;IAC7B,IAAMvzB,KAAK,GAAwB,EAAE;IACrC,IAAI,CAACuV,kBAAkB,CAAC1K,OAAO,EAAE;MAC/B,OAAO7K,KAAK;;IAGd,IAAIkV,gBAAgB,CAACrK,OAAO,EAAE;MAC5B,IAAMmX,MAAM,GAAGxB,gBAAgB,CAACtL,gBAAgB,CAACrK,OAAO,CAAC;MAEzD,IAAM+d,UAAU,GAAGL,kBAAkB,CAACvG,MAAM,CAAC;MAE7C,IAAI,CAACA,MAAM,EAAE;QACX,OAAOhiB,KAAK;;;MAIdA,KAAK,CAACukB,IAAI,GAAGqE,UAAU,GAAG,CAAA5G,MAAM,oBAANA,MAAM,CAAE6R,WAAW,IAAG,CAAC;;IAGnD,OAAO7zB,KAAK;GACb;AACH;AAEA,SAASozB,qBAAqBA,CAC5B7d,kBAAgD;EAEhD,IAAML,gBAAgB,GAAGU,mBAAmB,EAAE;EAC9C,IAAMT,OAAO,GAAGY,UAAU,EAAE;EAC5B,IAAI+d,SAAS,GAAGb,SAAS,CAACc,EAAE;EAE5B,OAAO;IACLT,gBAAgB,EAAhBA,gBAAgB;IAChBD,MAAM,EAANA;GACD;EAED,SAASC,gBAAgBA;IACvB,OAAOQ,SAAS;;EAGlB,SAAST,MAAMA;IACbS,SAAS,GAAGb,SAAS,CAACc,EAAE;IACxB,IAAIC,cAAc,GAAG,CAAC;IAEtB,IAAI,CAACze,kBAAkB,CAAC1K,OAAO,EAAE;MAC/B,OAAO,CAAC;;IAGV,IAAMzM,MAAM,GAAGqmB,aAAa,CAAClP,kBAAkB,CAAC1K,OAAO,CAAC;IAExD,IAAIqK,gBAAgB,CAACrK,OAAO,EAAE;MAAA,IAAAopB,kBAAA;MAC5B,IAAMnZ,OAAO,GAAG3F,OAAO,CAACtK,OAAO;MAC/B,IAAMmX,MAAM,GAAGxB,gBAAgB,CAACtL,gBAAgB,CAACrK,OAAO,CAAC;MAEzD,IAAMqpB,YAAY,GAAGzP,aAAa,CAACzC,MAAM,CAAC;MAE1CgS,cAAc,GAAGnM,kBAAkB,CAAC7F,MAAM,CAAC;MAE3C,IAAMtL,SAAS,IAAAud,kBAAA,GAAGnZ,OAAO,oBAAPA,OAAO,CAAEpE,SAAS,YAAAud,kBAAA,GAAI,CAAC;MAEzC,IAAIvd,SAAS,GAAGsd,cAAc,GAAG51B,MAAM,EAAE;QACvC01B,SAAS,GAAGb,SAAS,CAACS,IAAI;QAC1BM,cAAc,IAAIE,YAAY,GAAG91B,MAAM;;;IAI3C,OAAO41B,cAAc,GAAG51B,MAAM;;AAElC;AAEA,IAAMskB,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,eAAAC,QAAA;EAC9B4Z,eAAe,EAAE;IACf,GAAG,EAAEhb,UAAU,CAACgb,eAAe;IAC/BoM,QAAQ,EAAE,UAAU;IACpBwK,KAAK,EAAE,MAAM;IACbvJ,IAAI,EAAE,MAAM;IACZ4K,OAAO,EAAE,KAAK;IACdgF,SAAS,EAAE,gCAAgC;IAC3CzQ,YAAY,EAAE,KAAK;IACnB3mB,OAAO,EAAE,MAAM;IACf0wB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,cAAc;IAC9B1wB,OAAO,EAAE,GAAG;IACZE,UAAU,EAAE,QAAQ;IACpBD,aAAa,EAAE,MAAM;IACrBsZ,GAAG,EAAE,OAAO;IACZuW,MAAM,EAAE,0CAA0C;IAClD1uB,MAAM,EAAE,0CAA0C;IAClD+vB,MAAM,EAAE,0CAA0C;IAClDpB,UAAU,EAAE,4CAA4C;IACxDkB,SAAS,EAAE,YAAY;IACvB9vB,UAAU,EAAE;GACb;EACD+oB,OAAO,EAAE;IACPlqB,OAAO,EAAE,GAAG;IACZE,UAAU,EAAE,SAAS;IACrBD,aAAa,EAAE,KAAK;IACpBgxB,SAAS,EAAE;GACZ;EACD0F,UAAU,EAAE;IACV,GAAG,EAAE,aAAa;IAClBS,eAAe,EAAE,WAAW;IAC5BnG,SAAS,EAAE;GACZ;EACD,cAAc,EAAE;IACd2F,OAAO,EAAE;MACPrd,GAAG,EAAE,GAAG;MACR0X,SAAS,EAAE;;GAEd;EACD2F,OAAO,EAAE;IACP,GAAG,EAAE,mBAAmB;IACxB/F,OAAO,EAAE,EAAE;IACXvK,QAAQ,EAAE,UAAU;IACpBjlB,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdoyB,gBAAgB,EAAE,WAAW;IAC7B6D,kBAAkB,EAAE,KAAK;IACzB5D,cAAc,EAAE,WAAW;IAC3Bla,GAAG,EAAE,MAAM;IACX0X,SAAS,EAAE,mBAAmB;IAC9BqC,eAAe,WAASgE,WAAW;;AACpC,gBACEh2B,QAAQ,CAAC,SAAS,EAAE;EACrB+1B,kBAAkB,EAAE;CACrB,CAAC,CACH,CAAC;;SC1NcE,IAAIA;EAClB,IAAMpf,OAAO,GAAGY,UAAU,EAAE;EAC5Bkb,WAAW,CAAC9b,OAAO,CAAC;EACpBqW,oBAAoB,CAACrW,OAAO,EAAErF,kBAAkB,CAAC0kB,MAAM,CAAC;EACxD3Z,cAAc,EAAE;EAEhB,OACErd;IACEuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAAC+R,IAAI,EAAE52B,uBAAuB,CAACK,iBAAiB,CAAC;IACrEilB,GAAG,EAAEhO;KAEL3X,cAAC01B,oBAAoB,OAAG,EACxB11B,cAACg1B,SAAS,OAAG,CACT;AAEV;AAEA,IAAM9P,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/Bo3B,IAAI,EAAE;IACJ,GAAG,EAAEv4B,UAAU,CAACib,UAAU;IAC1Bud,IAAI,EAAE,GAAG;IACTC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,QAAQ;IACnBtR,QAAQ,EAAE;;CAEb,CAAC;;SCxCcuR,6BAA6BA,CAC3C7S,MAAyB,EACzBlH,OAAwB;EAExB,IAAI,CAACkH,MAAM,IAAI,CAAClH,OAAO,EAAE;IACvB,OAAO,CAAC;;EAGV,IAAMga,UAAU,GAAG9S,MAAM,CAACkC,qBAAqB,EAAE;EACjD,IAAM6Q,QAAQ,GAAGja,OAAO,CAACoJ,qBAAqB,EAAE;;EAGhD,OAAO6Q,QAAQ,CAAC32B,MAAM,IAAI02B,UAAU,CAACE,CAAC,GAAGD,QAAQ,CAACC,CAAC,CAAC;AACtD;;SCEgBC,qBAAqBA,CACnCC,KAAc,EACdC,eAAmE;EAEnE,IAAMhgB,OAAO,GAAGY,UAAU,EAAE;EAC5B,IAAM6E,iBAAiB,GAAGD,oBAAoB,EAAE;EAChD,IAAMD,cAAc,GAAGD,iBAAiB,EAAE;EAE1CrO,SAAS,CAAC;IACR,IAAI,CAAC8oB,KAAK,EAAE;MACV;;IAEF,IAAMpa,OAAO,GAAG3F,OAAO,CAACtK,OAAO;IAE/BiQ,OAAO,oBAAPA,OAAO,CAAEC,gBAAgB,CAAC,SAAS,EAAEqa,QAAQ,EAAE;MAC7Cna,OAAO,EAAE;KACV,CAAC;IAEFH,OAAO,oBAAPA,OAAO,CAAEC,gBAAgB,CAAC,WAAW,EAAEsa,WAAW,EAAE,IAAI,CAAC;IAEzDva,OAAO,oBAAPA,OAAO,CAAEC,gBAAgB,CAAC,OAAO,EAAEua,OAAO,EAAE,IAAI,CAAC;IAEjDxa,OAAO,oBAAPA,OAAO,CAAEC,gBAAgB,CAAC,UAAU,EAAEwa,OAAO,EAAE;MAC7Cta,OAAO,EAAE;KACV,CAAC;IACFH,OAAO,oBAAPA,OAAO,CAAEC,gBAAgB,CAAC,MAAM,EAAEwa,OAAO,EAAE,IAAI,CAAC;IAEhD,SAASD,OAAOA,CAACE,CAAa;MAC5B,IAAMxT,MAAM,GAAGxB,gBAAgB,CAACgV,CAAC,CAAC1f,MAAqB,CAAC;MAExD,IAAI,CAACkM,MAAM,EAAE;QACX,OAAOuT,OAAO,EAAE;;MAGlB,IAAAE,qBAAA,GAAqC1M,0BAA0B,CAAC/G,MAAM,CAAC;QAA/Dtc,OAAO,GAAA+vB,qBAAA,CAAP/vB,OAAO;QAAE2hB,eAAe,GAAAoO,qBAAA,CAAfpO,eAAe;MAEhC,IAAI,CAAC3hB,OAAO,IAAI,CAAC2hB,eAAe,EAAE;QAChC,OAAOkO,OAAO,EAAE;;MAGlBJ,eAAe,CAAC;QACdzvB,OAAO,EAAPA,OAAO;QACP2hB,eAAe,EAAfA;OACD,CAAC;;IAEJ,SAASkO,OAAOA,CAACC,CAA2B;MAC1C,IAAIA,CAAC,EAAE;QACL,IAAME,aAAa,GAAGF,CAAC,CAACE,aAA4B;QAEpD,IAAI,CAAClV,gBAAgB,CAACkV,aAAa,CAAC,EAAE;UACpC,OAAOP,eAAe,CAAC,IAAI,CAAC;;;MAIhCA,eAAe,CAAC,IAAI,CAAC;;IAEvB,SAASC,QAAQA,CAACI,CAAgB;MAChC,IAAIA,CAAC,CAACj3B,GAAG,KAAK,QAAQ,EAAE;QACtB42B,eAAe,CAAC,IAAI,CAAC;;;IAIzB,SAASE,WAAWA,CAACG,CAAa;MAChC,IAAI5a,iBAAiB,EAAE,EAAE;QACvB;;MAGF,IAAMoH,MAAM,GAAGxB,gBAAgB,CAACgV,CAAC,CAAC1f,MAAqB,CAAC;MAExD,IAAIkM,MAAM,EAAE;QACV,IAAM2T,aAAa,GAAGd,6BAA6B,CAAC7S,MAAM,EAAElH,OAAO,CAAC;QACpE,IAAMoZ,YAAY,GAAGlS,MAAM,CAACkC,qBAAqB,EAAE,CAAC9lB,MAAM;QAC1D,IAAIu3B,aAAa,GAAGzB,YAAY,EAAE;UAChC,OAAO0B,kCAAkC,CAAC5T,MAAM,EAAEmT,eAAe,CAAC;;QAGpEjhB,YAAY,CAAC8N,MAAM,CAAC;;;IAIxB,OAAO;MACLlH,OAAO,oBAAPA,OAAO,CAAEI,mBAAmB,CAAC,WAAW,EAAEma,WAAW,CAAC;MACtDva,OAAO,oBAAPA,OAAO,CAAEI,mBAAmB,CAAC,UAAU,EAAEqa,OAAO,CAAC;MACjDza,OAAO,oBAAPA,OAAO,CAAEI,mBAAmB,CAAC,OAAO,EAAEoa,OAAO,EAAE,IAAI,CAAC;MACpDxa,OAAO,oBAAPA,OAAO,CAAEI,mBAAmB,CAAC,MAAM,EAAEqa,OAAO,EAAE,IAAI,CAAC;MACnDza,OAAO,oBAAPA,OAAO,CAAEI,mBAAmB,CAAC,SAAS,EAAEka,QAAQ,CAAC;KAClD;GACF,EAAE,CAACjgB,OAAO,EAAE+f,KAAK,EAAEC,eAAe,EAAEva,iBAAiB,EAAEF,cAAc,CAAC,CAAC;AAC1E;AAEA,SAASkb,kCAAkCA,CACzC5T,MAAmB,EACnBmT,eAAmE;;EAEnE,IAAAU,sBAAA,GAAqC9M,0BAA0B,CAAC/G,MAAM,CAAC;IAA/Dtc,OAAO,GAAAmwB,sBAAA,CAAPnwB,OAAO;IAAE2hB,eAAe,GAAAwO,sBAAA,CAAfxO,eAAe;EAEhC,IAAI,CAAC3hB,OAAO,IAAI,CAAC2hB,eAAe,EAAE;IAChC;;EAGD,CAAAyO,qBAAA,GAAAhhB,QAAQ,CAACC,aAA6B,qBAAtC+gB,qBAAA,CAAwCC,IAAI,oBAA5CD,qBAAA,CAAwCC,IAAI,EAAI;EAEjDZ,eAAe,CAAC;IACdzvB,OAAO,EAAPA,OAAO;IACP2hB,eAAe,EAAfA;GACD,CAAC;AACJ;;;AC3HA,AAKA,IAAY2O,aAGX;AAHD,WAAYA,aAAa;EACvBA,gCAAe;EACfA,sCAAqB;AACvB,CAAC,EAHWA,aAAa,KAAbA,aAAa;AAYzB,SAAwBC,IAAIA,CAAAnyB,IAAA;MAC1B0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;IACRzJ,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;IAAAm2B,UAAA,GAAApyB,IAAA,CACT9D,KAAK;IAALA,KAAK,GAAAk2B,UAAA,cAAG,EAAE,GAAAA,UAAA;IAAAC,cAAA,GAAAryB,IAAA,CACVgwB,SAAS;IAATA,SAAS,GAAAqC,cAAA,cAAGH,aAAa,CAACI,GAAG,GAAAD,cAAA;EAE7B,OACE34B;IACEwC,KAAK,EAAA1C,QAAA,KAAO0C,KAAK,CAAE;IACnBD,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACgS,IAAI,EAAE30B,SAAS,EAAE2iB,QAAM,CAACoR,SAAS,CAAC;KAEtDtqB,QAAQ,CACL;AAEV;AAEA,IAAMkZ,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,EAAAg5B,kBAAA;EAC9B3B,IAAI,EAAE;IACJ33B,OAAO,EAAE;;AACV,GAAAs5B,kBAAA,CACAL,aAAa,CAACI,GAAG,IAAG;EACnB7S,aAAa,EAAE;CAChB,EAAA8S,kBAAA,CACAL,aAAa,CAACM,MAAM,IAAG;EACtB/S,aAAa,EAAE;CAChB,EAAA8S,kBAAA,EACD;;SClCsB5V,KAAKA,CAAA3c,IAAA;MAAG/D,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;IAAAm2B,UAAA,GAAApyB,IAAA,CAAE9D,KAAK;IAALA,KAAK,GAAAk2B,UAAA,cAAG,EAAE,GAAAA,UAAA;EACnD,OAAO14B;IAAKwC,KAAK,EAAA1C,QAAA;MAAIo3B,IAAI,EAAE;OAAM10B,KAAK,CAAE;IAAED,SAAS,EAAE0iB,EAAE,CAAC1iB,SAAS;IAAK;AACxE;;SCHwBw2B,QAAQA,CAAAzyB,IAAA;MAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;IAAEzJ,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;IAAEC,KAAK,GAAA8D,IAAA,CAAL9D,KAAK;EAC3D,OACExC;IAAKwC,KAAK,EAAA1C,QAAA,KAAO0C,KAAK;MAAEsjB,QAAQ,EAAE;MAAY;IAAEvjB,SAAS,EAAEA;KACxDyJ,QAAQ,CACL;AAEV;;SCNwBgtB,QAAQA,CAAA1yB,IAAA;MAAG0F,QAAQ,GAAA1F,IAAA,CAAR0F,QAAQ;IAAEzJ,SAAS,GAAA+D,IAAA,CAAT/D,SAAS;IAAEC,KAAK,GAAA8D,IAAA,CAAL9D,KAAK;EAC3D,OACExC;IAAKwC,KAAK,EAAA1C,QAAA,KAAO0C,KAAK;MAAEsjB,QAAQ,EAAE;MAAY;IAAEvjB,SAAS,EAAEA;KACxDyJ,QAAQ,CACL;AAEV;;ACEA;AACA,SAAgBitB,oBAAoBA,CAAA3yB,IAAA;MAClC+b,MAAM,GAAA/b,IAAA,CAAN+b,MAAM;IACNiM,OAAO,GAAAhoB,IAAA,CAAPgoB,OAAO;IACP4K,QAAQ,GAAA5yB,IAAA,CAAR4yB,QAAQ;IACRC,iBAAiB,GAAA7yB,IAAA,CAAjB6yB,iBAAiB;IACjB32B,KAAK,GAAA8D,IAAA,CAAL9D,KAAK;EAEL,OACExC,cAACkvB,MAAM;IACL1sB,KAAK,EAAEA,KAAK;IACZ8rB,OAAO,EAAEA,OAAO;IAChB/rB,SAAS,EAAE0iB,EAAE,eACCkU,iBAAiB,EAC7BjU,QAAM,CAACkU,IAAI,EACX,CAAC/W,MAAM,IAAI6C,QAAM,CAACmU,UAAU,EAC5BH,QAAQ,IAAIhU,QAAM,CAACoU,MAAM,CAC1B;oBACaJ,QAAQ;iCACGhzB,cAAc,CAACizB,iBAA8B;IAC9D;AAEd;AAEA,IAAMjU,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/Bw5B,UAAU,EAAE;IACV75B,OAAO,EAAE,GAAG;IACZmxB,MAAM,EAAE;GACT;EACD2I,MAAM,EAAE;IACN,GAAG,EAAE,YAAY;IACjB3I,MAAM,EAAE,GAAG;IACXnxB,OAAO,EAAE;GACV;EACD45B,IAAI,EAAE;IACJ,GAAG,EAAE,UAAU;IACfv4B,KAAK,EAAE,2BAA2B;IAClCtB,OAAO,EAAE,OAAO;IAChB8vB,MAAM,EAAE,SAAS;IACjBnJ,YAAY,EAAE,KAAK;IACnBtlB,MAAM,EAAE,2BAA2B;IACnCklB,QAAQ,EAAE,UAAU;IACpBwK,KAAK,EAAE,GAAG;IACV3vB,UAAU,EAAE,uDAAuD;IACnEgwB,MAAM,EAAE,GAAG;IACXrB,MAAM,EAAE,mDAAmD;IAC3DqH,SAAS,EAAE,6DAA6D;IACxE,QAAQ,EAAE;MACRA,SAAS,EAAE;KACZ;IACD,QAAQ,EAAE;MACRA,SAAS,EAAE;KACZ;IACD,oBAAoB,EAAE;MACpBvQ,eAAe,EAAE;KAClB;IACD,kBAAkB,EAAE;MAClBA,eAAe,EAAE;KAClB;IACD,kBAAkB,EAAE;MAClBA,eAAe,EAAE;KAClB;IACD,kBAAkB,EAAE;MAClBA,eAAe,EAAE;KAClB;IACD,kBAAkB,EAAE;MAClBA,eAAe,EAAE;KAClB;IACD,kBAAkB,EAAE;MAClBA,eAAe,EAAE;;;CAGtB,CAAC;;ACxFF;AACA,AAsBA,IAAMmT,SAAS,GAAG,EAAE;AAMpB,SAAgBC,kBAAkBA;EAChC,OACEx5B,cAACg5B,QAAQ;IAACx2B,KAAK,EAAE;MAAE5B,MAAM,EAAE24B;;KACzBv5B,cAAC+4B,QAAQ;IAACv2B,KAAK,EAAE;MAAEgmB,MAAM,EAAE,CAAC;MAAE8H,KAAK,EAAE;;KACnCtwB,cAACy5B,cAAc;IAACnD,SAAS,EAAEoD,uBAAuB,CAACC;IAAY,CACtD,CACF;AAEf;AAEA,SAAgBF,cAAcA,CAAAnzB,IAAA;4BAC5BgwB,SAAS;IAATA,SAAS,GAAAqC,cAAA,cAAGe,uBAAuB,CAACE,UAAU,GAAAjB,cAAA;EAE9C,IAAM9gB,iBAAiB,GAAGa,oBAAoB,EAAE;EAChD,IAAMmhB,UAAU,GAAG/mB,0BAA0B,EAAE;EAC/C,IAAAwJ,qBAAA,GAA4BlL,uBAAuB,EAAE;IAA9CiR,MAAM,GAAA/F,qBAAA;IAAEgG,SAAS,GAAAhG,qBAAA;EACxB,IAAA6R,qBAAA,GAA4Cvd,sBAAsB,EAAE;IAA7DnB,cAAc,GAAA0e,qBAAA;IAAE2L,iBAAiB,GAAA3L,qBAAA;EACxC,IAAM9b,gBAAgB,GAAGmC,yBAAyB,EAAE;EACpD,IAAMiI,mBAAmB,GAAGN,sBAAsB,EAAE;EACpD,IAAMiC,gBAAgB,GAAGT,mBAAmB,EAAE;EAE9C,IAAIkc,UAAU,EAAE;IACd,OAAO,IAAI;;EAGb,IAAME,SAAS,GAAMR,SAAS,GAAG5zB,kBAAkB,CAAChD,MAAM,OAAI;EAE9D,IAAMq3B,YAAY,GAAG3X,MAAM,GAAG0X,SAAS,GAAGR,SAAS,GAAG,IAAI;EAE1D,IAAMU,QAAQ,GAAG3D,SAAS,KAAKoD,uBAAuB,CAACC,QAAQ;EAE/D,OACE35B,cAACg5B,QAAQ;IACPz2B,SAAS,EAAE0iB,EAAE,CACXC,QAAM,CAACgV,SAAS,EAChBD,QAAQ,IAAI/U,QAAM,CAAC+U,QAAQ,EAC3B5X,MAAM,IAAI6C,QAAM,CAACvjB,IAAI,EACrBs4B,QAAQ,IAAI5X,MAAM,IAAI6C,QAAM,CAACiV,cAAc,CAC5C;IACD33B,KAAK,EACHy3B,QAAQ,GACJ;MAAEG,SAAS,EAAEJ,YAAY;MAAEp5B,MAAM,EAAEo5B;KAAc,GACjD;MAAEI,SAAS,EAAEJ;;KAGnBh6B;IAAKuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACmV,MAAM,CAAC;IAAE1U,GAAG,EAAE9N;KACrClS,kBAAkB,CAAC1G,GAAG,CAAC,UAACk6B,iBAAiB,EAAE5L,CAAC;IAC3C,IAAM+L,MAAM,GAAGH,iBAAiB,KAAK1pB,cAAc;IAEnD,OACEzP,cAACi5B,oBAAoB;MACnBl4B,GAAG,EAAEo4B,iBAAiB;MACtBA,iBAAiB,EAAEA,iBAAiB;MACpC9W,MAAM,EAAEA,MAAM;MACd7f,KAAK,EAAE;QACLiuB,SAAS,EAAExL,EAAE,CACXgV,QAAQ,oBACW1M,CAAC,IAAIlL,MAAM,GAAGkX,SAAS,GAAG,CAAC,CAAC,4BAC5BhM,CAAC,IAAIlL,MAAM,GAAGkX,SAAS,GAAG,CAAC,CAAC,QAAK,EACpDlX,MAAM,IAAIiX,MAAM,IAAI,YAAY;OAEnC;MACDJ,QAAQ,EAAEI,MAAM;MAChBhL,OAAO,EAAE,SAAAA;QACP,IAAIjM,MAAM,EAAE;UACVyX,iBAAiB,CAACX,iBAAiB,CAAC;UACpC9mB,gBAAgB,CAAC8mB,iBAAiB,CAAC;UACnC/a,gBAAgB,EAAE;SACnB,MAAM;UACLkE,SAAS,CAAC,IAAI,CAAC;;QAEjB7F,mBAAmB,EAAE;;MAEvB;GAEL,CAAC,CACE,CACG;AAEf;AAEA,AAAA,IAAYid,uBAGX;AAHD,WAAYA,uBAAuB;EACjCA,oDAA8B;EAC9BA,wDAAkC;AACpC,CAAC,EAHWA,uBAAuB,KAAvBA,uBAAuB;AAKnC,IAAMxU,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/Bq6B,SAAS,EAAE;IACT,GAAG,EAAE,gBAAgB;IACrB,IAAI,EAAE;MACJ,sBAAsB,EAAE;KACzB;IACD36B,OAAO,EAAE,MAAM;IACf0wB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,UAAU;IAC1BvvB,UAAU,EAAE,sBAAsB;IAClCgxB,OAAO,EAAE;GACV;EACDsI,QAAQ,EAAE;IACRtI,OAAO,EAAE,KAAK;IACd1B,UAAU,EAAE,UAAU;IACtBlK,aAAa,EAAE,QAAQ;IACvBG,YAAY,EAAE,KAAK;IACnBoJ,MAAM,EAAE;GACT;EACD6K,cAAc,EAAE;IACdxD,SAAS,EAAE;GACZ;EACDh1B,IAAI,EAAE;;IAEJ6kB,cAAc,EAAE,WAAW;IAC3B+I,UAAU,EAAE,wCAAwC;IACpD,aAAa,EAAE;MACbD,MAAM,EAAE;;GAEX;EACD+K,MAAM,EAAE;IACN,GAAG,EAAE,sBAAsB;IAC3BvU,QAAQ,EAAE,UAAU;IACpBjlB,KAAK,EAAE,2BAA2B;IAClCD,MAAM,EAAE;;CAEX,CAAC;;SC1Hc05B,OAAOA;EACrB,IAAM3vB,aAAa,GAAG+J,gBAAgB,EAAE;EACxC,IAAM6N,mBAAmB,GAAGtB,sBAAsB,EAAE;EACpD,IAAApN,qBAAA,GAAwBrD,qBAAqB,EAAE;IAAxC2iB,aAAa,GAAAtf,qBAAA;EAEpB,IAAI,CAAClJ,aAAa,CAACkB,WAAW,EAAE;IAC9B,OAAO,IAAI;;EAGb,OACE7L,cAACy4B,IAAI;IACHl2B,SAAS,EAAE0iB,EAAE,CACXC,QAAM,CAACqV,OAAO,EACdl6B,uBAAuB,CAACK,iBAAiB,EACzCyyB,aAAa,IAAIjO,QAAM,CAACsV,eAAe;KAGzCx6B,cAACy6B,WAAW,OAAG,EACfz6B,cAACijB,KAAK,OAAG,EACRV,mBAAmB,GAAGviB,cAACw5B,kBAAkB,OAAG,GAAG,IAAI,CAC/C;AAEX;AAEA,SAAgBiB,WAAWA;;EACzB,IAAM9vB,aAAa,GAAG+J,gBAAgB,EAAE;EACxC,IAAA9H,SAAA,GAAwCC,QAAQ,CAAe,IAAI,CAAC;IAA7D6tB,YAAY,GAAA9tB,SAAA;IAAE+qB,eAAe,GAAA/qB,SAAA;EACpC,IAAMzK,UAAU,GAAG6Q,mBAAmB,EAAE;EACxC,IAAAoJ,qBAAA,GAA+BlL,4BAA4B,EAAE;IAAtDypB,oBAAoB,GAAAve,qBAAA;EAC3B,IAAMhR,WAAW,GAAG4K,oBAAoB,EAAE;EAE1CyhB,qBAAqB,CAAC9sB,aAAa,CAACkB,WAAW,EAAE8rB,eAAe,CAAC;EAEjE,IAAM3wB,KAAK,GAAGmC,cAAc,EAAAyxB,qBAAA,GAC1BF,YAAY,oBAAZA,YAAY,CAAExyB,OAAO,YAAA0yB,qBAAA,GAAIF,YAAY,oBAAZA,YAAY,CAAE7Q,eAAe,CACvD;EAED,IAAMgR,IAAI,GAAG7zB,KAAK,IAAI,IAAI,IAAI0zB,YAAY,IAAI,IAAI;EAElD,OAAO16B,cAAC86B,cAAc,OAAG;EAEzB,SAASA,cAAcA;IACrB,IAAMnvB,YAAY,GAChBgvB,oBAAoB,WAApBA,oBAAoB,GAAIxxB,cAAc,CAACwB,aAAa,CAACgB,YAAY,CAAC;IACpE,IAAI,CAACA,YAAY,EAAE;MACjB,OAAO,IAAI;;IAEb,IAAMovB,WAAW,GAAGJ,oBAAoB,GACpC3yB,SAAS,CAAC2yB,oBAAoB,CAAC,GAC/BhwB,aAAa,CAACiB,cAAc;IAEhC,OACE5L,8BACEA,2BACG66B,IAAI,GACH76B,cAACiyB,aAAa;MACZ/pB,OAAO,EAAEwyB,YAAY,oBAAZA,YAAY,CAAExyB,OAAiB;MACxClB,KAAK,EAAEA,KAAK;MACZ7E,UAAU,EAAEA,UAAU;MACtB+vB,IAAI,EAAE,EAAE;MACR9mB,WAAW,EAAEA,WAAW;MACxB7I,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACle,KAAK;MAC1B,GACA2E,YAAY,GACd3L,cAACiyB,aAAa;MACZ/pB,OAAO,EAAEP,YAAY,CAACgE,YAAY,CAAC;MACnC3E,KAAK,EAAE2E,YAAY;MACnBxJ,UAAU,EAAEA,UAAU;MACtB+vB,IAAI,EAAE,EAAE;MACR9mB,WAAW,EAAEA,WAAW;MACxB7I,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACle,KAAK;MAC1B,GACA,IAAI,CACJ,EACNhH;MAAKuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACmE,KAAK;OAC5BwR,IAAI,GAAG7yB,SAAS,CAAChB,KAAK,CAAC,GAAG+zB,WAAW,CAClC,CACL;;AAGT;AAOA,IAAM7V,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/B06B,OAAO,EAAE;IACPtK,UAAU,EAAE,QAAQ;IACpB+K,SAAS,EAAE,2CAA2C;IACtDp6B,MAAM,EAAE,2BAA2B;IACnC+wB,OAAO,EAAE,iCAAiC;IAC1C7L,QAAQ,EAAE,UAAU;IACpB6K,MAAM,EAAE;GACT;EACDtH,KAAK,EAAE;IACLwJ,KAAK,EAAE,+BAA+B;IACtC/B,QAAQ,EAAE,8BAA8B;IACxCa,OAAO,EAAE,iCAAiC;IAC1C2C,aAAa,EAAE;GAChB;EACDttB,KAAK,EAAE;IACL2qB,OAAO,EAAE;GACV;EACD6I,eAAe,EAAE;IACfh7B,OAAO,EAAE,GAAG;IACZmB,UAAU,EAAE;;CAEf,CAAC;;SC1Ics6B,mBAAmBA,CAACC,SAAyB;;EAC3D,QAAAC,qBAAA,GAAOD,SAAS,oBAATA,SAAS,CAAEE,YAAY,CAAC,WAAW,CAAC,YAAAD,qBAAA,GAAI,IAAI;AACrD;;SCIgBE,gCAAgCA,CAC9CC,iBAA6C;EAE7C,IAAM3jB,OAAO,GAAGY,UAAU,EAAE;EAE5B3J,SAAS,CAAC;IACR,IAAM2sB,iBAAiB,GAAG,IAAIC,GAAG,EAAE;IACnC,IAAMle,OAAO,GAAG3F,OAAO,CAACtK,OAAO;IAC/B,IAAMouB,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,UAAAv1B,OAAO;MACL,IAAI,CAACmX,OAAO,EAAE;QACZ;;MAGF,SAAAqe,SAAA,GAAAC,+BAAA,CAAoBz1B,OAAO,GAAA01B,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAAG,IAAA,GAAE;QAAA,IAAlBC,KAAK,GAAAF,KAAA,CAAA76B,KAAA;QACd,IAAM4I,GAAE,GAAGqxB,mBAAmB,CAACc,KAAK,CAACzjB,MAAM,CAAC;QAC5CijB,iBAAiB,CAACS,GAAG,CAACpyB,GAAE,EAAEmyB,KAAK,CAACE,iBAAiB,CAAC;;MAGpD,IAAMC,MAAM,GAAGr9B,KAAK,CAACsqB,IAAI,CAACoS,iBAAiB,CAAC;MAC5C,IAAMY,YAAY,GAAGD,MAAM,CAACA,MAAM,CAACv5B,MAAM,GAAG,CAAC,CAAC;MAE9C,IAAIw5B,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;QACxB,OAAOb,iBAAiB,CAACa,YAAY,CAAC,CAAC,CAAC,CAAC;;MAG3C,SAAA7S,EAAA,MAAA8S,OAAA,GAA0BF,MAAM,EAAA5S,EAAA,GAAA8S,OAAA,CAAAz5B,MAAA,EAAA2mB,EAAA,IAAE;QAA7B,IAAA+S,UAAA,GAAAD,OAAA,CAAA9S,EAAA;UAAO1f,EAAE,GAAAyyB,UAAA;UAAEC,KAAK,GAAAD,UAAA;QACnB,IAAIC,KAAK,EAAE;UACThB,iBAAiB,CAAC1xB,EAAE,CAAC;UACrB;;;KAGL,EACD;MACE2yB,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;KACjB,CACF;IACDjf,OAAO,oBAAPA,OAAO,CAAE8L,gBAAgB,CAACzqB,WAAW,CAACD,UAAU,CAACsF,QAAQ,CAAC,CAAC,CAACwD,OAAO,CAAC,UAAAg1B,EAAE;MACpEf,QAAQ,CAACgB,OAAO,CAACD,EAAE,CAAC;KACrB,CAAC;GACH,EAAE,CAAC7kB,OAAO,EAAE2jB,iBAAiB,CAAC,CAAC;AAClC;;SCxCgBoB,yBAAyBA;EACvC,IAAM/kB,OAAO,GAAGY,UAAU,EAAE;EAC5B,IAAMd,aAAa,GAAGU,gBAAgB,EAAE;EAExC,OAAO,SAASwkB,sBAAsBA,CAAC34B,QAAgB;;IACrD,IAAI,CAAC2T,OAAO,CAACtK,OAAO,EAAE;MACpB;;IAEF,IAAM6tB,SAAS,IAAA0B,gBAAA,GAAGjlB,OAAO,CAACtK,OAAO,qBAAfuvB,gBAAA,CAAiBnS,aAAa,mBAC/BzmB,QAAQ,QAAI,CACT;IAEpB,IAAI,CAACk3B,SAAS,EAAE;MACd;;IAGF,IAAMhQ,SAAS,GAAGgQ,SAAS,CAAChQ,SAAS,IAAI,CAAC;IAE1CrS,QAAQ,CAACpB,aAAa,CAACpK,OAAO,EAAE6d,SAAS,CAAC;GAC3C;AACH;;SCzBgB2R,yBAAyBA;EACvC,IAAMC,oBAAoB,GAAGxpB,qBAAqB,EAAE;EAEpD,IAAI,CAACwpB,oBAAoB,EAAE;IACzB,OAAO,KAAK;;EAGd,OAAOA,oBAAoB,CAACn6B,MAAM,KAAK,CAAC;AAC1C;;;;SCegBo6B,cAAcA,CAAAz2B,IAAA;;MAC5B02B,gBAAgB,GAAA12B,IAAA,CAAhB02B,gBAAgB;IAChBh5B,QAAQ,GAAAsC,IAAA,CAARtC,QAAQ;IACRi5B,eAAe,GAAA32B,IAAA,CAAf22B,eAAe;IACfhJ,cAAc,GAAA3tB,IAAA,CAAd2tB,cAAc;IACd3F,OAAO,GAAAhoB,IAAA,CAAPgoB,OAAO;EAEP,OACEtuB,cAACkvB,MAAM;IACLyD,QAAQ,EAAEsK,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC;IAClC16B,SAAS,EAAE0iB,EAAE,CACXC,QAAM,CAACgY,MAAM,EACb78B,uBAAuB,CAACC,WAAW,eACxB0D,QAAQ,GAAAwhB,GAAA,OAAAA,GAAA,CAEhB9mB,UAAU,CAAC46B,MAAM,IAAG0D,gBAAgB,EAAAxX,GAAA,EAExC;IACD8I,OAAO,EAAEA,OAAO;kBACJhqB,8BAA8B,CAAC2vB,cAAc,CAAC;qBAC3C+I,gBAAgB;IAC/BG,IAAI,EAAC,KAAK;qBACI;IACd;AAEN;AAEA,IAAMC,mBAAmB,GAAG;EAC1B78B,mBAAmB,EAAE;CACtB;AACD,IAAM88B,aAAa,GAAG;EACpB98B,mBAAmB,EAAE;CACtB;AAED,IAAM+8B,oBAAoB,GAAG;EAC3B,0BAA0B,EAAE;IAC1BJ,MAAM,EAAE;MACN,QAAQ,EAAEE,mBAAmB;MAC7B,cAAc,EAAEA;;;CAGrB;AAED,IAAMlY,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,eAAAC,QAAA;EAC9Bo9B,MAAM,EAAE;IACN,GAAG,EAAE,aAAa;IAClB39B,OAAO,EAAE,cAAc;IACvBoB,UAAU,EAAE,0BAA0B;IACtCmlB,QAAQ,EAAE,UAAU;IACpBllB,MAAM,EAAE,4CAA4C;IACpDC,KAAK,EAAE,4CAA4C;IACnDoyB,cAAc,EAAE,uDAAuD;IACvEzD,OAAO,EAAE,MAAM;IACfqH,kBAAkB,EAAE,KAAK;IACzB/D,eAAe,WAASyK,aAAa,MAAG;IACxC,eAAe,EAAE;MACflN,OAAO,EAAE,EAAE;MACXvK,QAAQ,EAAE,UAAU;MACpB/M,GAAG,EAAE,MAAM;MACXgO,IAAI,EAAE,MAAM;MACZuJ,KAAK,EAAE,MAAM;MACb9H,MAAM,EAAE,MAAM;MACd8G,MAAM,EAAE,iDAAiD;MACzDpJ,YAAY,EAAE;KACf;IACD,qBAAqB,EAAE;MACrBsX,mBAAmB,EACjB;KACH;IACD,kBAAkB,EAAE;MAClBA,mBAAmB,EACjB;KACH;IACD,sBAAsB,EAAE;MACtBA,mBAAmB,EACjB;KACH;IACD,0BAA0B,EAAE;MAC1BA,mBAAmB,EACjB;KACH;IACD,iBAAiB,EAAE;MACjBA,mBAAmB,EACjB;KACH;IACD,sBAAsB,EAAE;MACtBA,mBAAmB,EACjB;KACH;IACD,mBAAmB,EAAE;MACnBA,mBAAmB,EACjB;KACH;IACD,0BAA0B,EAAE;MAC1BA,mBAAmB,EAAE;KACtB;IACD,mBAAmB,EAAE;MACnBA,mBAAmB,EACjB;KACH;IACD,yBAAyB,EAAE;MACzBA,mBAAmB,EACjB;;;AAEL,gBACE18B,QAAQ,CAAC,QAAQ,EAAEu8B,aAAa,CAAC;EACpC,iBAAiB,eAAAv9B,QAAA,KACZw9B,oBAAoB,CACxB;EACD,iBAAiB,eAAAx9B,QAAA,KACZw9B,oBAAoB;AACxB,EACF,CAAC;;SCzHcG,kBAAkBA;EAChC,IAAA7wB,SAAA,GAA4CC,QAAQ,CAAgB,IAAI,CAAC;IAAlE6wB,cAAc,GAAA9wB,SAAA;IAAE0uB,iBAAiB,GAAA1uB,SAAA;EACxC,IAAM+vB,sBAAsB,GAAGD,yBAAyB,EAAE;EAC1DrB,gCAAgC,CAACC,iBAAiB,CAAC;EACnD,IAAMnY,YAAY,GAAG3M,eAAe,EAAE;EAEtC,IAAMmnB,gBAAgB,GAAGvqB,mBAAmB,EAAE;EAC9C,IAAM0E,qBAAqB,GAAGa,wBAAwB,EAAE;EACxD,IAAMilB,kBAAkB,GAAGf,yBAAyB,EAAE;EAEtD,OACE78B;IACEuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAAC2Y,GAAG,CAAC;IACzBV,IAAI,EAAC,SAAS;kBACH,qBAAqB;IAChCvzB,EAAE,EAAC,qBAAqB;IACxB+b,GAAG,EAAE7N;KAEJ6lB,gBAAgB,CAAC1+B,GAAG,CAAC,UAAAg1B,cAAc;IAClC,IAAMjwB,QAAQ,GAAGK,0BAA0B,CAAC4vB,cAAc,CAAC;IAC3D,IAAM+I,gBAAgB,GAAGh5B,QAAQ,KAAK05B,cAAc;IAEpD,IAAI5P,gBAAgB,CAACmG,cAAc,CAAC,IAAI2J,kBAAkB,EAAE;MAC1D,OAAO,IAAI;;IAGb,IAAMX,eAAe,GAAG,CAAC9Z,YAAY,IAAI,CAAC6Z,gBAAgB;IAE1D,OACEh9B,cAAC+8B,cAAc;MACbh8B,GAAG,EAAEiD,QAAQ;MACbA,QAAQ,EAAEA,QAAQ;MAClBg5B,gBAAgB,EAAEA,gBAAgB;MAClCC,eAAe,EAAEA,eAAe;MAChChJ,cAAc,EAAEA,cAAc;MAC9B3F,OAAO,EAAE,SAAAA;QACPqO,sBAAsB,CAAC34B,QAAQ,CAAC;QAChC6C,UAAU,CAAC;UACTy0B,iBAAiB,CAACt3B,QAAQ,CAAC;SAC5B,EAAE,EAAE,CAAC;;MAER;GAEL,CAAC,CACE;AAEV;AAEA,IAAMkhB,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,CAAC;EAC/Bg+B,GAAG,EAAE;IACH,GAAG,EAAE,kBAAkB;IACvBt+B,OAAO,EAAE,MAAM;IACfwmB,aAAa,EAAE,KAAK;IACpBmK,cAAc,EAAE,cAAc;IAC9ByB,OAAO,EAAE;GACV;EACD,oBAAoB,EAAE;IACpBkM,GAAG,EAAE;MACHr+B,OAAO,EAAE,KAAK;MACd6vB,MAAM,EAAE,SAAS;MACjB5vB,aAAa,EAAE;;GAElB;EACD,8CAA8C,EAAE;IAC9Co+B,GAAG,EAAE;MACHr+B,OAAO,EAAE,KAAK;MACd6vB,MAAM,EAAE,SAAS;MACjB5vB,aAAa,EAAE;;;CAGpB,CAAC;;;;SCzEcq+B,cAAcA;EAC5B,IAAMzf,WAAW,GAAGJ,cAAc,EAAE;EAEpC,OACEje,cAACkvB,MAAM;IACL3sB,SAAS,EAAE0iB,EAAE,CACXC,QAAM,CAAC6Y,cAAc,EACrB19B,uBAAuB,CAACI,mBAAmB,CAC5C;IACD6tB,OAAO,EAAEjQ,WAAW;kBACT,OAAO;IAClBqU,KAAK,EAAC;KAEN1yB;IAAKuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAAC8Y,eAAe;IAAK,CACvC;AAEb;AAEA,IAAMC,SAAS,GAAG;EAChB,QAAQ,EAAE;IACR,yBAAyB,EAAE;MACzB19B,mBAAmB,EAAE;;;CAG1B;AAED,IAAM2kB,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,eAAAC,QAAA;EAC9Bi+B,cAAc,EAAE;IACd,GAAG,EAAE,sBAAsB;IAC3BjY,QAAQ,EAAE,UAAU;IACpBwK,KAAK,EAAE,qCAAqC;IAC5C1vB,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbtB,OAAO,EAAE,MAAM;IACf0wB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBnX,GAAG,EAAE,KAAK;IACV0X,SAAS,EAAE,kBAAkB;IAC7BkB,OAAO,EAAE,GAAG;IACZzL,YAAY,EAAE,KAAK;IACnB,QAAQ,EAAE;MACRqJ,UAAU,EAAE;KACb;IACD,QAAQ,EAAE;MACRA,UAAU,EAAE;;GAEf;EACDyO,eAAe,EAAE;IACf,GAAG,EAAE,sBAAsB;IAC3B5X,eAAe,EAAE,aAAa;IAC9B4M,gBAAgB,EAAE,WAAW;IAC7BC,cAAc,EAAE,MAAM;IACtBryB,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbiyB,eAAe,WAASoL,QAAQ,MAAG;IACnC,QAAQ,EAAE;MACR39B,mBAAmB,EAAE;KACtB;IACD,QAAQ,EAAE;MACRA,mBAAmB,EAAE;;;AAExB,gBACEO,QAAQ,CAAC,iBAAiB,EAAE;EAC7BP,mBAAmB,EAAE;CACtB,CAAC,eACCO,QAAQ,CAAC,gBAAgB,EAAEm9B,SAAS,CAAC,CACzC,CAAC;;AC1EF,IAAME,KAAK,gBAAMx/B,WAAW,CAACD,UAAU,CAACmnB,WAAW,CAAC,sBAAIlnB,WAAW,CACjED,UAAU,CAACw2B,SAAS,CACnB;AAEH,IAAMkJ,YAAY,gBAAG,CAAC,QAAQ,EAAEz/B,WAAW,CAACD,UAAU,CAACsI,KAAK,CAAC,CAAC,CAAC7H,IAAI,CAAC,EAAE,CAAC;AACvE,IAAMk/B,QAAQ,gBAAG1/B,WAAW,CAACD,UAAU,CAACsF,QAAQ,CAAC;AAEjD,SAAgBs6B,SAASA,CAAAh4B,IAAA;MAAGtF,KAAK,GAAAsF,IAAA,CAALtF,KAAK;EAC/B,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;;EAGb,IAAMu9B,CAAC,GAAGC,QAAQ,CAACx9B,KAAK,CAAC;EAEzB,OACEhB,wCACEm+B,KAAK,SAAIC,YAAY,iDAKrBD,KAAK,SAAII,CAAC,+CAIVJ,KAAK,SAAIE,QAAQ,kBAAaE,CAAC,0CAGlC,CAAS;AAEZ;AAEA,SAASC,QAAQA,CAACx9B,KAAa;EAC7B,OAAO,CACLo9B,YAAY,EACZ,oBAAoB,EACpB3f,uBAAuB,CAACzd,KAAK,CAAC,EAC9B,IAAI,CACL,CAAC7B,IAAI,CAAC,EAAE,CAAC;AACZ;;;;SCrCgBs/B,SAASA;EACvB,OAAOz+B;IAAKuC,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACwZ,SAAS;IAAK;AACjD;AAEA,IAAMxZ,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,eAAAC,QAAA;EAC9B4+B,SAAS,EAAE;IACT,GAAG,EAAE,gBAAgB;IACrBrO,OAAO,EAAE,EAAE;IACXvK,QAAQ,EAAE,UAAU;IACpB/M,GAAG,EAAE,KAAK;IACVgO,IAAI,EAAE,qCAAqC;IAC3C0J,SAAS,EAAE,kBAAkB;IAC7B5vB,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdoyB,gBAAgB,EAAE,WAAW;IAC7B6D,kBAAkB,EAAE,KAAK;IACzB5D,cAAc,EAAE,MAAM;IACtBH,eAAe,WAAS6L,YAAY;;AACrC,gBACE79B,QAAQ,CAAC,WAAW,EAAE;EACvBP,mBAAmB,EAAE;CACtB,CAAC,CACH,CAAC;;SCNcq+B,eAAeA;EAC7B,IAAMn8B,cAAc,GAAGgT,uBAAuB,EAAE;EAEhD,IAAMwM,kBAAkB,GAAGlB,qBAAqB,EAAE;EAElD,IAAIte,cAAc,EAAE;IAClB,OAAO,IAAI;;EAGb,OACEzC,cAACy4B,IAAI;IAACl2B,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAAC2Z,OAAO;KAChC7+B,cAAC8+B,MAAM,OAAG,EAET7c,kBAAkB,GAAGjiB,cAACy5B,cAAc,OAAG,GAAG,IAAI,CAC1C;AAEX;AAEA,SAAgBqF,MAAMA;EACpB,IAAAlyB,SAAA,GAAsBC,QAAQ,CAAC,CAAC,CAAC;IAA1BkyB,GAAG,GAAAnyB,SAAA;IAAEoyB,MAAM,GAAApyB,SAAA;EAClB,IAAM6P,mBAAmB,GAAGN,sBAAsB,EAAE;EACpD,IAAMvE,cAAc,GAAGa,iBAAiB,EAAE;EAC1C,IAAMwmB,WAAW,GAAG1sB,0BAA0B,EAAE;EAChD,IAAM2sB,SAAS,GAAGhsB,wBAAwB,EAAE;EAC5C,IAAAisB,UAAA,GAAsDzgB,SAAS,EAAE;IAAzDE,mBAAmB,GAAAugB,UAAA,CAAnBvgB,mBAAmB;IAAErP,UAAU,GAAA4vB,UAAA,CAAV5vB,UAAU;IAAEuP,SAAQ,GAAAqgB,UAAA,CAARrgB,QAAQ;EAEjD,IAAMsgB,KAAK,GAAGxnB,cAAc,oBAAdA,cAAc,CAAEvK,OAAO;EACrC,IAAMrM,KAAK,GAAGo+B,KAAK,oBAALA,KAAK,CAAEp+B,KAAK;EAE1B,OACEhB,cAACg5B,QAAQ;IAACz2B,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACma,eAAe;KAC5Cr/B,cAACs+B,SAAS;IAACt9B,KAAK,EAAEA;IAAS,EAC3BhB;;IAEEk/B,SAAS,EAAEA,SAAS;kBACR,6BAA6B;IACzC3a,OAAO,EAAE9H,mBAAmB;IAC5Bla,SAAS,EAAE0iB,EAAE,CAACC,QAAM,CAACoa,MAAM,CAAC;IAC5BlQ,IAAI,EAAC,MAAM;qBACG,eAAe;IAC7B6P,WAAW,EAAEA,WAAW;IACxBngB,QAAQ,EAAE,SAAAA,SAAA4C,KAAK;MACbsd,MAAM,CAACD,GAAG,GAAG,CAAC,CAAC;MACfl4B,UAAU,CAAC;;QACTiY,SAAQ,EAAAygB,mBAAA,GAAC7d,KAAK,qBAAA8d,aAAA,GAAL9d,KAAK,CAAEpJ,MAAM,qBAAbknB,aAAA,CAAex+B,KAAK,YAAAu+B,mBAAA,GAAIv+B,KAAK,CAAC;OACxC,CAAC;KACH;IACD2kB,GAAG,EAAE/N;IACL,EACDrI,UAAU,GACTvP;IACEm9B,IAAI,EAAC,QAAQ;IACb56B,SAAS,EAAE0iB,EAAE,CAAC,2BAA2B,EAAEC,QAAM,CAACua,cAAc,CAAC;iBACvD,QAAQ;IAClB71B,EAAE,EAAC,eAAe;mBACN;KAEXgV,mBAAmB,CAChB,GACJ,IAAI,EACR5e,cAACy+B,SAAS,OAAG,EACbz+B,cAAC89B,cAAc,OAAG,CACT;AAEf;AAEA,IAAM5Y,QAAM,gBAAG9lB,UAAU,CAACS,MAAM,eAAAC,QAAA;EAC9B++B,OAAO,EAAE;IACPlN,OAAO,EAAE,2BAA2B;IACpChB,MAAM,EAAE;GACT;EACD0O,eAAe,EAAE;IACf,GAAG,EAAE,sBAAsB;IAC3BnI,IAAI,EAAE,GAAG;IACT33B,OAAO,EAAE,OAAO;IAChBkyB,QAAQ,EAAE;GACX;EACDgO,cAAc,EAAE;IACdC,IAAI,EAAE,eAAe;IACrBC,QAAQ,EAAE,YAAY;IACtB/+B,MAAM,EAAE,KAAK;IACbjB,QAAQ,EAAE,QAAQ;IAClBmmB,QAAQ,EAAE,UAAU;IACpB8Z,UAAU,EAAE,QAAQ;IACpB/+B,KAAK,EAAE;GACR;EACDy+B,MAAM,EAAE;IACN9P,OAAO,EAAE,MAAM;IACf7uB,UAAU,EAAE,sBAAsB;IAClCkyB,KAAK,EAAE,oCAAoC;IAC3C3M,YAAY,EAAE,uCAAuC;IACrDyL,OAAO,EAAE,iCAAiC;IAC1C/wB,MAAM,EAAE,gCAAgC;IACxCwlB,eAAe,EAAE,kCAAkC;IACnDkJ,MAAM,EAAE,4CAA4C;IACpDzuB,KAAK,EAAE,MAAM;IACb,QAAQ,EAAE;MACRulB,eAAe,EAAE,yCAAyC;MAC1DkJ,MAAM,EAAE;KACT;IACD,eAAe,EAAE;MACfuD,KAAK,EAAE;;GAEV;EAEDkL,cAAc,EAAE;IACd,GAAG,EAAE,sBAAsB;IAC3BjY,QAAQ,EAAE,UAAU;IACpBwK,KAAK,EAAE,qCAAqC;IAC5C1vB,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbtB,OAAO,EAAE,MAAM;IACf0wB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBnX,GAAG,EAAE,KAAK;IACV0X,SAAS,EAAE,kBAAkB;IAC7BkB,OAAO,EAAE,GAAG;IACZzL,YAAY,EAAE,KAAK;IACnB,QAAQ,EAAE;MACRqJ,UAAU,EAAE;KACb;IACD,QAAQ,EAAE;MACRA,UAAU,EAAE;;GAEf;EACDyO,eAAe,EAAE;IACf,GAAG,EAAE,sBAAsB;IAC3B5X,eAAe,EAAE,aAAa;IAC9B4M,gBAAgB,EAAE,WAAW;IAC7BC,cAAc,EAAE,MAAM;IACtBryB,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbiyB,eAAe,WAASoL,QAAQ,MAAG;IACnC,QAAQ,EAAE;MACR39B,mBAAmB,EAAE;KACtB;IACD,QAAQ,EAAE;MACRA,mBAAmB,EAAE;;;AAExB,gBACEO,QAAQ,CAAC,iBAAiB,EAAE;EAC7BP,mBAAmB,EAAE;CACtB,CAAC,eACCO,QAAQ,CAAC,gBAAgB,EAAE;EAC5B,gCAAgC,EAAE;IAChCP,mBAAmB,EAAE;;CAExB,CAAC,CACH,CAAC;;SClKcs/B,MAAMA;EACpB,OACE7/B,cAACg5B,QAAQ;IACPz2B,SAAS,EAAE0iB,EAAE,CAAC,YAAY,EAAE5kB,uBAAuB,CAACK,iBAAiB;KAErEV,cAAC4+B,eAAe,OAAG,EACnB5+B,cAACy9B,kBAAkB,OAAG,CACb;AAEf;;ACFA,SAASqC,WAAWA,CAAC3Q,KAAkB;EACrC,OACEnvB,cAACwX,yBAAyB,QACxBxX,cAACD,cAAc,OAAG,EAClBC,cAAC+L,oBAAoB,oBAAKojB,KAAK,GAC7BnvB,cAAC+/B,cAAc,OAAG,CACG,CACG;AAEhC;AAEA,SAASA,cAAcA;EACrB,IAAAlsB,qBAAA,GAA+BrD,qBAAqB,EAAE;IAA/C3O,oBAAoB,GAAAgS,qBAAA;EAC3B,IAAMnI,oBAAoB,GAAGkH,uBAAuB,EAAE;EAEtD,IAAAtG,eAAA,GAAkCtM,QAAc,CAAC,CAAC6B,oBAAoB,CAAC;IAAhEm+B,SAAS,GAAA1zB,eAAA;IAAE2zB,YAAY,GAAA3zB,eAAA;EAC9B,IAAM+V,MAAM,GAAG7O,aAAa,EAAE;EAE9BxT,SAAe,CAAC;IACd,IAAI6B,oBAAoB,IAAI,CAAC6J,oBAAoB,EAAE;MACjD;;IAGF,IAAI,CAACs0B,SAAS,EAAE;MACdC,YAAY,CAAC,IAAI,CAAC;;GAErB,EAAE,CAACD,SAAS,EAAEt0B,oBAAoB,EAAE7J,oBAAoB,CAAC,CAAC;EAE3D,IAAI,CAACwgB,MAAM,EAAE;IACX,OAAO,IAAI;;EAGb,OACEriB,cAAC0kB,UAAU,QACT1kB,cAACkzB,SAAS,OAAG,EACblzB,cAACkgC,qBAAqB;IAACF,SAAS,EAAEA;IAAa,CACpC;AAEjB;AAEA,SAASE,qBAAqBA,CAAA55B,IAAA;MAAG05B,SAAS,GAAA15B,IAAA,CAAT05B,SAAS;EACxC,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,IAAI;;EAGb,OACEhgC,8BACEA,cAAC6/B,MAAM,OAAG,EACV7/B,cAAC+2B,IAAI,OAAG,EACR/2B,cAACs6B,OAAO,OAAG,CACV;AAEP;AAEA;AACA,oCAAet6B,IAAU,CAAC8/B,WAAW,EAAE3+B,aAAa,CAAC;;ACvEtB,IAEVg/B,aAAc,0BAAAC,gBAAA;EAAAC,cAAA,CAAAF,aAAA,EAAAC,gBAAA;EAIjC,SAAAD,cAAYhR,KAAoC;;IAC9CmR,KAAA,GAAAF,gBAAA,CAAAG,IAAA,OAAMpR,KAAK,CAAC;IACZmR,KAAA,CAAKxzB,KAAK,GAAG;MAAE0zB,QAAQ,EAAE;KAAO;IAAC,OAAAF,KAAA;;EAClCH,aAAA,CAEMM,wBAAwB,GAA/B,SAAAA;IACE,OAAO;MAAED,QAAQ,EAAE;KAAM;GAC1B;EAAA,IAAAE,MAAA,GAAAP,aAAA,CAAAQ,SAAA;EAAAD,MAAA,CAEDE,iBAAiB,GAAjB,SAAAA,kBAAkBC,KAAY,EAAEC,SAAc;;IAE5CC,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,EAAEC,SAAS,CAAC;GACxE;EAAAJ,MAAA,CAEDM,MAAM,GAAN,SAAAA;IACE,IAAI,IAAI,CAACl0B,KAAK,CAAC0zB,QAAQ,EAAE;MACvB,OAAO,IAAI;;IAGb,OAAO,IAAI,CAACrR,KAAK,CAACnjB,QAAQ;GAC3B;EAAA,OAAAm0B,aAAA;AAAA,EAxBwCngC,SAG1C;;SCEeihC,aAAaA,CAAA36B,IAAA;MAC3B4B,OAAO,GAAA5B,IAAA,CAAP4B,OAAO;IAAAg5B,SAAA,GAAA56B,IAAA,CACP4rB,IAAI;IAAJA,IAAI,GAAAgP,SAAA,cAAG,EAAE,GAAAA,SAAA;IAAAC,eAAA,GAAA76B,IAAA,CACTnE,UAAU;IAAVA,UAAU,GAAAg/B,eAAA,cAAGr+B,UAAU,CAAC4C,KAAK,GAAAy7B,eAAA;IAAAhQ,aAAA,GAAA7qB,IAAA,CAC7B8qB,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAChB/lB,WAAW,GAAA9E,IAAA,CAAX8E,WAAW;IACX4Y,QAAQ,GAAA1d,IAAA,CAAR0d,QAAQ;EASR,IAAI,CAAC9b,OAAO,IAAI,CAAC8b,QAAQ,IAAI,CAAC5Y,WAAW,EAAE;IACzC,OAAO,IAAI;;EAGb,OACEpL,cAACiyB,aAAa;IACZ/pB,OAAO,EAAEA,OAAO;IAChBgqB,IAAI,EAAEA,IAAI;IACV/vB,UAAU,EAAEA,UAAU;IACtBivB,QAAQ,EAAEA,QAAQ;IAClBhmB,WAAW,EAAE4Y,QAAQ,GAAG;MAAA,OAAMA,QAAQ;QAAG5Y;IACzC;AAEN;;SCXwB00B,aAAWA,CAAC3Q,KAAkB;EACpD,IAAMld,gBAAgB,GAAGD,sBAAsB,CAAC;IAC9CE,YAAY,EAAEid,KAAK,CAACjd,YAAY;IAChCE,eAAe,EAAE+c,KAAK,CAAC/c,eAAe;IACtCC,gBAAgB,EAAE8c,KAAK,CAAC9c;GACzB,CAAC;EAEF,OACErS,cAACmgC,aAAa,QACZngC,cAAC2R,oBAAoB,CAACtF,QAAQ;IAACrL,KAAK,EAAEiR;KACpCjS,cAACohC,gBAAgB,oBAAKjS,KAAK,EAAI,CACD,CAClB;AAEpB;;;;;"}