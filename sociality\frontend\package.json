{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "./node_modules/.bin/vite build", "lint": "./node_modules/.bin/eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "./node_modules/.bin/vite preview", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:headless": "cypress run --headless", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:component": "cypress run --component", "test:component:open": "cypress open --component", "test:security": "cypress run --spec 'cypress/e2e/security-final.cy.js'", "test:core-performance": "cypress run --spec 'cypress/e2e/core-performance.cy.js' --headless", "test:performance": "cypress run --spec 'cypress/e2e/performance.cy.js' --headless", "test:performance:all": "cypress run --spec 'cypress/e2e/*performance*.cy.js' --headless", "test:comprehensive": "cypress run --spec 'cypress/e2e/user-workflow.cy.js,cypress/e2e/user-workflow-stress-focused.cy.js,cypress/e2e/core-performance.cy.js' --headless"}, "dependencies": {"@chakra-ui/avatar": "^2.3.0", "@chakra-ui/color-mode": "^2.2.0", "@chakra-ui/icons": "^2.1.0", "@chakra-ui/image": "^2.1.0", "@chakra-ui/layout": "^2.3.1", "@chakra-ui/menu": "^2.2.1", "@chakra-ui/portal": "^2.1.0", "@chakra-ui/react": "^2.7.1", "@chakra-ui/theme-utils": "^2.0.21", "@chakra-ui/toast": "^7.0.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "date-fns": "^2.30.0", "emoji-picker-react": "^4.13.2", "framer-motion": "^10.12.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^4.12.0", "react-router-dom": "^6.14.1", "react-slick": "^0.30.3", "react-window": "^1.8.11", "recoil": "^0.7.7", "slick-carousel": "^1.8.1", "socket.io-client": "^4.7.2"}, "devDependencies": {"@cypress/react": "^9.0.1", "@cypress/vite-dev-server": "^6.0.3", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@vitejs/plugin-react": "^4.0.1", "cypress": "^14.5.1", "eslint": "^8.44.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "vite": "^4.4.0"}}