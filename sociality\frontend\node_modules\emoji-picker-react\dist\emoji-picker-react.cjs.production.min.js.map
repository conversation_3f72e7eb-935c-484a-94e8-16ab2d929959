{"version": 3, "file": "emoji-picker-react.cjs.production.min.js", "sources": ["../src/DomUtils/classNames.ts", "../src/Stylesheet/stylesheet.tsx", "../src/config/compareConfig.ts", "../src/components/Reactions/DEFAULT_REACTIONS.ts", "../src/types/exposedTypes.ts", "../src/config/categoryConfig.ts", "../src/dataUtils/DataTypes.ts", "../src/data/skinToneVariations.ts", "../src/dataUtils/alphaNumericEmojiIndex.ts", "../src/dataUtils/emojiSelectors.ts", "../src/config/cdnUrls.ts", "../src/config/config.ts", "../src/components/context/PickerConfigContext.tsx", "../src/hooks/useDebouncedState.ts", "../src/hooks/useDisallowedEmojis.ts", "../src/config/useConfig.ts", "../src/hooks/useHideEmojisByUniocode.ts", "../src/components/context/PickerContext.tsx", "../src/hooks/useInitialLoad.ts", "../src/config/mutableConfig.ts", "../src/hooks/useIsSearchMode.ts", "../src/DomUtils/focusElement.ts", "../src/DomUtils/getActiveElement.ts", "../src/components/context/ElementRefContext.tsx", "../src/hooks/useKeyboardNavigation.ts", "../src/DomUtils/scrollTo.ts", "../src/DomUtils/selectors.ts", "../src/DomUtils/keyboardNavigation.ts", "../src/hooks/useCloseAllOpenToggles.ts", "../src/hooks/useDisallowMouseMove.ts", "../src/hooks/useFocus.ts", "../src/hooks/useFilter.ts", "../src/hooks/useSetVariationPicker.ts", "../src/hooks/useShouldShowSkinTonePicker.ts", "../src/DomUtils/elementPositionInRow.ts", "../src/hooks/preloadEmoji.ts", "../src/components/main/PickerMain.tsx", "../src/hooks/useOnFocus.ts", "../src/dataUtils/parseNativeEmoji.ts", "../src/dataUtils/suggested.ts", "../src/typeRefinements/typeRefinements.ts", "../src/hooks/useMouseDownHandlers.ts", "../src/components/atoms/Button.tsx", "../src/components/emoji/ClickableEmojiButton.tsx", "../src/components/emoji/emojiStyles.ts", "../src/components/emoji/EmojiImg.tsx", "../src/components/emoji/NativeEmoji.tsx", "../src/components/emoji/ViewOnlyEmoji.tsx", "../src/components/emoji/Emoji.tsx", "../src/components/Reactions/BtnPlus.tsx", "../src/components/Reactions/Reactions.tsx", "../src/components/body/EmojiCategory.tsx", "../src/hooks/useIsEverMounted.ts", "../src/components/body/Suggested.tsx", "../src/components/body/EmojiList.tsx", "../src/hooks/useIsEmojiHidden.ts", "../src/components/body/EmojiVariationPicker.tsx", "../src/components/body/Body.tsx", "../src/hooks/useOnScroll.ts", "../src/components/Layout/Flex.tsx", "../src/components/Layout/Space.tsx", "../src/components/Layout/Absolute.tsx", "../src/components/Layout/Relative.tsx", "../src/components/header/SkinTonePicker/BtnSkinToneVariation.tsx", "../src/components/header/SkinTonePicker/SkinTonePicker.tsx", "../src/components/footer/Preview.tsx", "../src/hooks/useEmojiPreviewEvents.ts", "../src/DomUtils/detectEmojyPartiallyBelowFold.ts", "../src/components/navigation/CategoryButton.tsx", "../src/components/navigation/CategoryNavigation.tsx", "../src/hooks/useScrollCategoryIntoView.ts", "../src/hooks/useActiveCategoryScrollDetection.ts", "../src/DomUtils/categoryNameFromDom.ts", "../src/hooks/useShouldHideCustomEmojis.ts", "../src/components/header/Search/BtnClearSearch.tsx", "../src/components/header/Search/CssSearch.tsx", "../src/components/header/Search/IcnSearch.tsx", "../src/components/header/Search/Search.tsx", "../src/components/header/Header.tsx", "../src/EmojiPickerReact.tsx", "../src/components/ErrorBoundary.tsx", "../src/components/emoji/ExportedEmoji.tsx", "../src/index.tsx"], "sourcesContent": ["export enum ClassNames {\n  hiddenOnSearch = 'epr-hidden-on-search',\n  searchActive = 'epr-search-active',\n  hidden = 'epr-hidden',\n  visible = 'epr-visible',\n  active = 'epr-active',\n  emoji = 'epr-emoji',\n  category = 'epr-emoji-category',\n  label = 'epr-emoji-category-label',\n  categoryContent = 'epr-emoji-category-content',\n  emojiHasVariations = 'epr-emoji-has-variations',\n  scrollBody = 'epr-body',\n  emojiList = 'epr-emoji-list',\n  external = '__EmojiPicker__',\n  emojiPicker = 'EmojiPickerReact',\n  open = 'epr-open',\n  vertical = 'epr-vertical',\n  horizontal = 'epr-horizontal',\n  variationPicker = 'epr-emoji-variation-picker',\n  darkTheme = 'epr-dark-theme',\n  autoTheme = 'epr-auto-theme'\n}\n\nexport function asSelectors(...classNames: ClassNames[]): string {\n  return classNames.map(c => `.${c}`).join('');\n}\n", "import { Styles, createSheet } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../DomUtils/classNames';\n\nexport const stylesheet = createSheet('epr', null);\n\nconst hidden = {\n  display: 'none',\n  opacity: '0',\n  pointerEvents: 'none',\n  visibility: 'hidden',\n  overflow: 'hidden'\n};\n\nexport const commonStyles = stylesheet.create({\n  hidden: {\n    '.': ClassNames.hidden,\n    ...hidden\n  }\n});\n\nexport const PickerStyleTag = React.memo(function PickerStyleTag() {\n  return (\n    <style\n      suppressHydrationWarning\n      dangerouslySetInnerHTML={{ __html: stylesheet.getStyle() }}\n    />\n  );\n});\n\nexport const commonInteractionStyles = stylesheet.create({\n  '.epr-main': {\n    ':has(input:not(:placeholder-shown))': {\n      categoryBtn: {\n        ':hover': {\n          opacity: '1',\n          backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n        }\n      },\n      hiddenOnSearch: {\n        '.': ClassNames.hiddenOnSearch,\n        ...hidden\n      }\n    },\n    ':has(input:placeholder-shown)': {\n      visibleOnSearchOnly: hidden\n    }\n  },\n  hiddenOnReactions: {\n    transition: 'all 0.5s ease-in-out'\n  },\n  '.epr-reactions': {\n    hiddenOnReactions: {\n      height: '0px',\n      width: '0px',\n      opacity: '0',\n      pointerEvents: 'none',\n      overflow: 'hidden'\n    }\n  },\n  '.EmojiPickerReact:not(.epr-search-active)': {\n    categoryBtn: {\n      ':hover': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      },\n      '&.epr-active': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      }\n    },\n    visibleOnSearchOnly: {\n      '.': 'epr-visible-on-search-only',\n      ...hidden\n    }\n  }\n});\n\nexport function darkMode(key: string, value: Styles) {\n  return {\n    '.epr-dark-theme': {\n      [key]: value\n    },\n    '.epr-auto-theme': {\n      [key]: {\n        '@media (prefers-color-scheme: dark)': value\n      }\n    }\n  };\n}\n", "import { PickerConfig } from './config';\n\n// eslint-disable-next-line complexity\nexport function compareConfig(prev: PickerConfig, next: PickerConfig) {\n  const prevCustomEmojis = prev.customEmojis ?? [];\n  const nextCustomEmojis = next.customEmojis ?? [];\n  return (\n    prev.open === next.open &&\n    prev.emojiVersion === next.emojiVersion &&\n    prev.reactionsDefaultOpen === next.reactionsDefaultOpen &&\n    prev.searchPlaceHolder === next.searchPlaceHolder &&\n    prev.searchPlaceholder === next.searchPlaceholder &&\n    prev.defaultSkinTone === next.defaultSkinTone &&\n    prev.skinTonesDisabled === next.skinTonesDisabled &&\n    prev.autoFocusSearch === next.autoFocusSearch &&\n    prev.emojiStyle === next.emojiStyle &&\n    prev.theme === next.theme &&\n    prev.suggestedEmojisMode === next.suggestedEmojisMode &&\n    prev.lazyLoadEmojis === next.lazyLoadEmojis &&\n    prev.className === next.className &&\n    prev.height === next.height &&\n    prev.width === next.width &&\n    prev.style === next.style &&\n    prev.searchDisabled === next.searchDisabled &&\n    prev.skinTonePickerLocation === next.skinTonePickerLocation &&\n    prevCustomEmojis.length === nextCustomEmojis.length\n  );\n}", "export const DEFAULT_REACTIONS = [\n  '1f44d', // 👍\n  '2764-fe0f', // ❤️\n  '1f603', // 😃\n  '1f622', // 😢\n  '1f64f', // 🙏\n  '1f44e', // 👎\n  '1f621' // 😡\n];\n", "export type EmojiClickData = {\n  activeSkinTone: SkinTones;\n  unified: string;\n  unifiedWithoutSkinTone: string;\n  emoji: string;\n  names: string[];\n  imageUrl: string;\n  getImageUrl: (emojiStyle?: EmojiStyle) => string;\n  isCustom: boolean;\n};\n\nexport enum SuggestionMode {\n  RECENT = 'recent',\n  FREQUENT = 'frequent'\n}\n\nexport enum EmojiStyle {\n  NATIVE = 'native',\n  APPLE = 'apple',\n  TWITTER = 'twitter',\n  GOOGLE = 'google',\n  FACEBOOK = 'facebook'\n}\n\nexport enum Theme {\n  DARK = 'dark',\n  LIGHT = 'light',\n  AUTO = 'auto'\n}\n\nexport enum SkinTones {\n  NEUTRAL = 'neutral',\n  LIGHT = '1f3fb',\n  MEDIUM_LIGHT = '1f3fc',\n  MEDIUM = '1f3fd',\n  MEDIUM_DARK = '1f3fe',\n  DARK = '1f3ff'\n}\n\nexport enum Categories {\n  SUGGESTED = 'suggested',\n  CUSTOM = 'custom',\n  SMILEYS_PEOPLE = 'smileys_people',\n  ANIMALS_NATURE = 'animals_nature',\n  FOOD_DRINK = 'food_drink',\n  TRAVEL_PLACES = 'travel_places',\n  ACTIVITIES = 'activities',\n  OBJECTS = 'objects',\n  SYMBOLS = 'symbols',\n  FLAGS = 'flags'\n}\n\nexport enum SkinTonePickerLocation {\n  SEARCH = 'SEARCH',\n  PREVIEW = 'PREVIEW'\n}\n", "import { Categories, SuggestionMode } from '../types/exposedTypes';\n\nexport { Categories };\n\nconst categoriesOrdered: Categories[] = [\n  Categories.SUGGESTED,\n  Categories.CUSTOM,\n  Categories.SMILEYS_PEOPLE,\n  Categories.ANIMALS_NATURE,\n  Categories.FOOD_DRINK,\n  Categories.TRAVEL_PLACES,\n  Categories.ACTIVITIES,\n  Categories.OBJECTS,\n  Categories.SYMBOLS,\n  Categories.FLAGS\n];\n\nexport const SuggestedRecent: CategoryConfig = {\n  name: 'Recently Used',\n  category: Categories.SUGGESTED\n};\n\nexport type CustomCategoryConfig = {\n  category: Categories.CUSTOM;\n  name: string;\n};\n\nconst configByCategory: Record<Categories, CategoryConfig> = {\n  [Categories.SUGGESTED]: {\n    category: Categories.SUGGESTED,\n    name: 'Frequently Used'\n  },\n  [Categories.CUSTOM]: {\n    category: Categories.CUSTOM,\n    name: 'Custom Emojis'\n  },\n  [Categories.SMILEYS_PEOPLE]: {\n    category: Categories.SMILEYS_PEOPLE,\n    name: 'Smileys & People'\n  },\n  [Categories.ANIMALS_NATURE]: {\n    category: Categories.ANIMALS_NATURE,\n    name: 'Animals & Nature'\n  },\n  [Categories.FOOD_DRINK]: {\n    category: Categories.FOOD_DRINK,\n    name: 'Food & Drink'\n  },\n  [Categories.TRAVEL_PLACES]: {\n    category: Categories.TRAVEL_PLACES,\n    name: 'Travel & Places'\n  },\n  [Categories.ACTIVITIES]: {\n    category: Categories.ACTIVITIES,\n    name: 'Activities'\n  },\n  [Categories.OBJECTS]: {\n    category: Categories.OBJECTS,\n    name: 'Objects'\n  },\n  [Categories.SYMBOLS]: {\n    category: Categories.SYMBOLS,\n    name: 'Symbols'\n  },\n  [Categories.FLAGS]: {\n    category: Categories.FLAGS,\n    name: 'Flags'\n  }\n};\n\nexport function baseCategoriesConfig(\n  modifiers?: Record<Categories, CategoryConfig>\n): CategoriesConfig {\n  return categoriesOrdered.map(category => {\n    return {\n      ...configByCategory[category],\n      ...(modifiers && modifiers[category] && modifiers[category])\n    };\n  });\n}\n\nexport function categoryFromCategoryConfig(category: CategoryConfig) {\n  return category.category;\n}\n\nexport function categoryNameFromCategoryConfig(category: CategoryConfig) {\n  return category.name;\n}\n\nexport type CategoriesConfig = CategoryConfig[];\n\nexport type CategoryConfig = {\n  category: Categories;\n  name: string;\n};\n\nexport type UserCategoryConfig = Array<Categories | CategoryConfig>;\n\nexport function mergeCategoriesConfig(\n  userCategoriesConfig: UserCategoryConfig = [],\n  modifiers: CategoryConfigModifiers = {}\n): CategoriesConfig {\n  const extra = {} as Record<Categories, CategoryConfig>;\n\n  if (modifiers.suggestionMode === SuggestionMode.RECENT) {\n    extra[Categories.SUGGESTED] = SuggestedRecent;\n  }\n\n  const base = baseCategoriesConfig(extra);\n  if (!userCategoriesConfig?.length) {\n    return base;\n  }\n\n  return userCategoriesConfig.map(category => {\n    if (typeof category === 'string') {\n      return getBaseConfigByCategory(category, extra[category]);\n    }\n\n    return {\n      ...getBaseConfigByCategory(category.category, extra[category.category]),\n      ...category\n    };\n  });\n}\n\nfunction getBaseConfigByCategory(\n  category: Categories,\n  modifier: CategoryConfig = {} as CategoryConfig\n) {\n  return Object.assign(configByCategory[category], modifier);\n}\n\ntype CategoryConfigModifiers = {\n  suggestionMode?: SuggestionMode;\n};\n", "import emojis from '../data/emojis';\n\nexport enum EmojiProperties {\n  name = 'n',\n  unified = 'u',\n  variations = 'v',\n  added_in = 'a',\n  imgUrl = 'imgUrl'\n}\n\nexport interface DataEmoji extends WithName {\n  [EmojiProperties.unified]: string;\n  [EmojiProperties.variations]?: string[];\n  [EmojiProperties.added_in]: string;\n  [EmojiProperties.imgUrl]?: string;\n}\n\nexport type DataEmojis = DataEmoji[];\n\nexport type DataGroups = keyof typeof emojis;\n\nexport type WithName = {\n  [EmojiProperties.name]: string[];\n};\n", "import { SkinTones } from '../types/exposedTypes';\n\nconst skinToneVariations = [\n  SkinTones.NEUTRAL,\n  SkinTones.LIGHT,\n  SkinTones.MEDIUM_LIGHT,\n  SkinTones.MEDIUM,\n  SkinTones.MEDIUM_DARK,\n  SkinTones.DARK\n];\n\nexport const skinTonesNamed = Object.entries(SkinTones).reduce(\n  (acc, [key, value]) => {\n    acc[value] = key;\n    return acc;\n  },\n  {} as Record<string, string>\n);\n\nexport const skinTonesMapped: Record<\n  string,\n  string\n> = skinToneVariations.reduce(\n  (mapped, skinTone) =>\n    Object.assign(mapped, {\n      [skinTone]: skinTone\n    }),\n  {}\n);\n\nexport default skinToneVariations;\n", "import { DataEmoji } from './DataTypes';\nimport { allEmojis, emojiNames, emojiUnified } from './emojiSelectors';\n\nexport const alphaNumericEmojiIndex: BaseIndex = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((searchIndex, emoji) => {\n    indexEmoji(emoji);\n    return searchIndex;\n  }, alphaNumericEmojiIndex as BaseIndex);\n});\n\ntype BaseIndex = Record<string, Record<string, DataEmoji>>;\n\nexport function indexEmoji(emoji: DataEmoji): void {\n  const joinedNameString = emojiNames(emoji)\n    .flat()\n    .join('')\n    .toLowerCase()\n    .replace(/[^a-zA-Z\\d]/g, '')\n    .split('');\n\n  joinedNameString.forEach(char => {\n    alphaNumericEmojiIndex[char] = alphaNumericEmojiIndex[char] ?? {};\n\n    alphaNumericEmojiIndex[char][emojiUnified(emoji)] = emoji;\n  });\n}\n", "import { Categories } from '../config/categoryConfig';\nimport { cdnUrl } from '../config/cdnUrls';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport emojis from '../data/emojis';\nimport skinToneVariations, {\n  skinTonesMapped\n} from '../data/skinToneVariations';\nimport { EmojiStyle, SkinTones } from '../types/exposedTypes';\n\nimport { DataEmoji, DataEmojis, EmojiProperties, WithName } from './DataTypes';\nimport { indexEmoji } from './alphaNumericEmojiIndex';\n\nexport function emojiNames(emoji: WithName): string[] {\n  return emoji[EmojiProperties.name] ?? [];\n}\n\nexport function addedIn(emoji: DataEmoji): number {\n  return parseFloat(emoji[EmojiProperties.added_in]);\n}\n\nexport function emojiName(emoji?: WithName): string {\n  if (!emoji) {\n    return '';\n  }\n\n  return emojiNames(emoji)[0];\n}\n\nexport function unifiedWithoutSkinTone(unified: string): string {\n  const splat = unified.split('-');\n  const [skinTone] = splat.splice(1, 1);\n\n  if (skinTonesMapped[skinTone]) {\n    return splat.join('-');\n  }\n\n  return unified;\n}\n\nexport function emojiUnified(emoji: DataEmoji, skinTone?: string): string {\n  const unified = emoji[EmojiProperties.unified];\n\n  if (!skinTone || !emojiHasVariations(emoji)) {\n    return unified;\n  }\n\n  return emojiVariationUnified(emoji, skinTone) ?? unified;\n}\n\nexport function emojisByCategory(category: Categories): DataEmojis {\n  // @ts-ignore\n  return emojis?.[category] ?? [];\n}\n\n// WARNING: DO NOT USE DIRECTLY\nexport function emojiUrlByUnified(\n  unified: string,\n  emojiStyle: EmojiStyle\n): string {\n  return `${cdnUrl(emojiStyle)}${unified}.png`;\n}\n\nexport function emojiVariations(emoji: DataEmoji): string[] {\n  return emoji[EmojiProperties.variations] ?? [];\n}\n\nexport function emojiHasVariations(emoji: DataEmoji): boolean {\n  return emojiVariations(emoji).length > 0;\n}\n\nexport function emojiVariationUnified(\n  emoji: DataEmoji,\n  skinTone?: string\n): string | undefined {\n  return skinTone\n    ? emojiVariations(emoji).find(variation => variation.includes(skinTone))\n    : emojiUnified(emoji);\n}\n\nexport function emojiByUnified(unified?: string): DataEmoji | undefined {\n  if (!unified) {\n    return;\n  }\n\n  if (allEmojisByUnified[unified]) {\n    return allEmojisByUnified[unified];\n  }\n\n  const withoutSkinTone = unifiedWithoutSkinTone(unified);\n  return allEmojisByUnified[withoutSkinTone];\n}\n\nexport const allEmojis: DataEmojis = Object.values(emojis).flat();\n\nexport function setCustomEmojis(customEmojis: CustomEmoji[]): void {\n  emojis[Categories.CUSTOM].length = 0;\n\n  customEmojis.forEach(emoji => {\n    const emojiData = customToRegularEmoji(emoji);\n\n    emojis[Categories.CUSTOM].push(emojiData as never);\n\n    if (allEmojisByUnified[emojiData[EmojiProperties.unified]]) {\n      return;\n    }\n\n    allEmojis.push(emojiData);\n    allEmojisByUnified[emojiData[EmojiProperties.unified]] = emojiData;\n    indexEmoji(emojiData);\n  });\n}\n\nfunction customToRegularEmoji(emoji: CustomEmoji): DataEmoji {\n  return {\n    [EmojiProperties.name]: emoji.names.map(name => name.toLowerCase()),\n    [EmojiProperties.unified]: emoji.id.toLowerCase(),\n    [EmojiProperties.added_in]: '0',\n    [EmojiProperties.imgUrl]: emoji.imgUrl\n  };\n}\n\nconst allEmojisByUnified: {\n  [unified: string]: DataEmoji;\n} = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((allEmojis, Emoji) => {\n    allEmojis[emojiUnified(Emoji)] = Emoji;\n\n    if (emojiHasVariations(Emoji)) {\n      emojiVariations(Emoji).forEach(variation => {\n        allEmojis[variation] = Emoji;\n      });\n    }\n\n    return allEmojis;\n  }, allEmojisByUnified);\n});\n\nexport function activeVariationFromUnified(unified: string): SkinTones | null {\n  const [, suspectedSkinTone] = unified.split('-') as [string, SkinTones];\n  return skinToneVariations.includes(suspectedSkinTone)\n    ? suspectedSkinTone\n    : null;\n}\n", "import { EmojiStyle } from '../types/exposedTypes';\n\nconst CDN_URL_APPLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/';\nconst CDN_URL_FACEBOOK =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-facebook/img/facebook/64/';\nconst CDN_URL_TWITTER =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-twitter/img/twitter/64/';\nconst CDN_URL_GOOGLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-google/img/google/64/';\n\nexport function cdnUrl(emojiStyle: EmojiStyle): string {\n  switch (emojiStyle) {\n    case EmojiStyle.TWITTER:\n      return CDN_URL_TWITTER;\n    case EmojiStyle.GOOGLE:\n      return CDN_URL_GOOGLE;\n    case EmojiStyle.FACEBOOK:\n      return CDN_URL_FACEBOOK;\n    case EmojiStyle.APPLE:\n    default:\n      return CDN_URL_APPLE;\n  }\n}\n", "import * as React from 'react';\n\nimport { DEFAULT_REACTIONS } from '../components/Reactions/DEFAULT_REACTIONS';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  setCustomEmojis,\n  emojiUrlByUnified,\n} from '../dataUtils/emojiSelectors';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme,\n} from '../types/exposedTypes';\n\nimport {\n  CategoriesConfig,\n  baseCategoriesConfig,\n  mergeCategoriesConfig,\n} from './categoryConfig';\nimport { CustomEmoji } from './customEmojiConfig';\n\nconst KNOWN_FAILING_EMOJIS = ['2640-fe0f', '2642-fe0f', '2695-fe0f'];\n\nexport const DEFAULT_SEARCH_PLACEHOLDER = 'Search';\nexport const SEARCH_RESULTS_NO_RESULTS_FOUND = 'No results found';\nexport const SEARCH_RESULTS_SUFFIX =\n  ' found. Use up and down arrow keys to navigate.';\nexport const SEARCH_RESULTS_ONE_RESULT_FOUND =\n  '1 result' + SEARCH_RESULTS_SUFFIX;\nexport const SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND =\n  '%n results' + SEARCH_RESULTS_SUFFIX;\n\nexport function mergeConfig(\n  userConfig: PickerConfig = {}\n): PickerConfigInternal {\n  const base = basePickerConfig();\n\n  const previewConfig = Object.assign(\n    base.previewConfig,\n    userConfig.previewConfig ?? {}\n  );\n  const config = Object.assign(base, userConfig);\n\n  const categories = mergeCategoriesConfig(userConfig.categories, {\n    suggestionMode: config.suggestedEmojisMode,\n  });\n\n  config.hiddenEmojis.forEach((emoji) => {\n    config.unicodeToHide.add(emoji);\n  });\n\n  setCustomEmojis(config.customEmojis ?? []);\n\n  const skinTonePickerLocation = config.searchDisabled\n    ? SkinTonePickerLocation.PREVIEW\n    : config.skinTonePickerLocation;\n\n  return {\n    ...config,\n    categories,\n    previewConfig,\n    skinTonePickerLocation,\n  };\n}\n\nexport function basePickerConfig(): PickerConfigInternal {\n  return {\n    autoFocusSearch: true,\n    categories: baseCategoriesConfig(),\n    className: '',\n    customEmojis: [],\n    defaultSkinTone: SkinTones.NEUTRAL,\n    emojiStyle: EmojiStyle.APPLE,\n    emojiVersion: null,\n    getEmojiUrl: emojiUrlByUnified,\n    height: 450,\n    lazyLoadEmojis: false,\n    previewConfig: {\n      ...basePreviewConfig,\n    },\n    searchDisabled: false,\n    searchPlaceHolder: DEFAULT_SEARCH_PLACEHOLDER,\n    searchPlaceholder: DEFAULT_SEARCH_PLACEHOLDER,\n    skinTonePickerLocation: SkinTonePickerLocation.SEARCH,\n    skinTonesDisabled: false,\n    style: {},\n    suggestedEmojisMode: SuggestionMode.FREQUENT,\n    theme: Theme.LIGHT,\n    unicodeToHide: new Set<string>(KNOWN_FAILING_EMOJIS),\n    width: 350,\n    reactionsDefaultOpen: false,\n    reactions: DEFAULT_REACTIONS,\n    open: true,\n    allowExpandReactions: true,\n    hiddenEmojis: [],\n  };\n}\n\nexport type PickerConfigInternal = {\n  emojiVersion: string | null;\n  searchPlaceHolder: string;\n  searchPlaceholder: string;\n  defaultSkinTone: SkinTones;\n  skinTonesDisabled: boolean;\n  autoFocusSearch: boolean;\n  emojiStyle: EmojiStyle;\n  categories: CategoriesConfig;\n  theme: Theme;\n  suggestedEmojisMode: SuggestionMode;\n  lazyLoadEmojis: boolean;\n  previewConfig: PreviewConfig;\n  className: string;\n  height: PickerDimensions;\n  width: PickerDimensions;\n  style: React.CSSProperties;\n  getEmojiUrl: GetEmojiUrl;\n  searchDisabled: boolean;\n  skinTonePickerLocation: SkinTonePickerLocation;\n  unicodeToHide: Set<string>;\n  customEmojis: CustomEmoji[];\n  reactionsDefaultOpen: boolean;\n  reactions: string[];\n  open: boolean;\n  allowExpandReactions: boolean;\n  hiddenEmojis: string[];\n};\n\nexport type PreviewConfig = {\n  defaultEmoji: string;\n  defaultCaption: string;\n  showPreview: boolean;\n};\n\nconst basePreviewConfig: PreviewConfig = {\n  defaultEmoji: '1f60a',\n  defaultCaption: \"What's your mood?\",\n  showPreview: true,\n};\n\ntype ConfigExternal = {\n  previewConfig: Partial<PreviewConfig>;\n  onEmojiClick: MouseDownEvent;\n  onReactionClick: MouseDownEvent;\n  onSkinToneChange: OnSkinToneChange;\n} & Omit<PickerConfigInternal, 'previewConfig' | 'unicodeToHide'>;\n\nexport type PickerConfig = Partial<ConfigExternal>;\n\nexport type PickerDimensions = string | number;\n\nexport type MouseDownEvent = (\n  emoji: EmojiClickData,\n  event: MouseEvent,\n  api?: OnEmojiClickApi\n) => void;\nexport type OnSkinToneChange = (emoji: SkinTones) => void;\n\ntype OnEmojiClickApi = {\n  collapseToReactions: () => void;\n};\n", "import * as React from 'react';\n\nimport { compareConfig } from '../../config/compareConfig';\nimport {\n  basePickerConfig,\n  mergeConfig,\n  PickerConfig,\n  PickerConfigInternal\n} from '../../config/config';\n\ntype Props = PickerConfig &\n  Readonly<{\n    children: React.ReactNode;\n  }>;\n\nconst ConfigContext = React.createContext<PickerConfigInternal>(\n  basePickerConfig()\n);\n\nexport function PickerConfigProvider({ children, ...config }: Props) {\n  const mergedConfig = useSetConfig(config);\n\n  return (\n    <ConfigContext.Provider value={mergedConfig}>\n      {children}\n    </ConfigContext.Provider>\n  );\n}\n\nexport function useSetConfig(config: PickerConfig) {\n  const [mergedConfig, setMergedConfig] = React.useState(() =>\n    mergeConfig(config)\n  );\n\n  React.useEffect(() => {\n    if (compareConfig(mergedConfig, config)) {\n      return;\n    }\n    setMergedConfig(mergeConfig(config));\n    // not gonna...\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n    config.customEmojis?.length,\n    config.open,\n    config.emojiVersion,\n    config.reactionsDefaultOpen,\n    config.searchPlaceHolder,\n    config.searchPlaceholder,\n    config.defaultSkinTone,\n    config.skinTonesDisabled,\n    config.autoFocusSearch,\n    config.emojiStyle,\n    config.theme,\n    config.suggestedEmojisMode,\n    config.lazyLoadEmojis,\n    config.className,\n    config.height,\n    config.width,\n    config.searchDisabled,\n    config.skinTonePickerLocation,\n    config.allowExpandReactions\n  ]);\n\n  return mergedConfig;\n}\n\nexport function usePickerConfig() {\n  return React.useContext(ConfigContext);\n}\n", "import { useRef, useState } from 'react';\n\nexport function useDebouncedState<T>(\n  initialValue: T,\n  delay: number = 0\n): [T, (value: T) => Promise<T>] {\n  const [state, setState] = useState<T>(initialValue);\n  const timer = useRef<number | null>(null);\n\n  function debouncedSetState(value: T) {\n    return new Promise<T>(resolve => {\n      if (timer.current) {\n        clearTimeout(timer.current);\n      }\n\n      timer.current = window?.setTimeout(() => {\n        setState(value);\n        resolve(value);\n      }, delay);\n    });\n  }\n\n  return [state, debouncedSetState];\n}\n", "import { useRef, useMemo } from 'react';\n\nimport { useEmojiVersionConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  addedIn,\n  allEmojis,\n  emojiUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { useIsUnicodeHidden } from './useHideEmojisByUniocode';\n\nexport function useDisallowedEmojis() {\n  const DisallowedEmojisRef = useRef<Record<string, boolean>>({});\n  const emojiVersionConfig = useEmojiVersionConfig();\n\n  return useMemo(() => {\n    const emojiVersion = parseFloat(`${emojiVersionConfig}`);\n\n    if (!emojiVersionConfig || Number.isNaN(emojiVersion)) {\n      return DisallowedEmojisRef.current;\n    }\n\n    return allEmojis.reduce((disallowedEmojis, emoji) => {\n      if (addedInNewerVersion(emoji, emojiVersion)) {\n        disallowedEmojis[emojiUnified(emoji)] = true;\n      }\n\n      return disallowedEmojis;\n    }, DisallowedEmojisRef.current);\n  }, [emojiVersionConfig]);\n}\n\nexport function useIsEmojiDisallowed() {\n  const disallowedEmojis = useDisallowedEmojis();\n  const isUnicodeHidden = useIsUnicodeHidden();\n\n  return function isEmojiDisallowed(emoji: DataEmoji) {\n    const unified = unifiedWithoutSkinTone(emojiUnified(emoji));\n\n    return Boolean(disallowedEmojis[unified] || isUnicodeHidden(unified));\n  };\n}\n\nfunction addedInNewerVersion(\n  emoji: DataEmoji,\n  supportedLevel: number\n): boolean {\n  return addedIn(emoji) > supportedLevel;\n}\n", "import * as React from 'react';\n\nimport { usePickerConfig } from '../components/context/PickerConfigContext';\nimport { useReactionsModeState } from '../components/context/PickerContext';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme\n} from '../types/exposedTypes';\n\nimport { CategoriesConfig } from './categoryConfig';\nimport {\n  DEFAULT_SEARCH_PLACEHOLDER,\n  SEARCH_RESULTS_NO_RESULTS_FOUND,\n  SEARCH_RESULTS_ONE_RESULT_FOUND,\n  SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND,\n  PickerDimensions,\n  PreviewConfig\n} from './config';\nimport { CustomEmoji } from './customEmojiConfig';\nimport { useMutableConfig } from './mutableConfig';\n\nexport enum MOUSE_EVENT_SOURCE {\n  REACTIONS = 'reactions',\n  PICKER = 'picker'\n}\n\nexport function useSearchPlaceHolderConfig(): string {\n  const { searchPlaceHolder, searchPlaceholder } = usePickerConfig();\n  return (\n    [searchPlaceHolder, searchPlaceholder].find(\n      p => p !== DEFAULT_SEARCH_PLACEHOLDER\n    ) ?? DEFAULT_SEARCH_PLACEHOLDER\n  );\n}\n\nexport function useDefaultSkinToneConfig(): SkinTones {\n  const { defaultSkinTone } = usePickerConfig();\n  return defaultSkinTone;\n}\n\nexport function useAllowExpandReactions(): boolean {\n  const { allowExpandReactions } = usePickerConfig();\n  return allowExpandReactions;\n}\n\nexport function useSkinTonesDisabledConfig(): boolean {\n  const { skinTonesDisabled } = usePickerConfig();\n  return skinTonesDisabled;\n}\n\nexport function useEmojiStyleConfig(): EmojiStyle {\n  const { emojiStyle } = usePickerConfig();\n  return emojiStyle;\n}\n\nexport function useAutoFocusSearchConfig(): boolean {\n  const { autoFocusSearch } = usePickerConfig();\n  return autoFocusSearch;\n}\n\nexport function useCategoriesConfig(): CategoriesConfig {\n  const { categories } = usePickerConfig();\n  return categories;\n}\n\nexport function useCustomEmojisConfig(): CustomEmoji[] {\n  const { customEmojis } = usePickerConfig();\n  return customEmojis;\n}\n\nexport function useOpenConfig(): boolean {\n  const { open } = usePickerConfig();\n  return open;\n}\n\nexport function useOnEmojiClickConfig(\n  mouseEventSource: MOUSE_EVENT_SOURCE\n): (emoji: EmojiClickData, event: MouseEvent) => void {\n  const { current } = useMutableConfig();\n  const [, setReactionsOpen] = useReactionsModeState();\n\n  const handler = current.onEmojiClick || (() => {});\n  const { onReactionClick } = current;\n\n  if (mouseEventSource === MOUSE_EVENT_SOURCE.REACTIONS && onReactionClick) {\n    return (...args) =>\n      onReactionClick(...args, {\n        collapseToReactions: () => {\n          setReactionsOpen(o => o);\n        }\n      });\n  }\n\n  return (...args) => {\n    handler(...args, {\n      collapseToReactions: () => {\n        setReactionsOpen(true);\n      }\n    });\n  };\n}\n\nexport function useOnSkinToneChangeConfig(): (skinTone: SkinTones) => void {\n  const { current } = useMutableConfig();\n\n  return current.onSkinToneChange || (() => {});\n}\n\nexport function usePreviewConfig(): PreviewConfig {\n  const { previewConfig } = usePickerConfig();\n  return previewConfig;\n}\n\nexport function useThemeConfig(): Theme {\n  const { theme } = usePickerConfig();\n\n  return theme;\n}\n\nexport function useSuggestedEmojisModeConfig(): SuggestionMode {\n  const { suggestedEmojisMode } = usePickerConfig();\n  return suggestedEmojisMode;\n}\n\nexport function useLazyLoadEmojisConfig(): boolean {\n  const { lazyLoadEmojis } = usePickerConfig();\n  return lazyLoadEmojis;\n}\n\nexport function useClassNameConfig(): string {\n  const { className } = usePickerConfig();\n  return className;\n}\n\nexport function useStyleConfig(): React.CSSProperties {\n  const { height, width, style } = usePickerConfig();\n  return { height: getDimension(height), width: getDimension(width), ...style };\n}\n\nexport function useReactionsOpenConfig(): boolean {\n  const { reactionsDefaultOpen } = usePickerConfig();\n  return reactionsDefaultOpen;\n}\n\nexport function useEmojiVersionConfig(): string | null {\n  const { emojiVersion } = usePickerConfig();\n  return emojiVersion;\n}\n\nexport function useSearchDisabledConfig(): boolean {\n  const { searchDisabled } = usePickerConfig();\n  return searchDisabled;\n}\n\nexport function useSkinTonePickerLocationConfig(): SkinTonePickerLocation {\n  const { skinTonePickerLocation } = usePickerConfig();\n  return skinTonePickerLocation;\n}\n\nexport function useUnicodeToHide() {\n  const { unicodeToHide } = usePickerConfig();\n  return unicodeToHide;\n}\n\nexport function useReactionsConfig(): string[] {\n  const { reactions } = usePickerConfig();\n  return reactions;\n}\n\nexport function useGetEmojiUrlConfig(): (\n  unified: string,\n  style: EmojiStyle\n) => string {\n  const { getEmojiUrl } = usePickerConfig();\n  return getEmojiUrl;\n}\n\nfunction getDimension(dimensionConfig: PickerDimensions): PickerDimensions {\n  return typeof dimensionConfig === 'number'\n    ? `${dimensionConfig}px`\n    : dimensionConfig;\n}\n\nexport function useSearchResultsConfig(searchResultsCount: number): string {\n  const hasResults = searchResultsCount > 0;\n  const isPlural = searchResultsCount > 1;\n\n  if (hasResults) {\n    return isPlural\n      ? SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND.replace(\n          '%n',\n          searchResultsCount.toString()\n        )\n      : SEARCH_RESULTS_ONE_RESULT_FOUND;\n  }\n\n  return SEARCH_RESULTS_NO_RESULTS_FOUND;\n}\n", "import { useUnicodeToHide } from \"../config/useConfig\";\n\nexport function useIsUnicodeHidden() {\n    const unicodeToHide = useUnicodeToHide();\n    return (emojiUnified: string) => unicodeToHide.has(emojiUnified);\n  }\n", "import * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  useDefaultSkinToneConfig,\n  useReactionsOpenConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { alphaNumericEmojiIndex } from '../../dataUtils/alphaNumericEmojiIndex';\nimport { useDebouncedState } from '../../hooks/useDebouncedState';\nimport { useDisallowedEmojis } from '../../hooks/useDisallowedEmojis';\nimport { FilterDict } from '../../hooks/useFilter';\nimport { useMarkInitialLoad } from '../../hooks/useInitialLoad';\nimport { SkinTones } from '../../types/exposedTypes';\n\nexport function PickerContextProvider({ children }: Props) {\n  const disallowedEmojis = useDisallowedEmojis();\n  const defaultSkinTone = useDefaultSkinToneConfig();\n  const reactionsDefaultOpen = useReactionsOpenConfig();\n\n  // Initialize the filter with the inititial dictionary\n  const filterRef = React.useRef<FilterState>(alphaNumericEmojiIndex);\n  const disallowClickRef = React.useRef<boolean>(false);\n  const disallowMouseRef = React.useRef<boolean>(false);\n  const disallowedEmojisRef = React.useRef<Record<string, boolean>>(\n    disallowedEmojis\n  );\n\n  const suggestedUpdateState = useDebouncedState(Date.now(), 200);\n  const searchTerm = useDebouncedState('', 100);\n  const skinToneFanOpenState = useState<boolean>(false);\n  const activeSkinTone = useState<SkinTones>(defaultSkinTone);\n  const activeCategoryState = useState<ActiveCategoryState>(null);\n  const emojisThatFailedToLoadState = useState<Set<string>>(new Set());\n  const emojiVariationPickerState = useState<DataEmoji | null>(null);\n  const reactionsModeState = useState(reactionsDefaultOpen);\n  const [isPastInitialLoad, setIsPastInitialLoad] = useState(false);\n\n  useMarkInitialLoad(setIsPastInitialLoad);\n\n  return (\n    <PickerContext.Provider\n      value={{\n        activeCategoryState,\n        activeSkinTone,\n        disallowClickRef,\n        disallowMouseRef,\n        disallowedEmojisRef,\n        emojiVariationPickerState,\n        emojisThatFailedToLoadState,\n        filterRef,\n        isPastInitialLoad,\n        searchTerm,\n        skinToneFanOpenState,\n        suggestedUpdateState,\n        reactionsModeState\n      }}\n    >\n      {children}\n    </PickerContext.Provider>\n  );\n}\n\ntype ReactState<T> = [T, React.Dispatch<React.SetStateAction<T>>];\n\nconst PickerContext = React.createContext<{\n  searchTerm: [string, (term: string) => Promise<string>];\n  suggestedUpdateState: [number, (term: number) => void];\n  activeCategoryState: ReactState<ActiveCategoryState>;\n  activeSkinTone: ReactState<SkinTones>;\n  emojisThatFailedToLoadState: ReactState<Set<string>>;\n  isPastInitialLoad: boolean;\n  emojiVariationPickerState: ReactState<DataEmoji | null>;\n  skinToneFanOpenState: ReactState<boolean>;\n  filterRef: React.MutableRefObject<FilterState>;\n  disallowClickRef: React.MutableRefObject<boolean>;\n  disallowMouseRef: React.MutableRefObject<boolean>;\n  disallowedEmojisRef: React.MutableRefObject<Record<string, boolean>>;\n  reactionsModeState: ReactState<boolean>;\n}>({\n  activeCategoryState: [null, () => {}],\n  activeSkinTone: [SkinTones.NEUTRAL, () => {}],\n  disallowClickRef: { current: false },\n  disallowMouseRef: { current: false },\n  disallowedEmojisRef: { current: {} },\n  emojiVariationPickerState: [null, () => {}],\n  emojisThatFailedToLoadState: [new Set(), () => {}],\n  filterRef: { current: {} },\n  isPastInitialLoad: true,\n  searchTerm: ['', () => new Promise<string>(() => undefined)],\n  skinToneFanOpenState: [false, () => {}],\n  suggestedUpdateState: [Date.now(), () => {}],\n  reactionsModeState: [false, () => {}]\n});\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport function useFilterRef() {\n  const { filterRef } = React.useContext(PickerContext);\n  return filterRef;\n}\n\nexport function useDisallowClickRef() {\n  const { disallowClickRef } = React.useContext(PickerContext);\n  return disallowClickRef;\n}\n\nexport function useDisallowMouseRef() {\n  const { disallowMouseRef } = React.useContext(PickerContext);\n  return disallowMouseRef;\n}\n\nexport function useReactionsModeState() {\n  const { reactionsModeState } = React.useContext(PickerContext);\n  return reactionsModeState;\n}\n\nexport function useSearchTermState() {\n  const { searchTerm } = React.useContext(PickerContext);\n  return searchTerm;\n}\n\nexport function useActiveSkinToneState(): [\n  SkinTones,\n  (skinTone: SkinTones) => void\n] {\n  const { activeSkinTone } = React.useContext(PickerContext);\n  return activeSkinTone;\n}\n\nexport function useEmojisThatFailedToLoadState() {\n  const { emojisThatFailedToLoadState } = React.useContext(PickerContext);\n  return emojisThatFailedToLoadState;\n}\n\nexport function useIsPastInitialLoad(): boolean {\n  const { isPastInitialLoad } = React.useContext(PickerContext);\n  return isPastInitialLoad;\n}\n\nexport function useEmojiVariationPickerState() {\n  const { emojiVariationPickerState } = React.useContext(PickerContext);\n  return emojiVariationPickerState;\n}\n\nexport function useSkinToneFanOpenState() {\n  const { skinToneFanOpenState } = React.useContext(PickerContext);\n  return skinToneFanOpenState;\n}\n\nexport function useDisallowedEmojisRef() {\n  const { disallowedEmojisRef } = React.useContext(PickerContext);\n  return disallowedEmojisRef;\n}\n\nexport function useUpdateSuggested(): [number, () => void] {\n  const { suggestedUpdateState } = React.useContext(PickerContext);\n\n  const [suggestedUpdated, setsuggestedUpdate] = suggestedUpdateState;\n  return [\n    suggestedUpdated,\n    function updateSuggested() {\n      setsuggestedUpdate(Date.now());\n    }\n  ];\n}\n\nexport type FilterState = Record<string, FilterDict>;\n\ntype ActiveCategoryState = null | string;\n", "import { useEffect } from 'react';\nimport * as React from 'react';\n\nexport function useMarkInitialLoad(\n  dispatch: React.Dispatch<React.SetStateAction<boolean>>\n) {\n  useEffect(() => {\n    dispatch(true);\n  }, [dispatch]);\n}\n", "import React from 'react';\n\nimport { MouseDownEvent, OnSkinToneChange } from './config';\n\nexport type MutableConfig = {\n  onEmojiClick?: MouseDownEvent;\n  onReactionClick?: MouseDownEvent;\n  onSkinToneChange?: OnSkinToneChange;\n};\n\nexport const MutableConfigContext = React.createContext<\n  React.MutableRefObject<MutableConfig>\n>({} as React.MutableRefObject<MutableConfig>);\n\nexport function useMutableConfig(): React.MutableRefObject<MutableConfig> {\n  const mutableConfig = React.useContext(MutableConfigContext);\n  return mutableConfig;\n}\n\nexport function useDefineMutableConfig(\n  config: MutableConfig\n): React.MutableRefObject<MutableConfig> {\n  const MutableConfigRef = React.useRef<MutableConfig>({\n    onEmojiClick: config.onEmojiClick || emptyFunc,\n    onReactionClick: config.onReactionClick || config.onEmojiClick,\n    onSkinToneChange: config.onSkinToneChange || emptyFunc\n  });\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onEmojiClick = config.onEmojiClick || emptyFunc;\n    MutableConfigRef.current.onReactionClick =\n      config.onReactionClick || config.onEmojiClick;\n  }, [config.onEmojiClick, config.onReactionClick]);\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onSkinToneChange =\n      config.onSkinToneChange || emptyFunc;\n  }, [config.onSkinToneChange]);\n\n  return MutableConfigRef;\n}\n\nfunction emptyFunc() {}\n", "import { useSearchTermState } from '../components/context/PickerContext';\n\nexport default function useIsSearchMode(): boolean {\n  const [searchTerm] = useSearchTermState();\n\n  return !!searchTerm;\n}\n", "import { NullableElement } from './selectors';\n\nexport function focusElement(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    element.focus();\n  });\n}\n\nexport function focusPrevElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const prev = element.previousElementSibling as HTMLElement;\n\n  focusElement(prev);\n}\n\nexport function focusNextElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const next = element.nextElementSibling as HTMLElement;\n\n  focusElement(next);\n}\n\nexport function focusFirstElementChild(element: NullableElement) {\n  if (!element) return;\n\n  const first = element.firstElementChild as HTMLElement;\n\n  focusElement(first);\n}\n", "import { NullableElement } from './selectors';\n\nexport function getActiveElement() {\n  return document.activeElement as NullableElement;\n}\n", "import * as React from 'react';\n\nimport { focusElement } from '../../DomUtils/focusElement';\nimport { NullableElement } from '../../DomUtils/selectors';\n\nexport function ElementRefContextProvider({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const PickerMainRef = React.useRef<HTMLElement>(null);\n  const AnchoredEmojiRef = React.useRef<HTMLElement>(null);\n  const BodyRef = React.useRef<HTMLDivElement>(null);\n  const SearchInputRef = React.useRef<HTMLInputElement>(null);\n  const SkinTonePickerRef = React.useRef<HTMLDivElement>(null);\n  const CategoryNavigationRef = React.useRef<HTMLDivElement>(null);\n  const VariationPickerRef = React.useRef<HTMLDivElement>(null);\n  const ReactionsRef = React.useRef<HTMLUListElement>(null);\n\n  return (\n    <ElementRefContext.Provider\n      value={{\n        AnchoredEmojiRef,\n        BodyRef,\n        CategoryNavigationRef,\n        PickerMainRef,\n        SearchInputRef,\n        SkinTonePickerRef,\n        VariationPickerRef,\n        ReactionsRef\n      }}\n    >\n      {children}\n    </ElementRefContext.Provider>\n  );\n}\n\nexport type ElementRef<\n  E extends HTMLElement = HTMLElement\n> = React.MutableRefObject<E | null>;\n\ntype ElementRefs = {\n  PickerMainRef: ElementRef;\n  AnchoredEmojiRef: ElementRef;\n  SkinTonePickerRef: ElementRef<HTMLDivElement>;\n  SearchInputRef: ElementRef<HTMLInputElement>;\n  BodyRef: ElementRef<HTMLDivElement>;\n  CategoryNavigationRef: ElementRef<HTMLDivElement>;\n  VariationPickerRef: ElementRef<HTMLDivElement>;\n  ReactionsRef: ElementRef<HTMLUListElement>;\n};\n\nconst ElementRefContext = React.createContext<ElementRefs>({\n  AnchoredEmojiRef: React.createRef(),\n  BodyRef: React.createRef(),\n  CategoryNavigationRef: React.createRef(),\n  PickerMainRef: React.createRef(),\n  SearchInputRef: React.createRef(),\n  SkinTonePickerRef: React.createRef(),\n  VariationPickerRef: React.createRef(),\n  ReactionsRef: React.createRef()\n});\n\nfunction useElementRef() {\n  return React.useContext(ElementRefContext);\n}\n\nexport function usePickerMainRef() {\n  return useElementRef()['PickerMainRef'];\n}\n\nexport function useAnchoredEmojiRef() {\n  return useElementRef()['AnchoredEmojiRef'];\n}\n\nexport function useSetAnchoredEmojiRef(): (target: NullableElement) => void {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return (target: NullableElement) => {\n    if (target === null && AnchoredEmojiRef.current !== null) {\n      focusElement(AnchoredEmojiRef.current);\n    }\n\n    AnchoredEmojiRef.current = target;\n  };\n}\n\nexport function useBodyRef() {\n  return useElementRef()['BodyRef'];\n}\n\nexport function useReactionsRef() {\n  return useElementRef()['ReactionsRef'];\n}\n\nexport function useSearchInputRef() {\n  return useElementRef()['SearchInputRef'];\n}\n\nexport function useSkinTonePickerRef() {\n  return useElementRef()['SkinTonePickerRef'];\n}\n\nexport function useCategoryNavigationRef() {\n  return useElementRef()['CategoryNavigationRef'];\n}\n\nexport function useVariationPickerRef() {\n  return useElementRef()['VariationPickerRef'];\n}\n", "import { useCallback, useEffect, useMemo } from 'react';\n\nimport { hasNextElementSibling } from '../DomUtils/elementPositionInRow';\nimport {\n  focusNextElementSibling,\n  focusPrevElementSibling\n} from '../DomUtils/focusElement';\nimport { getActiveElement } from '../DomUtils/getActiveElement';\nimport {\n  focusAndClickFirstVisibleEmoji,\n  focusFirstVisibleEmoji,\n  focusNextVisibleEmoji,\n  focusPrevVisibleEmoji,\n  focusVisibleEmojiOneRowDown,\n  focusVisibleEmojiOneRowUp\n} from '../DomUtils/keyboardNavigation';\nimport { useScrollTo } from '../DomUtils/scrollTo';\nimport { buttonFromTarget } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  useCategoryNavigationRef,\n  usePickerMainRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\nimport { useSkinToneFanOpenState } from '../components/context/PickerContext';\nimport { useSearchDisabledConfig } from '../config/useConfig';\n\nimport {\n  useCloseAllOpenToggles,\n  useHasOpenToggles\n} from './useCloseAllOpenToggles';\nimport { useDisallowMouseMove } from './useDisallowMouseMove';\nimport { useAppendSearch, useClearSearch } from './useFilter';\nimport {\n  useFocusCategoryNavigation,\n  useFocusSearchInput,\n  useFocusSkinTonePicker\n} from './useFocus';\nimport useIsSearchMode from './useIsSearchMode';\nimport useSetVariationPicker from './useSetVariationPicker';\nimport {\n  useIsSkinToneInPreview,\n  useIsSkinToneInSearch\n} from './useShouldShowSkinTonePicker';\n\nenum KeyboardEvents {\n  ArrowDown = 'ArrowDown',\n  ArrowUp = 'ArrowUp',\n  ArrowLeft = 'ArrowLeft',\n  ArrowRight = 'ArrowRight',\n  Escape = 'Escape',\n  Enter = 'Enter',\n  Space = ' '\n}\n\nexport function useKeyboardNavigation() {\n  usePickerMainKeyboardEvents();\n  useSearchInputKeyboardEvents();\n  useSkinTonePickerKeyboardEvents();\n  useCategoryNavigationKeyboardEvents();\n  useBodyKeyboardEvents();\n}\n\nfunction usePickerMainKeyboardEvents() {\n  const PickerMainRef = usePickerMainRef();\n  const clearSearch = useClearSearch();\n  const scrollTo = useScrollTo();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n  const hasOpenToggles = useHasOpenToggles();\n  const disallowMouseMove = useDisallowMouseMove();\n\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        disallowMouseMove();\n        switch (key) {\n          // eslint-disable-next-line no-fallthrough\n          case KeyboardEvents.Escape:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              return;\n            }\n            clearSearch();\n            scrollTo(0);\n            focusSearchInput();\n            break;\n        }\n      },\n    [\n      scrollTo,\n      clearSearch,\n      closeAllOpenToggles,\n      focusSearchInput,\n      hasOpenToggles,\n      disallowMouseMove\n    ]\n  );\n\n  useEffect(() => {\n    const current = PickerMainRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, scrollTo, onKeyDown]);\n}\n\nfunction useSearchInputKeyboardEvents() {\n  const focusSkinTonePicker = useFocusSkinTonePicker();\n  const PickerMainRef = usePickerMainRef();\n  const BodyRef = useBodyRef();\n  const SearchInputRef = useSearchInputRef();\n  const [, setSkinToneFanOpenState] = useSkinToneFanOpenState();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            if (!isSkinToneInSearch) {\n              return;\n            }\n            event.preventDefault();\n            setSkinToneFanOpenState(true);\n            focusSkinTonePicker();\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            goDownFromSearchInput();\n            break;\n          case KeyboardEvents.Enter:\n            event.preventDefault();\n            focusAndClickFirstVisibleEmoji(BodyRef.current);\n            break;\n        }\n      },\n    [\n      focusSkinTonePicker,\n      goDownFromSearchInput,\n      setSkinToneFanOpenState,\n      BodyRef,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SearchInputRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, onKeyDown]);\n}\n\nfunction useSkinTonePickerKeyboardEvents() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const focusSearchInput = useFocusSearchInput();\n  const SearchInputRef = useSearchInputRef();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        if (isSkinToneInSearch) {\n          switch (key) {\n            case KeyboardEvents.ArrowLeft:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowRight:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (isOpen) {\n                setIsOpen(false);\n              }\n              goDownFromSearchInput();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n\n        if (isSkinToneInPreview) {\n          switch (key) {\n            case KeyboardEvents.ArrowUp:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n      },\n    [\n      isOpen,\n      focusSearchInput,\n      setIsOpen,\n      goDownFromSearchInput,\n      onType,\n      isSkinToneInPreview,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SkinTonePickerRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [SkinTonePickerRef, SearchInputRef, isOpen, onKeyDown]);\n}\n\nfunction useCategoryNavigationKeyboardEvents() {\n  const focusSearchInput = useFocusSearchInput();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const BodyRef = useBodyRef();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            focusSearchInput();\n            break;\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            focusFirstVisibleEmoji(BodyRef.current);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [BodyRef, focusSearchInput, onType]\n  );\n\n  useEffect(() => {\n    const current = CategoryNavigationRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [CategoryNavigationRef, BodyRef, onKeyDown]);\n}\n\nfunction useBodyKeyboardEvents() {\n  const BodyRef = useBodyRef();\n  const goUpFromBody = useGoUpFromBody();\n  const setVariationPicker = useSetVariationPicker();\n  const hasOpenToggles = useHasOpenToggles();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        const activeElement = buttonFromTarget(getActiveElement());\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowDown(activeElement);\n            break;\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowUp(activeElement, goUpFromBody);\n            break;\n          case KeyboardEvents.Space:\n            event.preventDefault();\n            setVariationPicker(event.target as HTMLElement);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [\n      goUpFromBody,\n      onType,\n      setVariationPicker,\n      hasOpenToggles,\n      closeAllOpenToggles\n    ]\n  );\n\n  useEffect(() => {\n    const current = BodyRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [BodyRef, onKeyDown]);\n}\n\nfunction useGoDownFromSearchInput() {\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    function goDownFromSearchInput() {\n      if (isSearchMode) {\n        return focusFirstVisibleEmoji(BodyRef.current);\n      }\n      return focusCategoryNavigation();\n    },\n    [BodyRef, focusCategoryNavigation, isSearchMode]\n  );\n}\n\nfunction useGoUpFromBody() {\n  const focusSearchInput = useFocusSearchInput();\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n\n  return useCallback(\n    function goUpFromEmoji() {\n      if (isSearchMode) {\n        return focusSearchInput();\n      }\n      return focusCategoryNavigation();\n    },\n    [focusSearchInput, isSearchMode, focusCategoryNavigation]\n  );\n}\n\nfunction focusNextSkinTone(exitLeft: () => void) {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  if (!hasNextElementSibling(currentSkinTone)) {\n    exitLeft();\n  }\n\n  focusNextElementSibling(currentSkinTone);\n}\n\nfunction focusPrevSkinTone() {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  focusPrevElementSibling(currentSkinTone);\n}\n\nfunction useOnType() {\n  const appendSearch = useAppendSearch();\n  const focusSearchInput = useFocusSearchInput();\n  const searchDisabled = useSearchDisabledConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  return function onType(event: KeyboardEvent) {\n    const { key } = event;\n\n    if (hasModifier(event) || searchDisabled) {\n      return;\n    }\n\n    if (key.match(/(^[a-zA-Z0-9]$){1}/)) {\n      event.preventDefault();\n      closeAllOpenToggles();\n      focusSearchInput();\n      appendSearch(key);\n    }\n  };\n}\n\nfunction hasModifier(event: KeyboardEvent): boolean {\n  const { metaKey, ctrlKey, altKey } = event;\n\n  return metaKey || ctrlKey || altKey;\n}\n", "import { useCallback } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport {\n  categoryLabelHeight,\n  closestCategory,\n  closestScrollBody,\n  emojiDistanceFromScrollTop,\n  isEmojiBehindLabel,\n  NullableElement,\n  queryScrollBody\n} from './selectors';\n\nexport function scrollTo(root: NullableElement, top: number = 0) {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = top;\n  });\n}\n\nexport function scrollBy(root: NullableElement, by: number): void {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = $eprBody.scrollTop + by;\n  });\n}\n\nexport function useScrollTo() {\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    (top: number) => {\n      requestAnimationFrame(() => {\n        if (BodyRef.current) {\n          BodyRef.current.scrollTop = top;\n        }\n      });\n    },\n    [BodyRef]\n  );\n}\n\nexport function scrollEmojiAboveLabel(emoji: NullableElement) {\n  if (!emoji || !isEmojiBehindLabel(emoji)) {\n    return;\n  }\n\n  if (emoji.closest(asSelectors(ClassNames.variationPicker))) {\n    return;\n  }\n\n  const scrollBody = closestScrollBody(emoji);\n  const by = emojiDistanceFromScrollTop(emoji);\n  scrollBy(scrollBody, -(categoryLabelHeight(closestCategory(emoji)) - by));\n}\n", "import { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  emojiByUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport { firstVisibleElementInContainer } from './elementPositionInRow';\n\nexport type NullableElement = HTMLElement | null;\n\nexport const EmojiButtonSelector = `button${asSelectors(ClassNames.emoji)}`;\nexport const VisibleEmojiSelector = [\n  EmojiButtonSelector,\n  asSelectors(ClassNames.visible),\n  `:not(${asSelectors(ClassNames.hidden)})`\n].join('');\n\nexport function buttonFromTarget(\n  emojiElement: NullableElement\n): HTMLButtonElement | null {\n  return emojiElement?.closest(EmojiButtonSelector) ?? null;\n}\n\nexport function isEmojiButton(element: NullableElement): boolean {\n  if (!element) {\n    return false;\n  }\n\n  return element.matches(EmojiButtonSelector);\n}\n\nexport function emojiFromElement(\n  element: NullableElement\n): [DataEmoji, string] | [] {\n  const originalUnified = originalUnifiedFromEmojiElement(element);\n  const unified = unifiedFromEmojiElement(element);\n\n  if (!originalUnified) {\n    return [];\n  }\n\n  const emoji = emojiByUnified(unified ?? originalUnified);\n\n  if (!emoji) {\n    return [];\n  }\n\n  return [emoji, unified as string];\n}\n\nexport function isEmojiElement(element: NullableElement): boolean {\n  return Boolean(\n    element?.matches(EmojiButtonSelector) ||\n      element?.parentElement?.matches(EmojiButtonSelector)\n  );\n}\n\nexport function categoryLabelFromCategory(\n  category: NullableElement\n): NullableElement {\n  return category?.querySelector(asSelectors(ClassNames.label)) ?? null;\n}\n\nexport function closestCategoryLabel(\n  element: NullableElement\n): NullableElement {\n  const category = closestCategory(element);\n  return categoryLabelFromCategory(category);\n}\n\nexport function elementHeight(element: NullableElement): number {\n  return element?.clientHeight ?? 0;\n}\n\nexport function emojiTrueOffsetTop(element: NullableElement): number {\n  if (!element) {\n    return 0;\n  }\n\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  // compensate for the label height\n  const labelHeight = categoryLabelHeight(category);\n\n  return elementOffsetTop(button) + elementOffsetTop(category) + labelHeight;\n}\n\nexport function categoryLabelHeight(category: NullableElement): number {\n  if (!category) {\n    return 0;\n  }\n\n  const categoryWithoutLabel = category.querySelector(\n    asSelectors(ClassNames.categoryContent)\n  );\n\n  return (\n    (category?.clientHeight ?? 0) - (categoryWithoutLabel?.clientHeight ?? 0)\n  );\n}\n\nexport function isEmojiBehindLabel(emoji: NullableElement): boolean {\n  if (!emoji) {\n    return false;\n  }\n\n  return (\n    emojiDistanceFromScrollTop(emoji) <\n    categoryLabelHeight(closestCategory(emoji))\n  );\n}\n\nexport function queryScrollBody(root: NullableElement): NullableElement {\n  if (!root) return null;\n\n  return root.matches(asSelectors(ClassNames.scrollBody))\n    ? root\n    : root.querySelector(asSelectors(ClassNames.scrollBody));\n}\n\nexport function emojiDistanceFromScrollTop(emoji: NullableElement): number {\n  if (!emoji) {\n    return 0;\n  }\n\n  return emojiTrueOffsetTop(emoji) - (closestScrollBody(emoji)?.scrollTop ?? 0);\n}\n\nexport function closestScrollBody(element: NullableElement): NullableElement {\n  if (!element) {\n    return null;\n  }\n\n  return element.closest(asSelectors(ClassNames.scrollBody)) ?? null;\n}\n\nexport function emojiTruOffsetLeft(element: NullableElement): number {\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  return elementOffsetLeft(button) + elementOffsetLeft(category);\n}\n\nfunction elementOffsetTop(element: NullableElement): number {\n  return element?.offsetTop ?? 0;\n}\n\nfunction elementOffsetLeft(element: NullableElement): number {\n  return element?.offsetLeft ?? 0;\n}\n\nexport function unifiedFromEmojiElement(emoji: NullableElement): string | null {\n  return elementDataSetKey(buttonFromTarget(emoji), 'unified') ?? null;\n}\n\nexport function originalUnifiedFromEmojiElement(\n  emoji: NullableElement\n): string | null {\n  const unified = unifiedFromEmojiElement(emoji);\n\n  if (unified) {\n    return unifiedWithoutSkinTone(unified);\n  }\n  return null;\n}\n\nexport function allUnifiedFromEmojiElement(\n  emoji: NullableElement\n): { unified: string | null; originalUnified: string | null } {\n  if (!emoji) {\n    return {\n      unified: null,\n      originalUnified: null\n    };\n  }\n\n  return {\n    unified: unifiedFromEmojiElement(emoji),\n    originalUnified: originalUnifiedFromEmojiElement(emoji)\n  };\n}\n\nfunction elementDataSetKey(\n  element: NullableElement,\n  key: string\n): string | null {\n  return elementDataSet(element)[key] ?? null;\n}\n\nfunction elementDataSet(element: NullableElement): DOMStringMap {\n  return element?.dataset ?? {};\n}\n\nexport function isVisibleEmoji(element: HTMLElement) {\n  return element.classList.contains(ClassNames.visible);\n}\n\nexport function isHidden(element: NullableElement) {\n  if (!element) return true;\n\n  return element.classList.contains(ClassNames.hidden);\n}\n\nexport function allVisibleEmojis(parent: NullableElement) {\n  if (!parent) {\n    return [];\n  }\n\n  return Array.from(\n    parent.querySelectorAll(VisibleEmojiSelector)\n  ) as HTMLElement[];\n}\n\nexport function lastVisibleEmoji(element: NullableElement): NullableElement {\n  if (!element) return null;\n\n  const allEmojis = allVisibleEmojis(element);\n  const [last] = allEmojis.slice(-1);\n  if (!last) {\n    return null;\n  }\n\n  if (!isVisibleEmoji(last)) {\n    return prevVisibleEmoji(last);\n  }\n\n  return last;\n}\n\nexport function nextVisibleEmoji(element: HTMLElement): NullableElement {\n  const next = element.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return firstVisibleEmoji(nextCategory(element));\n  }\n\n  if (!isVisibleEmoji(next)) {\n    return nextVisibleEmoji(next);\n  }\n\n  return next;\n}\n\nexport function prevVisibleEmoji(element: HTMLElement): NullableElement {\n  const prev = element.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return lastVisibleEmoji(prevCategory(element));\n  }\n\n  if (!isVisibleEmoji(prev)) {\n    return prevVisibleEmoji(prev);\n  }\n\n  return prev;\n}\n\nexport function firstVisibleEmoji(parent: NullableElement) {\n  if (!parent) {\n    return null;\n  }\n\n  const allEmojis = allVisibleEmojis(parent);\n\n  return firstVisibleElementInContainer(parent, allEmojis, 0.1);\n}\n\nexport function prevCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const prev = category.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return null;\n  }\n\n  if (isHidden(prev)) {\n    return prevCategory(prev);\n  }\n\n  return prev;\n}\n\nexport function nextCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const next = category.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return null;\n  }\n\n  if (isHidden(next)) {\n    return nextCategory(next);\n  }\n\n  return next;\n}\n\nexport function closestCategory(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(asSelectors(ClassNames.category)) as HTMLElement;\n}\n\nexport function closestCategoryContent(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(\n    asSelectors(ClassNames.categoryContent)\n  ) as HTMLElement;\n}\n", "import {\n  elementCountInRow,\n  elementIndexInRow,\n  getElementInNextRow,\n  getElementInPrevRow,\n  getElementInRow,\n  hasNextRow,\n  rowNumber\n} from './elementPositionInRow';\nimport { focusElement } from './focusElement';\nimport { scrollEmojiAboveLabel } from './scrollTo';\nimport {\n  allVisibleEmojis,\n  closestCategory,\n  firstVisibleEmoji,\n  lastVisibleEmoji,\n  nextCategory,\n  nextVisibleEmoji,\n  NullableElement,\n  prevCategory,\n  prevVisibleEmoji,\n  closestCategoryContent\n} from './selectors';\n\nexport function focusFirstVisibleEmoji(parent: NullableElement) {\n  const emoji = firstVisibleEmoji(parent);\n  focusElement(emoji);\n  scrollEmojiAboveLabel(emoji);\n}\n\nexport function focusAndClickFirstVisibleEmoji(parent: NullableElement) {\n  const firstEmoji = firstVisibleEmoji(parent);\n\n  focusElement(firstEmoji);\n  firstEmoji?.click();\n}\n\nexport function focusLastVisibleEmoji(parent: NullableElement) {\n  focusElement(lastVisibleEmoji(parent));\n}\n\nexport function focusNextVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = nextVisibleEmoji(element);\n\n  if (!next) {\n    return focusFirstVisibleEmoji(nextCategory(element));\n  }\n\n  focusElement(next);\n  scrollEmojiAboveLabel(next);\n}\n\nexport function focusPrevVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const prev = prevVisibleEmoji(element);\n\n  if (!prev) {\n    return focusLastVisibleEmoji(prevCategory(element));\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowUp(\n  element: NullableElement,\n  exitUp: () => void\n) {\n  if (!element) {\n    return;\n  }\n\n  const prev = visibleEmojiOneRowUp(element);\n\n  if (!prev) {\n    return exitUp();\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowDown(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = visibleEmojiOneRowDown(element);\n\n  return focusElement(next);\n}\n\nfunction visibleEmojiOneRowUp(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n\n  if (row === 0) {\n    const prevVisibleCategory = prevCategory(category);\n\n    if (!prevVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(prevVisibleCategory),\n      -1, // last row\n      countInRow,\n      indexInRow\n    );\n  }\n\n  return getElementInPrevRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n}\n\nfunction visibleEmojiOneRowDown(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n  if (!hasNextRow(categoryContent, element)) {\n    const nextVisibleCategory = nextCategory(category);\n\n    if (!nextVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(nextVisibleCategory),\n      0,\n      countInRow,\n      indexInRow\n    );\n  }\n\n  const itemInNextRow = getElementInNextRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n\n  return itemInNextRow;\n}\n", "import { useCallback } from 'react';\n\nimport {\n  useEmojiVariationPickerState,\n  useSkinToneFanOpenState\n} from '../components/context/PickerContext';\n\nexport function useCloseAllOpenToggles() {\n  const [variationPicker, setVariationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen, setSkinToneFanOpen] = useSkinToneFanOpenState();\n\n  const closeAllOpenToggles = useCallback(() => {\n    if (variationPicker) {\n      setVariationPicker(null);\n    }\n\n    if (skinToneFanOpen) {\n      setSkinToneFanOpen(false);\n    }\n  }, [\n    variationPicker,\n    skinToneFanOpen,\n    setVariationPicker,\n    setSkinToneFanOpen\n  ]);\n\n  return closeAllOpenToggles;\n}\n\nexport function useHasOpenToggles() {\n  const [variationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen] = useSkinToneFanOpenState();\n\n  return function hasOpenToggles() {\n    return !!variationPicker || skinToneFanOpen;\n  };\n}\n", "import { useEffect } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useDisallowMouseRef } from '../components/context/PickerContext';\n\nexport function useDisallowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function disallowMouseMove() {\n    DisallowMouseRef.current = true;\n  };\n}\n\nexport function useAllowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function allowMouseMove() {\n    DisallowMouseRef.current = false;\n  };\n}\n\nexport function useIsMouseDisallowed() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function isMouseDisallowed() {\n    return DisallowMouseRef.current;\n  };\n}\n\nexport function useOnMouseMove() {\n  const BodyRef = useBodyRef();\n  const allowMouseMove = useAllowMouseMove();\n  const isMouseDisallowed = useIsMouseDisallowed();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    bodyRef?.addEventListener('mousemove', onMouseMove, {\n      passive: true\n    });\n\n    function onMouseMove() {\n      if (isMouseDisallowed()) {\n        allowMouseMove();\n      }\n    }\n    return () => {\n      bodyRef?.removeEventListener('mousemove', onMouseMove);\n    };\n  }, [BodyRef, allowMouseMove, isMouseDisallowed]);\n}\n", "import { useCallback } from 'react';\n\nimport { focusElement, focusFirstElementChild } from '../DomUtils/focusElement';\nimport {\n  useCategoryNavigationRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\n\nexport function useFocusSearchInput() {\n  const SearchInputRef = useSearchInputRef();\n\n  return useCallback(() => {\n    focusElement(SearchInputRef.current);\n  }, [SearchInputRef]);\n}\n\nexport function useFocusSkinTonePicker() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n\n  return useCallback(() => {\n    if (!SkinTonePickerRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(SkinTonePickerRef.current);\n  }, [SkinTonePickerRef]);\n}\n\nexport function useFocusCategoryNavigation() {\n  const CategoryNavigationRef = useCategoryNavigationRef();\n\n  return useCallback(() => {\n    if (!CategoryNavigationRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(CategoryNavigationRef.current);\n  }, [CategoryNavigationRef]);\n}\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport {\n  usePickerMainRef,\n  useSearchInputRef\n} from '../components/context/ElementRefContext';\nimport {\n  FilterState,\n  useFilterRef,\n  useSearchTermState\n} from '../components/context/PickerContext';\nimport { useSearchResultsConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiNames } from '../dataUtils/emojiSelectors';\n\nimport { useFocusSearchInput } from './useFocus';\n\nfunction useSetFilterRef() {\n  const filterRef = useFilterRef();\n\n  return function setFilter(\n    setter: FilterState | ((current: FilterState) => FilterState)\n  ): void {\n    if (typeof setter === 'function') {\n      return setFilter(setter(filterRef.current));\n    }\n\n    filterRef.current = setter;\n  };\n}\n\nexport function useClearSearch() {\n  const applySearch = useApplySearch();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n\n  return function clearSearch() {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = '';\n    }\n\n    applySearch('');\n    focusSearchInput();\n  };\n}\n\nexport function useAppendSearch() {\n  const SearchInputRef = useSearchInputRef();\n  const applySearch = useApplySearch();\n\n  return function appendSearch(str: string) {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = `${SearchInputRef.current.value}${str}`;\n      applySearch(getNormalizedSearchTerm(SearchInputRef.current.value));\n    } else {\n      applySearch(getNormalizedSearchTerm(str));\n    }\n  };\n}\n\nexport function useFilter() {\n  const SearchInputRef = useSearchInputRef();\n  const filterRef = useFilterRef();\n  const setFilterRef = useSetFilterRef();\n  const applySearch = useApplySearch();\n\n  const [searchTerm] = useSearchTermState();\n  const statusSearchResults = getStatusSearchResults(\n    filterRef.current,\n    searchTerm\n  );\n\n  return {\n    onChange,\n    searchTerm,\n    SearchInputRef,\n    statusSearchResults\n  };\n\n  function onChange(inputValue: string) {\n    const filter = filterRef.current;\n\n    const nextValue = inputValue.toLowerCase();\n\n    if (filter?.[nextValue] || nextValue.length <= 1) {\n      return applySearch(nextValue);\n    }\n\n    const longestMatch = findLongestMatch(nextValue, filter);\n\n    if (!longestMatch) {\n      // Can we even get here?\n      // If so, we need to search among all emojis\n      return applySearch(nextValue);\n    }\n\n    setFilterRef(current =>\n      Object.assign(current, {\n        [nextValue]: filterEmojiObjectByKeyword(longestMatch, nextValue)\n      })\n    );\n    applySearch(nextValue);\n  }\n}\n\nfunction useApplySearch() {\n  const [, setSearchTerm] = useSearchTermState();\n  const PickerMainRef = usePickerMainRef();\n\n  return function applySearch(searchTerm: string) {\n    requestAnimationFrame(() => {\n      setSearchTerm(searchTerm ? searchTerm?.toLowerCase() : searchTerm).then(\n        () => {\n          scrollTo(PickerMainRef.current, 0);\n        }\n      );\n    });\n  };\n}\n\nfunction filterEmojiObjectByKeyword(\n  emojis: FilterDict,\n  keyword: string\n): FilterDict {\n  const filtered: FilterDict = {};\n\n  for (const unified in emojis) {\n    const emoji = emojis[unified];\n\n    if (hasMatch(emoji, keyword)) {\n      filtered[unified] = emoji;\n    }\n  }\n\n  return filtered;\n}\n\nfunction hasMatch(emoji: DataEmoji, keyword: string): boolean {\n  return emojiNames(emoji).some(name => name.includes(keyword));\n}\n\nexport function useIsEmojiFiltered(): (unified: string) => boolean {\n  const { current: filter } = useFilterRef();\n  const [searchTerm] = useSearchTermState();\n\n  return unified => isEmojiFilteredBySearchTerm(unified, filter, searchTerm);\n}\n\nfunction isEmojiFilteredBySearchTerm(\n  unified: string,\n  filter: FilterState,\n  searchTerm: string\n): boolean {\n  if (!filter || !searchTerm) {\n    return false;\n  }\n\n  return !filter[searchTerm]?.[unified];\n}\n\nexport type FilterDict = Record<string, DataEmoji>;\n\nfunction findLongestMatch(\n  keyword: string,\n  dict: Record<string, FilterDict> | null\n): FilterDict | null {\n  if (!dict) {\n    return null;\n  }\n\n  if (dict[keyword]) {\n    return dict[keyword];\n  }\n\n  const longestMatchingKey = Object.keys(dict)\n    .sort((a, b) => b.length - a.length)\n    .find(key => keyword.includes(key));\n\n  if (longestMatchingKey) {\n    return dict[longestMatchingKey];\n  }\n\n  return null;\n}\n\nexport function getNormalizedSearchTerm(str: string): string {\n  if (!str || typeof str !== 'string') {\n    return '';\n  }\n\n  return str.trim().toLowerCase();\n}\n\nfunction getStatusSearchResults(\n  filterState: FilterState,\n  searchTerm: string\n): string {\n  if (!filterState?.[searchTerm]) return '';\n\n  const searchResultsCount =\n    Object.entries(filterState?.[searchTerm])?.length || 0;\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useSearchResultsConfig(searchResultsCount);\n}\n", "import { emojiFromElement, NullableElement } from '../DomUtils/selectors';\nimport { useSetAnchoredEmojiRef } from '../components/context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../components/context/PickerContext';\n\nexport default function useSetVariationPicker() {\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n\n  return function setVariationPicker(element: NullableElement) {\n    const [emoji] = emojiFromElement(element);\n\n    if (emoji) {\n      setAnchoredEmojiRef(element);\n      setEmojiVariationPicker(emoji);\n    }\n  };\n}\n", "import { useSkinTonePickerLocationConfig } from '../config/useConfig';\nimport { SkinTonePickerLocation } from '../types/exposedTypes';\n\nexport function useShouldShowSkinTonePicker() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return function shouldShowSkinTonePicker(location: SkinTonePickerLocation) {\n    return skinTonePickerLocationConfig === location;\n  };\n}\n\nexport function useIsSkinToneInSearch() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.SEARCH;\n}\n\nexport function useIsSkinToneInPreview() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.PREVIEW;\n}\n", "import { DEFAULT_LABEL_HEIGHT } from '../components/main/PickerMain';\n\nimport { ClassNames, asSelectors } from './classNames';\nimport { NullableElement } from './selectors';\n\nexport function elementCountInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const parentWidth = parent.getBoundingClientRect().width;\n  const elementWidth = element.getBoundingClientRect().width;\n  return Math.floor(parentWidth / elementWidth);\n}\n\nexport function elementIndexInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementWidth = element.getBoundingClientRect().width;\n  const elementLeft = element.getBoundingClientRect().left;\n  const parentLeft = parent.getBoundingClientRect().left;\n\n  return Math.floor((elementLeft - parentLeft) / elementWidth);\n}\n\nexport function rowNumber(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  return Math.round((elementTop - parentTop) / elementHeight);\n}\n\nexport function hasNextRow(\n  parent: NullableElement,\n  element: NullableElement\n): boolean {\n  if (!parent || !element) {\n    return false;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentHeight = parent.getBoundingClientRect().height;\n\n  return Math.round(elementTop - parentTop + elementHeight) < parentHeight;\n}\n\nfunction getRowElements(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number\n): HTMLElement[] {\n  if (row === -1) {\n    const lastRow = Math.floor((elements.length - 1) / elementsInRow);\n    const firstElementIndex = lastRow * elementsInRow;\n    const lastElementIndex = elements.length - 1;\n    return elements.slice(firstElementIndex, lastElementIndex + 1);\n  }\n\n  return elements.slice(row * elementsInRow, (row + 1) * elementsInRow);\n}\n\nfunction getNextRowElements(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number\n): HTMLElement[] {\n  const nextRow = currentRow + 1;\n\n  if (nextRow * elementsInRow > allElements.length) {\n    return [];\n  }\n\n  return getRowElements(allElements, nextRow, elementsInRow);\n}\n\nexport function getElementInRow(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number,\n  indexInRow: number\n): NullableElement {\n  const rowElements = getRowElements(elements, row, elementsInRow);\n  // get element, default to last\n  return rowElements[indexInRow] || rowElements[rowElements.length - 1] || null;\n}\n\nexport function getElementInNextRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const nextRowElements = getNextRowElements(\n    allElements,\n    currentRow,\n    elementsInRow\n  );\n\n  // return item in index, or last item in row\n  return (\n    nextRowElements[index] ||\n    nextRowElements[nextRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function getElementInPrevRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const prevRowElements = getRowElements(\n    allElements,\n    currentRow - 1,\n    elementsInRow\n  );\n\n  // default to last\n  return (\n    prevRowElements[index] ||\n    prevRowElements[prevRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function firstVisibleElementInContainer(\n  parent: NullableElement,\n  elements: HTMLElement[],\n  maxVisibilityDiffThreshold = 0\n): NullableElement {\n  if (!parent || !elements.length) {\n    return null;\n  }\n\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentBottom = parent.getBoundingClientRect().bottom;\n  const parentTopWithLabel = parentTop + getLabelHeight(parent);\n\n  const visibleElements = elements.find(element => {\n    const elementTop = element.getBoundingClientRect().top;\n    const elementBottom = element.getBoundingClientRect().bottom;\n    const maxVisibilityDiffPixels =\n      element.clientHeight * maxVisibilityDiffThreshold;\n\n    const elementTopWithAllowedDiff = elementTop + maxVisibilityDiffPixels;\n    const elementBottomWithAllowedDiff =\n      elementBottom - maxVisibilityDiffPixels;\n\n    if (elementTopWithAllowedDiff < parentTopWithLabel) {\n      return false;\n    }\n\n    return (\n      (elementTopWithAllowedDiff >= parentTop &&\n        elementTopWithAllowedDiff <= parentBottom) ||\n      (elementBottomWithAllowedDiff >= parentTop &&\n        elementBottomWithAllowedDiff <= parentBottom)\n    );\n  });\n\n  return visibleElements || null;\n}\n\nexport function hasNextElementSibling(element: HTMLElement) {\n  return !!element.nextElementSibling;\n}\n\nfunction getLabelHeight(parentNode: HTMLElement) {\n  const labels = Array.from(\n    parentNode.querySelectorAll(asSelectors(ClassNames.label))\n  );\n\n  for (const label of labels) {\n    const height = label.getBoundingClientRect().height;\n    // return height if label is not hidden\n    if (height > 0) {\n      return height;\n    }\n  }\n\n  return DEFAULT_LABEL_HEIGHT;\n}\n", "import { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified, emojiVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nexport function preloadEmoji(\n  getEmojiUrl: GetEmojiUrl,\n  emoji: undefined | DataEmoji,\n  emojiStyle: EmojiStyle\n): void {\n  if (!emoji) {\n    return;\n  }\n\n  if (emojiStyle === EmojiStyle.NATIVE) {\n    return;\n  }\n\n  const unified = emojiUnified(emoji);\n\n  if (preloadedEmojs.has(unified)) {\n    return;\n  }\n\n  emojiVariations(emoji).forEach((variation) => {\n    const emojiUrl = getEmojiUrl(variation, emojiStyle);\n    preloadImage(emojiUrl);\n  });\n\n  preloadedEmojs.add(unified);\n}\n\nexport const preloadedEmojs: Set<string> = new Set();\n\nfunction preloadImage(url: string): void {\n  const image = new Image();\n  image.src = url;\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useClassNameConfig,\n  useStyleConfig,\n  useThemeConfig\n} from '../../config/useConfig';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';\nimport { useOnFocus } from '../../hooks/useOnFocus';\nimport { Theme } from '../../types/exposedTypes';\nimport { usePickerMainRef } from '../context/ElementRefContext';\nimport {\n  PickerContextProvider,\n  useReactionsModeState\n} from '../context/PickerContext';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport const DEFAULT_LABEL_HEIGHT = 40;\n\nexport default function PickerMain({ children }: Props) {\n  return (\n    <PickerContextProvider>\n      <PickerRootElement>{children}</PickerRootElement>\n    </PickerContextProvider>\n  );\n}\n\ntype RootProps = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n  children: React.ReactNode;\n}>;\n\nfunction PickerRootElement({ children }: RootProps) {\n  const [reactionsMode] = useReactionsModeState();\n  const theme = useThemeConfig();\n  const searchModeActive = useIsSearchMode();\n  const PickerMainRef = usePickerMainRef();\n  const className = useClassNameConfig();\n  const style = useStyleConfig();\n\n  useKeyboardNavigation();\n  useOnFocus();\n\n  const { width, height, ...styleProps } = style || {};\n\n  return (\n    <aside\n      className={cx(\n        styles.main,\n        styles.baseVariables,\n        theme === Theme.DARK && styles.darkTheme,\n        theme === Theme.AUTO && styles.autoThemeDark,\n        {\n          [ClassNames.searchActive]: searchModeActive\n        },\n        reactionsMode && styles.reactionsMenu,\n        className\n      )}\n      ref={PickerMainRef}\n      style={{\n        ...styleProps,\n        ...(!reactionsMode && { height, width })\n      }}\n    >\n      {children}\n    </aside>\n  );\n}\n\nconst DarkTheme = {\n  '--epr-emoji-variation-picker-bg-color':\n    'var(--epr-dark-emoji-variation-picker-bg-color)',\n  '--epr-hover-bg-color-reduced-opacity':\n    'var(--epr-dark-hover-bg-color-reduced-opacity)',\n  '--epr-highlight-color': 'var(--epr-dark-highlight-color)',\n  '--epr-text-color': 'var(--epr-dark-text-color)',\n  '--epr-hover-bg-color': 'var(--epr-dark-hover-bg-color)',\n  '--epr-focus-bg-color': 'var(--epr-dark-focus-bg-color)',\n  '--epr-search-input-bg-color': 'var(--epr-dark-search-input-bg-color)',\n  '--epr-category-label-bg-color': 'var(--epr-dark-category-label-bg-color)',\n  '--epr-picker-border-color': 'var(--epr-dark-picker-border-color)',\n  '--epr-bg-color': 'var(--epr-dark-bg-color)',\n  '--epr-reactions-bg-color': 'var(--epr-dark-reactions-bg-color)',\n  '--epr-search-input-bg-color-active':\n    'var(--epr-dark-search-input-bg-color-active)',\n  '--epr-emoji-variation-indicator-color':\n    'var(--epr-dark-emoji-variation-indicator-color)',\n  '--epr-category-icon-active-color':\n    'var(--epr-dark-category-icon-active-color)',\n  '--epr-skin-tone-picker-menu-color':\n    'var(--epr-dark-skin-tone-picker-menu-color)',\n  '--epr-skin-tone-outer-border-color': 'var(--epr-dark-skin-tone-outer-border-color)',\n  '--epr-skin-tone-inner-border-color': 'var(--epr-dark-skin-tone-inner-border-color)'\n};\n\nconst styles = stylesheet.create({\n  main: {\n    '.': ['epr-main', ClassNames.emojiPicker],\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    borderWidth: '1px',\n    borderStyle: 'solid',\n    borderRadius: 'var(--epr-picker-border-radius)',\n    borderColor: 'var(--epr-picker-border-color)',\n    backgroundColor: 'var(--epr-bg-color)',\n    overflow: 'hidden',\n    transition: 'all 0.3s ease-in-out, background-color 0.1s ease-in-out',\n    '*': {\n      boxSizing: 'border-box',\n      fontFamily: 'sans-serif'\n    }\n  },\n  baseVariables: {\n    '--': {\n      '--epr-highlight-color': '#007aeb',\n      '--epr-hover-bg-color': '#e5f0fa',\n      '--epr-hover-bg-color-reduced-opacity': '#e5f0fa80',\n      '--epr-focus-bg-color': '#e0f0ff',\n      '--epr-text-color': '#858585',\n      '--epr-search-input-bg-color': '#f6f6f6',\n      '--epr-picker-border-color': '#e7e7e7',\n      '--epr-bg-color': '#fff',\n      '--epr-reactions-bg-color': '#ffffff90',\n      '--epr-category-icon-active-color': '#6aa8de',\n      '--epr-skin-tone-picker-menu-color': '#ffffff95',\n      '--epr-skin-tone-outer-border-color': '#555555',\n      '--epr-skin-tone-inner-border-color': 'var(--epr-bg-color)',\n\n      '--epr-horizontal-padding': '10px',\n\n      '--epr-picker-border-radius': '8px',\n\n      /* Header */\n      '--epr-search-border-color': 'var(--epr-highlight-color)',\n      '--epr-header-padding': '15px var(--epr-horizontal-padding)',\n\n      /* Skin Tone Picker */\n      '--epr-active-skin-tone-indicator-border-color':\n        'var(--epr-highlight-color)',\n      '--epr-active-skin-hover-color': 'var(--epr-hover-bg-color)',\n\n      /* Search */\n      '--epr-search-input-bg-color-active': 'var(--epr-search-input-bg-color)',\n      '--epr-search-input-padding': '0 30px',\n      '--epr-search-input-border-radius': '8px',\n      '--epr-search-input-height': '40px',\n      '--epr-search-input-text-color': 'var(--epr-text-color)',\n      '--epr-search-input-placeholder-color': 'var(--epr-text-color)',\n      '--epr-search-bar-inner-padding': 'var(--epr-horizontal-padding)',\n\n      /*  Category Navigation */\n      '--epr-category-navigation-button-size': '30px',\n\n      /* Variation Picker */\n      '--epr-emoji-variation-picker-height': '45px',\n      '--epr-emoji-variation-picker-bg-color': 'var(--epr-bg-color)',\n\n      /*  Preview */\n      '--epr-preview-height': '70px',\n      '--epr-preview-text-size': '14px',\n      '--epr-preview-text-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-preview-border-color': 'var(--epr-picker-border-color)',\n      '--epr-preview-text-color': 'var(--epr-text-color)',\n\n      /* Category */\n      '--epr-category-padding': '0 var(--epr-horizontal-padding)',\n\n      /*  Category Label */\n      '--epr-category-label-bg-color': '#ffffffe6',\n      '--epr-category-label-text-color': 'var(--epr-text-color)',\n      '--epr-category-label-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-category-label-height': `${DEFAULT_LABEL_HEIGHT}px`,\n\n      /*  Emoji */\n      '--epr-emoji-size': '30px',\n      '--epr-emoji-padding': '5px',\n      '--epr-emoji-fullsize':\n        'calc(var(--epr-emoji-size) + var(--epr-emoji-padding) * 2)',\n      '--epr-emoji-hover-color': 'var(--epr-hover-bg-color)',\n      '--epr-emoji-variation-indicator-color': 'var(--epr-picker-border-color)',\n      '--epr-emoji-variation-indicator-color-hover': 'var(--epr-text-color)',\n\n      /* Z-Index */\n      '--epr-header-overlay-z-index': '3',\n      '--epr-emoji-variations-indictator-z-index': '1',\n      '--epr-category-label-z-index': '2',\n      '--epr-skin-variation-picker-z-index': '5',\n      '--epr-preview-z-index': '6',\n\n      /* Dark Theme Variables */\n      '--epr-dark': '#000',\n      '--epr-dark-emoji-variation-picker-bg-color': 'var(--epr-dark)',\n      '--epr-dark-highlight-color': '#c0c0c0',\n      '--epr-dark-text-color': 'var(--epr-highlight-color)',\n      '--epr-dark-hover-bg-color': '#363636f6',\n      '--epr-dark-hover-bg-color-reduced-opacity': '#36363680',\n      '--epr-dark-focus-bg-color': '#474747',\n      '--epr-dark-search-input-bg-color': '#333333',\n      '--epr-dark-category-label-bg-color': '#222222e6',\n      '--epr-dark-picker-border-color': '#151617',\n      '--epr-dark-bg-color': '#222222',\n      '--epr-dark-reactions-bg-color': '#22222290',\n      '--epr-dark-search-input-bg-color-active': 'var(--epr-dark)',\n      '--epr-dark-emoji-variation-indicator-color': '#444',\n      '--epr-dark-category-icon-active-color': '#3271b7',\n      '--epr-dark-skin-tone-picker-menu-color': '#22222295',\n      '--epr-dark-skin-tone-outer-border-color': 'var(--epr-dark-picker-border-color)',\n      '--epr-dark-skin-tone-inner-border-color': '#00000000',\n    }\n  },\n  autoThemeDark: {\n    '.': ClassNames.autoTheme,\n    '@media (prefers-color-scheme: dark)': {\n      '--': DarkTheme\n    }\n  },\n  darkTheme: {\n    '.': ClassNames.darkTheme,\n    '--': DarkTheme\n  },\n  reactionsMenu: {\n    '.': 'epr-reactions',\n    height: '50px',\n    display: 'inline-flex',\n    backgroundColor: 'var(--epr-reactions-bg-color)',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(8px)',\n    '--': {\n      '--epr-picker-border-radius': '50px'\n    }\n  }\n});\n", "import { useEffect } from 'react';\n\nimport { buttonFromTarget, emojiFromElement } from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useEmojiStyleConfig, useGetEmojiUrlConfig } from '../config/useConfig';\nimport { emojiHasVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nimport { preloadEmoji } from './preloadEmoji';\n\nexport function useOnFocus() {\n  const BodyRef = useBodyRef();\n  const emojiStyle = useEmojiStyleConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEffect(() => {\n    if (emojiStyle === EmojiStyle.NATIVE) {\n      return;\n    }\n\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('focusin', onFocus);\n\n    return () => {\n      bodyRef?.removeEventListener('focusin', onFocus);\n    };\n\n    function onFocus(event: FocusEvent) {\n      const button = buttonFromTarget(event.target as HTMLElement);\n\n      if (!button) {\n        return;\n      }\n\n      const [emoji] = emojiFromElement(button);\n\n      if (!emoji) {\n        return;\n      }\n\n      if (emojiHasVariations(emoji)) {\n        preloadEmoji(getEmojiUrl, emoji, emojiStyle);\n      }\n    }\n  }, [BodyRef, emojiStyle, getEmojiUrl]);\n}\n", "export function parseNativeEmoji(unified: string): string {\n  return unified\n    .split('-')\n    .map(hex => String.fromCodePoint(parseInt(hex, 16)))\n    .join('');\n}\n", "import { SkinTones, SuggestionMode } from '../types/exposedTypes';\n\nimport { DataEmoji } from './DataTypes';\nimport { emojiUnified } from './emojiSelectors';\n\nconst SUGGESTED_LS_KEY = 'epr_suggested';\n\ntype SuggestedItem = {\n  unified: string;\n  original: string;\n  count: number;\n};\n\ntype Suggested = SuggestedItem[];\n\nexport function getSuggested(mode?: SuggestionMode): Suggested {\n  try {\n    if (!window?.localStorage) {\n      return [];\n    }\n    const recent = JSON.parse(\n      window?.localStorage.getItem(SUGGESTED_LS_KEY) ?? '[]'\n    ) as Suggested;\n\n    if (mode === SuggestionMode.FREQUENT) {\n      return recent.sort((a, b) => b.count - a.count);\n    }\n\n    return recent;\n  } catch {\n    return [];\n  }\n}\n\nexport function setSuggested(emoji: DataEmoji, skinTone: SkinTones) {\n  const recent = getSuggested();\n\n  const unified = emojiUnified(emoji, skinTone);\n  const originalUnified = emojiUnified(emoji);\n\n  let existing = recent.find(({ unified: u }) => u === unified);\n\n  let nextList: SuggestedItem[];\n\n  if (existing) {\n    nextList = [existing].concat(recent.filter(i => i !== existing));\n  } else {\n    existing = {\n      unified,\n      original: originalUnified,\n      count: 0\n    };\n    nextList = [existing, ...recent];\n  }\n\n  existing.count++;\n\n  nextList.length = Math.min(nextList.length, 14);\n\n  try {\n    window?.localStorage.setItem(SUGGESTED_LS_KEY, JSON.stringify(nextList));\n    // Prevents the change from being seen immediately.\n  } catch {\n    // ignore\n  }\n}\n", "import {\n  Categories,\n  CategoryConfig,\n  CustomCategoryConfig\n} from '../config/categoryConfig';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\n\nexport function isCustomCategory(\n  category: CategoryConfig | CustomCategoryConfig\n): category is CustomCategoryConfig {\n  return category.category === Categories.CUSTOM;\n}\n\nexport function isCustomEmoji(emoji: Partial<DataEmoji>): emoji is CustomEmoji {\n  return emoji.imgUrl !== undefined;\n}\n", "import * as React from 'react';\nimport { useEffect, useRef } from 'react';\n\nimport {\n  emojiFromElement,\n  isEmojiElement,\n  NullableElement\n} from '../DomUtils/selectors';\nimport {\n  useActiveSkinToneState,\n  useDisallowClickRef,\n  useEmojiVariationPickerState,\n  useUpdateSuggested\n} from '../components/context/PickerContext';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useOnEmojiClickConfig\n} from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  activeVariationFromUnified,\n  emojiHasVariations,\n  emojiNames,\n  emojiUnified\n} from '../dataUtils/emojiSelectors';\nimport { parseNativeEmoji } from '../dataUtils/parseNativeEmoji';\nimport { setSuggested } from '../dataUtils/suggested';\nimport { isCustomEmoji } from '../typeRefinements/typeRefinements';\nimport { EmojiClickData, SkinTones, EmojiStyle } from '../types/exposedTypes';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\nimport useSetVariationPicker from './useSetVariationPicker';\n\nexport function useMouseDownHandlers(\n  ContainerRef: React.MutableRefObject<NullableElement>,\n  mouseEventSource: MOUSE_EVENT_SOURCE\n) {\n  const mouseDownTimerRef = useRef<undefined | number>();\n  const setVariationPicker = useSetVariationPicker();\n  const disallowClickRef = useDisallowClickRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const onEmojiClick = useOnEmojiClickConfig(mouseEventSource);\n  const [, updateSuggested] = useUpdateSuggested();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const activeEmojiStyle = useEmojiStyleConfig();\n\n  const onClick = React.useCallback(\n    function onClick(event: MouseEvent) {\n      if (disallowClickRef.current) {\n        return;\n      }\n\n      closeAllOpenToggles();\n\n      const [emoji, unified] = emojiFromEvent(event);\n\n      if (!emoji || !unified) {\n        return;\n      }\n\n      const skinToneToUse =\n        activeVariationFromUnified(unified) || activeSkinTone;\n\n      updateSuggested();\n      setSuggested(emoji, skinToneToUse);\n      onEmojiClick(\n        emojiClickOutput(emoji, skinToneToUse, activeEmojiStyle, getEmojiUrl),\n        event\n      );\n    },\n    [\n      activeSkinTone,\n      closeAllOpenToggles,\n      disallowClickRef,\n      onEmojiClick,\n      updateSuggested,\n      getEmojiUrl,\n      activeEmojiStyle\n    ]\n  );\n\n  const onMouseDown = React.useCallback(\n    function onMouseDown(event: MouseEvent) {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n      }\n\n      const [emoji] = emojiFromEvent(event);\n\n      if (!emoji || !emojiHasVariations(emoji)) {\n        return;\n      }\n\n      mouseDownTimerRef.current = window?.setTimeout(() => {\n        disallowClickRef.current = true;\n        mouseDownTimerRef.current = undefined;\n        closeAllOpenToggles();\n        setVariationPicker(event.target as HTMLElement);\n        setEmojiVariationPicker(emoji);\n      }, 500);\n    },\n    [\n      disallowClickRef,\n      closeAllOpenToggles,\n      setVariationPicker,\n      setEmojiVariationPicker\n    ]\n  );\n  const onMouseUp = React.useCallback(\n    function onMouseUp() {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n        mouseDownTimerRef.current = undefined;\n      } else if (disallowClickRef.current) {\n        // The problem we're trying to overcome here\n        // is that the emoji has both mouseup and click events\n        // and when releasing a mouseup event\n        // the click gets triggered too\n        // So we're disallowing the click event for a short time\n\n        requestAnimationFrame(() => {\n          disallowClickRef.current = false;\n        });\n      }\n    },\n    [disallowClickRef]\n  );\n\n  useEffect(() => {\n    if (!ContainerRef.current) {\n      return;\n    }\n    const confainerRef = ContainerRef.current;\n    confainerRef.addEventListener('click', onClick, {\n      passive: true\n    });\n\n    confainerRef.addEventListener('mousedown', onMouseDown, {\n      passive: true\n    });\n    confainerRef.addEventListener('mouseup', onMouseUp, {\n      passive: true\n    });\n\n    return () => {\n      confainerRef?.removeEventListener('click', onClick);\n      confainerRef?.removeEventListener('mousedown', onMouseDown);\n      confainerRef?.removeEventListener('mouseup', onMouseUp);\n    };\n  }, [ContainerRef, onClick, onMouseDown, onMouseUp]);\n}\n\nfunction emojiFromEvent(event: MouseEvent): [DataEmoji, string] | [] {\n  const target = event?.target as HTMLElement;\n  if (!isEmojiElement(target)) {\n    return [];\n  }\n\n  return emojiFromElement(target);\n}\n\nfunction emojiClickOutput(\n  emoji: DataEmoji,\n  activeSkinTone: SkinTones,\n  activeEmojiStyle: EmojiStyle,\n  getEmojiUrl: GetEmojiUrl\n): EmojiClickData {\n  const names = emojiNames(emoji);\n\n  if (isCustomEmoji(emoji)) {\n    const unified = emojiUnified(emoji);\n    return {\n      activeSkinTone,\n      emoji: unified,\n      getImageUrl() {\n        return emoji.imgUrl;\n      },\n      imageUrl: emoji.imgUrl,\n      isCustom: true,\n      names,\n      unified,\n      unifiedWithoutSkinTone: unified\n    };\n  }\n  const unified = emojiUnified(emoji, activeSkinTone);\n\n  return {\n    activeSkinTone,\n    emoji: parseNativeEmoji(unified),\n    getImageUrl(emojiStyle: EmojiStyle = activeEmojiStyle ?? EmojiStyle.APPLE) {\n      return getEmojiUrl(unified, emojiStyle);\n    },\n    imageUrl: getEmojiUrl(unified, activeEmojiStyle ?? EmojiStyle.APPLE),\n    isCustom: false,\n    names,\n    unified,\n    unifiedWithoutSkinTone: emojiUnified(emoji)\n  };\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\ninterface Props\n  extends React.DetailedHTMLProps<\n    React.ButtonHTMLAttributes<HTMLButtonElement>,\n    HTMLButtonElement\n  > {\n  className?: string;\n}\n\nexport function Button(props: Props) {\n  return (\n    <button\n      type=\"button\"\n      {...props}\n      className={cx(styles.button, props.className)}\n    >\n      {props.children}\n    </button>\n  );\n}\n\nconst styles = stylesheet.create({\n  button: {\n    '.': 'epr-btn',\n    cursor: 'pointer',\n    border: '0',\n    background: 'none',\n    outline: 'none'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\n\ntype ClickableEmojiButtonProps = Readonly<{\n  hidden?: boolean;\n  showVariations?: boolean;\n  hiddenOnSearch?: boolean;\n  emojiNames: string[];\n  children: React.ReactNode;\n  hasVariations: boolean;\n  unified?: string;\n  noBackground?: boolean;\n  className?: string;\n}>;\n\nexport function ClickableEmojiButton({\n  emojiNames,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  showVariations = true,\n  hasVariations,\n  children,\n  className,\n  noBackground = false\n}: ClickableEmojiButtonProps) {\n  return (\n    <Button\n      className={cx(\n        styles.emoji,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch,\n        {\n          [ClassNames.visible]: !hidden && !hiddenOnSearch\n        },\n        !!(hasVariations && showVariations) && styles.hasVariations,\n        noBackground && styles.noBackground,\n        className\n      )}\n      data-unified={unified}\n      aria-label={getAriaLabel(emojiNames)}\n      data-full-name={emojiNames}\n    >\n      {children}\n    </Button>\n  );\n}\n\nfunction getAriaLabel(emojiNames: string[]) {\n  return emojiNames[0].match('flag-')\n    ? emojiNames[1] ?? emojiNames[0]\n    : emojiNames[0];\n}\n\nconst styles = stylesheet.create({\n  emoji: {\n    '.': ClassNames.emoji,\n    position: 'relative',\n    width: 'var(--epr-emoji-fullsize)',\n    height: 'var(--epr-emoji-fullsize)',\n    boxSizing: 'border-box',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    borderRadius: '8px',\n    overflow: 'hidden',\n    transition: 'background-color 0.2s',\n    ':hover': {\n      backgroundColor: 'var(--epr-emoji-hover-color)'\n    },\n    ':focus': {\n      backgroundColor: 'var(--epr-focus-bg-color)'\n    }\n  },\n  noBackground: {\n    background: 'none',\n    ':hover': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    },\n    ':focus': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    }\n  },\n  hasVariations: {\n    '.': ClassNames.emojiHasVariations,\n    ':after': {\n      content: '',\n      display: 'block',\n      width: '0',\n      height: '0',\n      right: '0px',\n      bottom: '1px',\n      position: 'absolute',\n      borderLeft: '4px solid transparent',\n      borderRight: '4px solid transparent',\n      transform: 'rotate(135deg)',\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color)',\n      zIndex: 'var(--epr-emoji-variations-indictator-z-index)'\n    },\n    ':hover:after': {\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color-hover)'\n    }\n  }\n});\n", "import { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport const emojiStyles = stylesheet.create({\n  external: {\n    '.': ClassNames.external,\n    fontSize: '0'\n  },\n  common: {\n    alignSelf: 'center',\n    justifySelf: 'center',\n    display: 'block'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function EmojiImg({\n  emojiName,\n  style,\n  lazyLoad = false,\n  imgUrl,\n  onError,\n  className\n}: {\n  emojiName: string;\n  emojiStyle: EmojiStyle;\n  style: React.CSSProperties;\n  lazyLoad?: boolean;\n  imgUrl: string;\n    onError: () => void;\n  className?: string;\n}) {\n  return (\n    <img\n      src={imgUrl}\n      alt={emojiName}\n      className={cx(styles.emojiImag, emojiStyles.external, emojiStyles.common, className)}\n      loading={lazyLoad ? 'lazy' : 'eager'}\n      onError={onError}\n      style={style}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiImag: {\n    '.': 'epr-emoji-img',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    minWidth: 'var(--epr-emoji-fullsize)',\n    minHeight: 'var(--epr-emoji-fullsize)',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { parseNativeEmoji } from '../../dataUtils/parseNativeEmoji';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function NativeEmoji({\n  unified,\n  style,\n  className\n}: {\n  unified: string;\n  style: React.CSSProperties;\n  className?: string;\n}) {\n  return (\n    <span\n      className={cx(\n        styles.nativeEmoji,\n        emojiStyles.common,\n        emojiStyles.external,\n        className\n      )}\n      data-unified={unified}\n      style={style}\n    >\n      {parseNativeEmoji(unified)}\n    </span>\n  );\n}\n\nconst styles = stylesheet.create({\n  nativeEmoji: {\n    '.': 'epr-emoji-native',\n    fontFamily:\n      '\"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\"!important',\n    position: 'relative',\n    lineHeight: '100%',\n    fontSize: 'var(--epr-emoji-size)',\n    textAlign: 'center',\n    alignSelf: 'center',\n    justifySelf: 'center',\n    letterSpacing: '0',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import * as React from 'react';\n\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUrlByUnified\n} from '../../dataUtils/emojiSelectors';\nimport { isCustomEmoji } from '../../typeRefinements/typeRefinements';\nimport { EmojiStyle } from '../../types/exposedTypes';\nimport { useEmojisThatFailedToLoadState } from '../context/PickerContext';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { EmojiImg } from './EmojiImg';\nimport { NativeEmoji } from './NativeEmoji';\n\nexport function ViewOnlyEmoji({\n  emoji,\n  unified,\n  emojiStyle,\n  size,\n  lazyLoad,\n  getEmojiUrl = emojiUrlByUnified,\n  className\n}: BaseEmojiProps) {\n  const [, setEmojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n\n  const style = {} as React.CSSProperties;\n  if (size) {\n    style.width = style.height = style.fontSize = `${size}px`;\n  }\n\n  const emojiToRender = emoji ? emoji : emojiByUnified(unified);\n\n  if (!emojiToRender) {\n    return null;\n  }\n\n  if (isCustomEmoji(emojiToRender)) {\n    return (\n      <EmojiImg\n        style={style}\n        emojiName={unified}\n        emojiStyle={EmojiStyle.NATIVE}\n        lazyLoad={lazyLoad}\n        imgUrl={emojiToRender.imgUrl}\n        onError={onError}\n        className={className}\n      />\n    );\n  }\n\n  return (\n    <>\n      {emojiStyle === EmojiStyle.NATIVE ? (\n        <NativeEmoji unified={unified} style={style} className={className} />\n      ) : (\n        <EmojiImg\n          style={style}\n          emojiName={emojiName(emojiToRender)}\n          emojiStyle={emojiStyle}\n          lazyLoad={lazyLoad}\n          imgUrl={getEmojiUrl(unified, emojiStyle)}\n          onError={onError}\n          className={className}\n        />\n      )}\n    </>\n  );\n\n  function onError() {\n    setEmojisThatFailedToLoad(prev => new Set(prev).add(unified));\n  }\n}\n", "import * as React from 'react';\n\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiHasVariations, emojiNames } from '../../dataUtils/emojiSelectors';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { ClickableEmojiButton } from './ClickableEmojiButton';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\ntype ClickableEmojiProps = Readonly<\n  BaseEmojiProps & {\n    hidden?: boolean;\n    showVariations?: boolean;\n    hiddenOnSearch?: boolean;\n    emoji: DataEmoji;\n    className?: string;\n    noBackground?: boolean;\n  }\n>;\n\nexport function ClickableEmoji({\n  emoji,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  emojiStyle,\n  showVariations = true,\n  size,\n  lazyLoad,\n  getEmojiUrl,\n  className,\n  noBackground = false\n}: ClickableEmojiProps) {\n  const hasVariations = emojiHasVariations(emoji);\n\n  return (\n    <ClickableEmojiButton\n      hasVariations={hasVariations}\n      showVariations={showVariations}\n      hidden={hidden}\n      hiddenOnSearch={hiddenOnSearch}\n      emojiNames={emojiNames(emoji)}\n      unified={unified}\n      noBackground={noBackground}\n    >\n      <ViewOnlyEmoji\n        unified={unified}\n        emoji={emoji}\n        size={size}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoad}\n        getEmojiUrl={getEmojiUrl}\n        className={className}\n      />\n    </ClickableEmojiButton>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\nimport { useReactionsModeState } from '../context/PickerContext';\n\nimport Plus from './svg/plus.svg';\n\nexport function BtnPlus() {\n  const [, setReactionsMode] = useReactionsModeState();\n  return (\n    <Button\n      aria-label=\"Show all Emojis\"\n      title=\"Show all Emojis\"\n      tabIndex={0}\n      className={cx(styles.plusSign)}\n      onClick={() => setReactionsMode(false)}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  plusSign: {\n    fontSize: '20px',\n    padding: '17px',\n    color: 'var(--epr-text-color)',\n    borderRadius: '50%',\n    textAlign: 'center',\n    lineHeight: '100%',\n    width: '20px',\n    height: '20px',\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    transition: 'background-color 0.2s ease-in-out',\n    ':after': {\n      content: '',\n      minWidth: '20px',\n      minHeight: '20px',\n      backgroundImage: `url(${Plus})`,\n      backgroundColor: 'transparent',\n      backgroundRepeat: 'no-repeat',\n      backgroundSize: '20px',\n      backgroundPositionY: '0'\n    },\n    ':hover': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-20px'\n      }\n    },\n    ':focus': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-40px'\n      }\n    }\n  },\n  ...darkMode('plusSign', {\n    ':after': { backgroundPositionY: '-40px' },\n    ':hover:after': { backgroundPositionY: '-60px' }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonStyles, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useReactionsConfig,\n  useAllowExpandReactions,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useReactionsRef } from '../context/ElementRefContext';\nimport { useReactionsModeState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { BtnPlus } from './BtnPlus';\n\nexport function Reactions() {\n  const [reactionsOpen] = useReactionsModeState();\n  const ReactionsRef = useReactionsRef();\n  const reactions = useReactionsConfig();\n  useMouseDownHandlers(ReactionsRef, MOUSE_EVENT_SOURCE.REACTIONS);\n  const emojiStyle = useEmojiStyleConfig();\n  const allowExpandReactions = useAllowExpandReactions();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  if (!reactionsOpen) {\n    return null;\n  }\n\n  return (\n    <ul\n      className={cx(styles.list, !reactionsOpen && commonStyles.hidden)}\n      ref={ReactionsRef}\n    >\n      {reactions.map(reaction => (\n        <li key={reaction}>\n          <ClickableEmoji\n            emoji={emojiByUnified(reaction) as DataEmoji}\n            emojiStyle={emojiStyle}\n            unified={reaction}\n            showVariations={false}\n            className={cx(styles.emojiButton)}\n            noBackground\n            getEmojiUrl={getEmojiUrl}\n          />\n        </li>\n      ))}\n      {allowExpandReactions ? (\n        <li>\n          <BtnPlus />\n        </li>\n      ) : null}\n    </ul>\n  );\n}\n\nconst styles = stylesheet.create({\n  list: {\n    listStyle: 'none',\n    margin: '0',\n    padding: '0 5px',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    height: '100%'\n  },\n  emojiButton: {\n    ':hover': {\n      transform: 'scale(1.2)'\n    },\n    ':focus': {\n      transform: 'scale(1.2)'\n    },\n    ':active': {\n      transform: 'scale(1.1)'\n    },\n    transition: 'transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.5)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryFromCategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n  children?: React.ReactNode;\n  hidden?: boolean;\n  hiddenOnSearch?: boolean;\n}>;\n\nexport function EmojiCategory({\n  categoryConfig,\n  children,\n  hidden,\n  hiddenOnSearch\n}: Props) {\n  const category = categoryFromCategoryConfig(categoryConfig);\n  const categoryName = categoryNameFromCategoryConfig(categoryConfig);\n\n  return (\n    <li\n      className={cx(\n        styles.category,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch\n      )}\n      data-name={category}\n      aria-label={categoryName}\n    >\n      <h2 className={cx(styles.label)}>{categoryName}</h2>\n      <div className={cx(styles.categoryContent)}>{children}</div>\n    </li>\n  );\n}\n\nconst styles = stylesheet.create({\n  category: {\n    '.': ClassNames.category,\n    ':not(:has(.epr-visible))': {\n      display: 'none'\n    }\n  },\n  categoryContent: {\n    '.': ClassNames.categoryContent,\n    display: 'grid',\n    gridGap: '0',\n    gridTemplateColumns: 'repeat(auto-fill, var(--epr-emoji-fullsize))',\n    justifyContent: 'space-between',\n    margin: 'var(--epr-category-padding)',\n    position: 'relative'\n  },\n  label: {\n    '.': ClassNames.label,\n    alignItems: 'center',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(3px)',\n    backgroundColor: 'var(--epr-category-label-bg-color)',\n    color: 'var(--epr-category-label-text-color)',\n    display: 'flex',\n    fontSize: '16px',\n    fontWeight: 'bold',\n    height: 'var(--epr-category-label-height)',\n    margin: '0',\n    padding: 'var(--epr-category-label-padding)',\n    position: 'sticky',\n    textTransform: 'capitalize',\n    top: '0',\n    width: '100%',\n    zIndex: 'var(--epr-category-label-z-index)'\n  }\n});\n", "import * as React from 'react';\n\nlet isEverMounted = false;\n\nexport function useIsEverMounted() {\n  const [isMounted, setIsMounted] = React.useState(isEverMounted);\n\n  React.useEffect(() => {\n    setIsMounted(true);\n    isEverMounted = true;\n  }, []);\n\n  return isMounted || isEverMounted;\n}\n", "import * as React from 'react';\n\nimport { CategoryConfig } from '../../config/categoryConfig';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useSuggestedEmojisModeConfig\n} from '../../config/useConfig';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { getSuggested } from '../../dataUtils/suggested';\nimport { useIsEmojiDisallowed } from '../../hooks/useDisallowedEmojis';\nimport { useIsEverMounted } from '../../hooks/useIsEverMounted';\nimport { useUpdateSuggested } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n}>;\n\nexport function Suggested({ categoryConfig }: Props) {\n  const [suggestedUpdated] = useUpdateSuggested();\n  const isMounted = useIsEverMounted();\n  const suggestedEmojisModeConfig = useSuggestedEmojisModeConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const suggested = React.useMemo(\n    () => getSuggested(suggestedEmojisModeConfig) ?? [],\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [suggestedUpdated, suggestedEmojisModeConfig]\n  );\n  const emojiStyle = useEmojiStyleConfig();\n  const isEmojiDisallowed = useIsEmojiDisallowed();\n\n  if (!isMounted) {\n    return null;\n  }\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      hiddenOnSearch\n      hidden={suggested.length === 0}\n    >\n      {suggested.map(suggestedItem => {\n        const emoji = emojiByUnified(suggestedItem.original);\n\n        if (!emoji) {\n          return null;\n        }\n\n        if (isEmojiDisallowed(emoji)) {\n          return null;\n        }\n\n        return (\n          <ClickableEmoji\n            showVariations={false}\n            unified={suggestedItem.unified}\n            emojiStyle={emojiStyle}\n            emoji={emoji}\n            key={suggestedItem.unified}\n            getEmojiUrl={getEmojiUrl}\n          />\n        );\n      })}\n    </EmojiCategory>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  Categories,\n  CategoryConfig,\n  categoryFromCategoryConfig\n} from '../../config/categoryConfig';\nimport {\n  useCategoriesConfig,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useLazyLoadEmojisConfig,\n  useSkinTonesDisabledConfig\n} from '../../config/useConfig';\nimport { emojisByCategory, emojiUnified } from '../../dataUtils/emojiSelectors';\nimport { useIsEmojiDisallowed } from '../../hooks/useDisallowedEmojis';\nimport { useIsEmojiHidden } from '../../hooks/useIsEmojiHidden';\nimport {\n  useActiveSkinToneState,\n  useIsPastInitialLoad\n} from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\nimport { Suggested } from './Suggested';\n\nexport function EmojiList() {\n  const categories = useCategoriesConfig();\n  const renderdCategoriesCountRef = React.useRef(0);\n\n  return (\n    <ul className={cx(styles.emojiList)}>\n      {categories.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n\n        if (category === Categories.SUGGESTED) {\n          return <Suggested key={category} categoryConfig={categoryConfig} />;\n        }\n\n        return (\n          <React.Suspense key={category}>\n            <RenderCategory\n              category={category}\n              categoryConfig={categoryConfig}\n              renderdCategoriesCountRef={renderdCategoriesCountRef}\n            />\n          </React.Suspense>\n        );\n      })}\n    </ul>\n  );\n}\n\nfunction RenderCategory({\n  category,\n  categoryConfig,\n  renderdCategoriesCountRef\n}: {\n  category: Categories;\n  categoryConfig: CategoryConfig;\n  renderdCategoriesCountRef: React.MutableRefObject<number>;\n}) {\n  const isEmojiHidden = useIsEmojiHidden();\n  const lazyLoadEmojis = useLazyLoadEmojisConfig();\n  const emojiStyle = useEmojiStyleConfig();\n  const isPastInitialLoad = useIsPastInitialLoad();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const isEmojiDisallowed = useIsEmojiDisallowed();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const showVariations = !useSkinTonesDisabledConfig();\n\n  // Small trick to defer the rendering of all emoji categories until the first category is visible\n  // This way the user gets to actually see something and not wait for the whole picker to render.\n  const emojisToPush =\n    !isPastInitialLoad && renderdCategoriesCountRef.current > 0\n      ? []\n      : emojisByCategory(category);\n\n  if (emojisToPush.length > 0) {\n    renderdCategoriesCountRef.current++;\n  }\n\n  let hiddenCounter = 0;\n\n  const emojis = emojisToPush.map(emoji => {\n    const unified = emojiUnified(emoji, activeSkinTone);\n    const { failedToLoad, filteredOut, hidden } = isEmojiHidden(emoji);\n\n    const isDisallowed = isEmojiDisallowed(emoji);\n\n    if (hidden || isDisallowed) {\n      hiddenCounter++;\n    }\n\n    if (isDisallowed) {\n      return null;\n    }\n\n    return (\n      <ClickableEmoji\n        showVariations={showVariations}\n        key={unified}\n        emoji={emoji}\n        unified={unified}\n        hidden={failedToLoad}\n        hiddenOnSearch={filteredOut}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoadEmojis}\n        getEmojiUrl={getEmojiUrl}\n      />\n    );\n  });\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      // Indicates that there are no visible emojis\n      // Hence, the category should be hidden\n      hidden={hiddenCounter === emojis.length}\n    >\n      {emojis}\n    </EmojiCategory>\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiList: {\n    '.': ClassNames.emojiList,\n    listStyle: 'none',\n    margin: '0',\n    padding: '0'\n  }\n});\n", "import { useEmojisThatFailedToLoadState } from '../components/context/PickerContext';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified } from '../dataUtils/emojiSelectors';\n\nimport { useIsEmojiFiltered } from './useFilter';\n\nexport function useIsEmojiHidden(): (emoji: DataEmoji) => IsHiddenReturn {\n  const [emojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n  const isEmojiFiltered = useIsEmojiFiltered();\n\n  return (emoji: DataEmoji): IsHiddenReturn => {\n    const unified = emojiUnified(emoji);\n\n    const failedToLoad = emojisThatFailedToLoad.has(unified);\n    const filteredOut = isEmojiFiltered(unified);\n\n    return {\n      failedToLoad,\n      filteredOut,\n      hidden: failedToLoad || filteredOut\n    };\n  };\n}\n\ntype IsHiddenReturn = {\n  failedToLoad: boolean;\n  filteredOut: boolean;\n  hidden: boolean;\n};\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { focusFirstVisibleEmoji } from '../../DomUtils/keyboardNavigation';\nimport {\n  buttonFromTarget,\n  elementHeight,\n  emojiTrueOffsetTop,\n  emojiTruOffsetLeft\n} from '../../DomUtils/selectors';\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport {\n  emojiHasVariations,\n  emojiUnified,\n  emojiVariations\n} from '../../dataUtils/emojiSelectors';\nimport {\n  useAnchoredEmojiRef,\n  useBodyRef,\n  useSetAnchoredEmojiRef,\n  useVariationPickerRef\n} from '../context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport SVGTriangle from './svg/triangle.svg';\n\nenum Direction {\n  Up,\n  Down\n}\n\n// eslint-disable-next-line complexity\nexport function EmojiVariationPicker() {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const VariationPickerRef = useVariationPickerRef();\n  const [emoji] = useEmojiVariationPickerState();\n  const emojiStyle = useEmojiStyleConfig();\n\n  const { getTop, getMenuDirection } = useVariationPickerTop(\n    VariationPickerRef\n  );\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const getPointerStyle = usePointerStyle(VariationPickerRef);\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n  const visible = Boolean(\n    emoji &&\n      button &&\n      emojiHasVariations(emoji) &&\n      button.classList.contains(ClassNames.emojiHasVariations)\n  );\n\n  useEffect(() => {\n    if (!visible) {\n      return;\n    }\n\n    focusFirstVisibleEmoji(VariationPickerRef.current);\n  }, [VariationPickerRef, visible, AnchoredEmojiRef]);\n\n  let top, pointerStyle;\n\n  if (!visible && AnchoredEmojiRef.current) {\n    setAnchoredEmojiRef(null);\n  } else {\n    top = getTop();\n    pointerStyle = getPointerStyle();\n  }\n\n  return (\n    <div\n      ref={VariationPickerRef}\n      className={cx(\n        styles.variationPicker,\n        getMenuDirection() === Direction.Down && styles.pointingUp,\n        visible && styles.visible\n      )}\n      style={{ top }}\n    >\n      {visible && emoji\n        ? [emojiUnified(emoji)]\n            .concat(emojiVariations(emoji))\n            .slice(0, 6)\n            .map(unified => (\n              <ClickableEmoji\n                key={unified}\n                emoji={emoji}\n                unified={unified}\n                emojiStyle={emojiStyle}\n                showVariations={false}\n                getEmojiUrl={getEmojiUrl}\n              />\n            ))\n        : null}\n      <div className={cx(styles.pointer)} style={pointerStyle} />\n    </div>\n  );\n}\n\nfunction usePointerStyle(VariationPickerRef: React.RefObject<HTMLElement>) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return function getPointerStyle() {\n    const style: React.CSSProperties = {};\n    if (!VariationPickerRef.current) {\n      return style;\n    }\n\n    if (AnchoredEmojiRef.current) {\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const offsetLeft = emojiTruOffsetLeft(button);\n\n      if (!button) {\n        return style;\n      }\n\n      // half of the button\n      style.left = offsetLeft + button?.clientWidth / 2;\n    }\n\n    return style;\n  };\n}\n\nfunction useVariationPickerTop(\n  VariationPickerRef: React.RefObject<HTMLElement>\n) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const BodyRef = useBodyRef();\n  let direction = Direction.Up;\n\n  return {\n    getMenuDirection,\n    getTop\n  };\n\n  function getMenuDirection() {\n    return direction;\n  }\n\n  function getTop() {\n    direction = Direction.Up;\n    let emojiOffsetTop = 0;\n\n    if (!VariationPickerRef.current) {\n      return 0;\n    }\n\n    const height = elementHeight(VariationPickerRef.current);\n\n    if (AnchoredEmojiRef.current) {\n      const bodyRef = BodyRef.current;\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const buttonHeight = elementHeight(button);\n\n      emojiOffsetTop = emojiTrueOffsetTop(button);\n\n      const scrollTop = bodyRef?.scrollTop ?? 0;\n\n      if (scrollTop > emojiOffsetTop - height) {\n        direction = Direction.Down;\n        emojiOffsetTop += buttonHeight + height;\n      }\n    }\n\n    return emojiOffsetTop - height;\n  }\n}\n\nconst styles = stylesheet.create({\n  variationPicker: {\n    '.': ClassNames.variationPicker,\n    position: 'absolute',\n    right: '15px',\n    left: '15px',\n    padding: '5px',\n    boxShadow: '0px 2px 5px rgba(0, 0, 0, 0.2)',\n    borderRadius: '3px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-around',\n    opacity: '0',\n    visibility: 'hidden',\n    pointerEvents: 'none',\n    top: '-100%',\n    border: '1px solid var(--epr-picker-border-color)',\n    height: 'var(--epr-emoji-variation-picker-height)',\n    zIndex: 'var(--epr-skin-variation-picker-z-index)',\n    background: 'var(--epr-emoji-variation-picker-bg-color)',\n    transform: 'scale(0.9)',\n    transition: 'transform 0.1s ease-out, opacity 0.2s ease-out'\n  },\n  visible: {\n    opacity: '1',\n    visibility: 'visible',\n    pointerEvents: 'all',\n    transform: 'scale(1)'\n  },\n  pointingUp: {\n    '.': 'pointing-up',\n    transformOrigin: 'center 0%',\n    transform: 'scale(0.9)'\n  },\n  '.pointing-up': {\n    pointer: {\n      top: '0',\n      transform: 'rotate(180deg) translateY(100%) translateX(18px)'\n    }\n  },\n  pointer: {\n    '.': 'epr-emoji-pointer',\n    content: '',\n    position: 'absolute',\n    width: '25px',\n    height: '15px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '50px 15px',\n    top: '100%',\n    transform: 'translateX(-18px)',\n    backgroundImage: `url(${SVGTriangle})`\n  },\n  ...darkMode('pointer', {\n    backgroundPosition: '-25px 0'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { MOUSE_EVENT_SOURCE } from '../../config/useConfig';\nimport { useOnMouseMove } from '../../hooks/useDisallowMouseMove';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useOnScroll } from '../../hooks/useOnScroll';\nimport { useBodyRef } from '../context/ElementRefContext';\n\nimport { EmojiList } from './EmojiList';\nimport { EmojiVariationPicker } from './EmojiVariationPicker';\n\nexport function Body() {\n  const BodyRef = useBodyRef();\n  useOnScroll(BodyRef);\n  useMouseDownHandlers(BodyRef, MOUSE_EVENT_SOURCE.PICKER);\n  useOnMouseMove();\n\n  return (\n    <div\n      className={cx(styles.body, commonInteractionStyles.hiddenOnReactions)}\n      ref={BodyRef}\n    >\n      <EmojiVariationPicker />\n      <EmojiList />\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  body: {\n    '.': ClassNames.scrollBody,\n    flex: '1',\n    overflowY: 'scroll',\n    overflowX: 'hidden',\n    position: 'relative'\n  }\n});\n", "import { useEffect } from 'react';\n\nimport { ElementRef } from '../components/context/ElementRefContext';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\n\nexport function useOnScroll(BodyRef: ElementRef) {\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    if (!bodyRef) {\n      return;\n    }\n\n    bodyRef.addEventListener('scroll', onScroll, {\n      passive: true\n    });\n\n    function onScroll() {\n      closeAllOpenToggles();\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('scroll', onScroll);\n    };\n  }, [BodyRef, closeAllOpenToggles]);\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport enum FlexDirection {\n  ROW = 'FlexRow',\n  COLUMN = 'FlexColumn'\n}\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n  direction?: FlexDirection;\n}>;\n\nexport default function Flex({\n  children,\n  className,\n  style = {},\n  direction = FlexDirection.ROW\n}: Props) {\n  return (\n    <div\n      style={{ ...style }}\n      className={cx(styles.flex, className, styles[direction])}\n    >\n      {children}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  flex: {\n    display: 'flex'\n  },\n  [FlexDirection.ROW]: {\n    flexDirection: 'row'\n  },\n  [FlexDirection.COLUMN]: {\n    flexDirection: 'column'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\n\ntype Props = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Space({ className, style = {} }: Props) {\n  return <div style={{ flex: 1, ...style }} className={cx(className)} />;\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Absolute({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'absolute' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Relative({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'relative' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport { skinTonesNamed } from '../../../data/skinToneVariations';\nimport { SkinTones } from '../../../types/exposedTypes';\nimport { Button } from '../../atoms/Button';\n\ntype Props = {\n  isOpen: boolean;\n  onClick: () => void;\n  isActive: boolean;\n  skinToneVariation: SkinTones;\n  style?: React.CSSProperties;\n};\n\n// eslint-disable-next-line complexity\nexport function BtnSkinToneVariation({\n  isOpen,\n  onClick,\n  isActive,\n  skinToneVariation,\n  style\n}: Props) {\n  return (\n    <Button\n      style={style}\n      onClick={onClick}\n      className={cx(\n        `epr-tone-${skinToneVariation}`,\n        styles.tone,\n        !isOpen && styles.closedTone,\n        isActive && styles.active\n      )}\n      aria-pressed={isActive}\n      aria-label={`Skin tone ${skinTonesNamed[skinToneVariation as SkinTones]}`}\n    ></Button>\n  );\n}\n\nconst styles = stylesheet.create({\n  closedTone: {\n    opacity: '0',\n    zIndex: '0'\n  },\n  active: {\n    '.': 'epr-active',\n    zIndex: '1',\n    opacity: '1'\n  },\n  tone: {\n    '.': 'epr-tone',\n    width: 'var(--epr-skin-tone-size)',\n    display: 'block',\n    cursor: 'pointer',\n    borderRadius: '4px',\n    height: 'var(--epr-skin-tone-size)',\n    position: 'absolute',\n    right: '0',\n    transition: 'transform 0.3s ease-in-out, opacity 0.35s ease-in-out',\n    zIndex: '0',\n    border: '1px solid var(--epr-skin-tone-outer-border-color)',\n    boxShadow: 'inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)',\n    ':hover': {\n      boxShadow: '0 0 0 3px var(--epr-active-skin-hover-color), inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)'\n    },\n    ':focus': {\n      boxShadow: '0 0 0 3px var(--epr-focus-bg-color)'\n    },\n    '&.epr-tone-neutral': {\n      backgroundColor: '#ffd225'\n    },\n    '&.epr-tone-1f3fb': {\n      backgroundColor: '#ffdfbd'\n    },\n    '&.epr-tone-1f3fc': {\n      backgroundColor: '#e9c197'\n    },\n    '&.epr-tone-1f3fd': {\n      backgroundColor: '#c88e62'\n    },\n    '&.epr-tone-1f3fe': {\n      backgroundColor: '#a86637'\n    },\n    '&.epr-tone-1f3ff': {\n      backgroundColor: '#60463a'\n    }\n  }\n});\n", "/* eslint-disable complexity */\nimport { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../../DomUtils/classNames';\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useOnSkinToneChangeConfig,\n  useSkinTonesDisabledConfig\n} from '../../../config/useConfig';\nimport skinToneVariations from '../../../data/skinToneVariations';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFocusSearchInput } from '../../../hooks/useFocus';\nimport Absolute from '../../Layout/Absolute';\nimport Relative from '../../Layout/Relative';\nimport { useSkinTonePickerRef } from '../../context/ElementRefContext';\nimport {\n  useActiveSkinToneState,\n  useSkinToneFanOpenState\n} from '../../context/PickerContext';\n\nimport { BtnSkinToneVariation } from './BtnSkinToneVariation';\n\nconst ITEM_SIZE = 28;\n\ntype Props = {\n  direction?: SkinTonePickerDirection;\n};\n\nexport function SkinTonePickerMenu() {\n  return (\n    <Relative style={{ height: ITEM_SIZE }}>\n      <Absolute style={{ bottom: 0, right: 0 }}>\n        <SkinTonePicker direction={SkinTonePickerDirection.VERTICAL} />\n      </Absolute>\n    </Relative>\n  );\n}\n\nexport function SkinTonePicker({\n  direction = SkinTonePickerDirection.HORIZONTAL\n}: Props) {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const isDisabled = useSkinTonesDisabledConfig();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const [activeSkinTone, setActiveSkinTone] = useActiveSkinToneState();\n  const onSkinToneChange = useOnSkinToneChangeConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const focusSearchInput = useFocusSearchInput();\n\n  if (isDisabled) {\n    return null;\n  }\n\n  const fullWidth = `${ITEM_SIZE * skinToneVariations.length}px`;\n\n  const expandedSize = isOpen ? fullWidth : ITEM_SIZE + 'px';\n\n  const vertical = direction === SkinTonePickerDirection.VERTICAL;\n\n  return (\n    <Relative\n      className={cx(\n        styles.skinTones,\n        vertical && styles.vertical,\n        isOpen && styles.open,\n        vertical && isOpen && styles.verticalShadow\n      )}\n      style={\n        vertical\n          ? { flexBasis: expandedSize, height: expandedSize }\n          : { flexBasis: expandedSize }\n      }\n    >\n      <div className={cx(styles.select)} ref={SkinTonePickerRef}>\n        {skinToneVariations.map((skinToneVariation, i) => {\n          const active = skinToneVariation === activeSkinTone;\n\n          return (\n            <BtnSkinToneVariation\n              key={skinToneVariation}\n              skinToneVariation={skinToneVariation}\n              isOpen={isOpen}\n              style={{\n                transform: cx(\n                  vertical\n                    ? `translateY(-${i * (isOpen ? ITEM_SIZE : 0)}px)`\n                    : `translateX(-${i * (isOpen ? ITEM_SIZE : 0)}px)`,\n                  isOpen && active && 'scale(1.3)'\n                )\n              }}\n              isActive={active}\n              onClick={() => {\n                if (isOpen) {\n                  setActiveSkinTone(skinToneVariation);\n                  onSkinToneChange(skinToneVariation);\n                  focusSearchInput();\n                } else {\n                  setIsOpen(true);\n                }\n                closeAllOpenToggles();\n              }}\n            />\n          );\n        })}\n      </div>\n    </Relative>\n  );\n}\n\nexport enum SkinTonePickerDirection {\n  VERTICAL = ClassNames.vertical,\n  HORIZONTAL = ClassNames.horizontal\n}\n\nconst styles = stylesheet.create({\n  skinTones: {\n    '.': 'epr-skin-tones',\n    '--': {\n      '--epr-skin-tone-size': '15px'\n    },\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'flex-end',\n    transition: 'all 0.3s ease-in-out',\n    padding: '10px 0'\n  },\n  vertical: {\n    padding: '9px',\n    alignItems: 'flex-end',\n    flexDirection: 'column',\n    borderRadius: '6px',\n    border: '1px solid var(--epr-bg-color)'\n  },\n  verticalShadow: {\n    boxShadow: '0px 0 7px var(--epr-picker-border-color)'\n  },\n  open: {\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(5px)',\n    background: 'var(--epr-skin-tone-picker-menu-color)',\n    '.epr-active': {\n      border: '1px solid var(--epr-active-skin-tone-indicator-border-color)'\n    }\n  },\n  select: {\n    '.': 'epr-skin-tone-select',\n    position: 'relative',\n    width: 'var(--epr-skin-tone-size)',\n    height: 'var(--epr-skin-tone-size)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  usePreviewConfig\n} from '../../config/useConfig';\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUnified\n} from '../../dataUtils/emojiSelectors';\nimport { useEmojiPreviewEvents } from '../../hooks/useEmojiPreviewEvents';\nimport { useIsSkinToneInPreview } from '../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../Layout/Flex';\nimport Space from '../Layout/Space';\nimport {\n  useEmojiVariationPickerState,\n  useReactionsModeState\n} from '../context/PickerContext';\nimport { ViewOnlyEmoji } from '../emoji/ViewOnlyEmoji';\nimport { SkinTonePickerMenu } from '../header/SkinTonePicker/SkinTonePicker';\n\nexport function Preview() {\n  const previewConfig = usePreviewConfig();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n  const [reactionsOpen] = useReactionsModeState();\n\n  if (!previewConfig.showPreview) {\n    return null;\n  }\n\n  return (\n    <Flex\n      className={cx(\n        styles.preview,\n        commonInteractionStyles.hiddenOnReactions,\n        reactionsOpen && styles.hideOnReactions\n      )}\n    >\n      <PreviewBody />\n      <Space />\n      {isSkinToneInPreview ? <SkinTonePickerMenu /> : null}\n    </Flex>\n  );\n}\n\nexport function PreviewBody() {\n  const previewConfig = usePreviewConfig();\n  const [previewEmoji, setPreviewEmoji] = useState<PreviewEmoji>(null);\n  const emojiStyle = useEmojiStyleConfig();\n  const [variationPickerEmoji] = useEmojiVariationPickerState();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEmojiPreviewEvents(previewConfig.showPreview, setPreviewEmoji);\n\n  const emoji = emojiByUnified(\n    previewEmoji?.unified ?? previewEmoji?.originalUnified\n  );\n\n  const show = emoji != null && previewEmoji != null;\n\n  return <PreviewContent />;\n\n  function PreviewContent() {\n    const defaultEmoji =\n      variationPickerEmoji ?? emojiByUnified(previewConfig.defaultEmoji);\n    if (!defaultEmoji) {\n      return null;\n    }\n    const defaultText = variationPickerEmoji\n      ? emojiName(variationPickerEmoji)\n      : previewConfig.defaultCaption;\n\n    return (\n      <>\n        <div>\n          {show ? (\n            <ViewOnlyEmoji\n              unified={previewEmoji?.unified as string}\n              emoji={emoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : defaultEmoji ? (\n            <ViewOnlyEmoji\n              unified={emojiUnified(defaultEmoji)}\n              emoji={defaultEmoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : null}\n        </div>\n        <div className={cx(styles.label)}>\n          {show ? emojiName(emoji) : defaultText}\n        </div>\n      </>\n    );\n  }\n}\n\nexport type PreviewEmoji = null | {\n  unified: string;\n  originalUnified: string;\n};\n\nconst styles = stylesheet.create({\n  preview: {\n    alignItems: 'center',\n    borderTop: '1px solid var(--epr-preview-border-color)',\n    height: 'var(--epr-preview-height)',\n    padding: '0 var(--epr-horizontal-padding)',\n    position: 'relative',\n    zIndex: 'var(--epr-preview-z-index)'\n  },\n  label: {\n    color: 'var(--epr-preview-text-color)',\n    fontSize: 'var(--epr-preview-text-size)',\n    padding: 'var(--epr-preview-text-padding)',\n    textTransform: 'capitalize'\n  },\n  emoji: {\n    padding: '0'\n  },\n  hideOnReactions: {\n    opacity: '0',\n    transition: 'opacity 0.5s ease-in-out'\n  }\n});\n", "import * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { detectEmojyPartiallyBelowFold } from '../DomUtils/detectEmojyPartiallyBelowFold';\nimport { focusElement } from '../DomUtils/focusElement';\nimport {\n  allUnifiedFromEmojiElement,\n  buttonFromTarget\n} from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { PreviewEmoji } from '../components/footer/Preview';\n\nimport {\n  useAllowMouseMove,\n  useIsMouseDisallowed\n} from './useDisallowMouseMove';\n\nexport function useEmojiPreviewEvents(\n  allow: boolean,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const BodyRef = useBodyRef();\n  const isMouseDisallowed = useIsMouseDisallowed();\n  const allowMouseMove = useAllowMouseMove();\n\n  useEffect(() => {\n    if (!allow) {\n      return;\n    }\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('keydown', onEscape, {\n      passive: true\n    });\n\n    bodyRef?.addEventListener('mouseover', onMouseOver, true);\n\n    bodyRef?.addEventListener('focus', onEnter, true);\n\n    bodyRef?.addEventListener('mouseout', onLeave, {\n      passive: true\n    });\n    bodyRef?.addEventListener('blur', onLeave, true);\n\n    function onEnter(e: FocusEvent) {\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (!button) {\n        return onLeave();\n      }\n\n      const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n      if (!unified || !originalUnified) {\n        return onLeave();\n      }\n\n      setPreviewEmoji({\n        unified,\n        originalUnified\n      });\n    }\n    function onLeave(e?: FocusEvent | MouseEvent) {\n      if (e) {\n        const relatedTarget = e.relatedTarget as HTMLElement;\n\n        if (!buttonFromTarget(relatedTarget)) {\n          return setPreviewEmoji(null);\n        }\n      }\n\n      setPreviewEmoji(null);\n    }\n    function onEscape(e: KeyboardEvent) {\n      if (e.key === 'Escape') {\n        setPreviewEmoji(null);\n      }\n    }\n\n    function onMouseOver(e: MouseEvent) {\n      if (isMouseDisallowed()) {\n        return;\n      }\n\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (button) {\n        const belowFoldByPx = detectEmojyPartiallyBelowFold(button, bodyRef);\n        const buttonHeight = button.getBoundingClientRect().height;\n        if (belowFoldByPx < buttonHeight) {\n          return handlePartiallyVisibleElementFocus(button, setPreviewEmoji);\n        }\n\n        focusElement(button);\n      }\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('mouseover', onMouseOver);\n      bodyRef?.removeEventListener('mouseout', onLeave);\n      bodyRef?.removeEventListener('focus', onEnter, true);\n      bodyRef?.removeEventListener('blur', onLeave, true);\n      bodyRef?.removeEventListener('keydown', onEscape);\n    };\n  }, [BodyRef, allow, setPreviewEmoji, isMouseDisallowed, allowMouseMove]);\n}\n\nfunction handlePartiallyVisibleElementFocus(\n  button: HTMLElement,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n  if (!unified || !originalUnified) {\n    return;\n  }\n\n  (document.activeElement as HTMLElement)?.blur?.();\n\n  setPreviewEmoji({\n    unified,\n    originalUnified\n  });\n}\n", "import { NullableElement } from './selectors';\n\nexport function detectEmojyPartiallyBelowFold(\n  button: HTMLButtonElement,\n  bodyRef: NullableElement\n): number {\n  if (!button || !bodyRef) {\n    return 0;\n  }\n\n  const buttonRect = button.getBoundingClientRect();\n  const bodyRect = bodyRef.getBoundingClientRect();\n\n  // If the element is obscured by at least half of its size\n  return bodyRect.height - (buttonRect.y - bodyRect.y);\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\nimport { Button } from '../atoms/Button';\n\nimport SVGNavigation from './svg/CategoryNav.svg';\n\ntype Props = {\n  isActiveCategory: boolean;\n  category: string;\n  allowNavigation: boolean;\n  onClick: () => void;\n  categoryConfig: CategoryConfig;\n};\n\nexport function CategoryButton({\n  isActiveCategory,\n  category,\n  allowNavigation,\n  categoryConfig,\n  onClick\n}: Props) {\n  return (\n    <Button\n      tabIndex={allowNavigation ? 0 : -1}\n      className={cx(\n        styles.catBtn,\n        commonInteractionStyles.categoryBtn,\n        `epr-icn-${category}`,\n        {\n          [ClassNames.active]: isActiveCategory\n        }\n      )}\n      onClick={onClick}\n      aria-label={categoryNameFromCategoryConfig(categoryConfig)}\n      aria-selected={isActiveCategory}\n      role=\"tab\"\n      aria-controls=\"epr-category-nav-id\"\n    />\n  );\n}\n\nconst DarkActivePositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 3)'\n};\nconst DarkPositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 2)'\n};\n\nconst DarkInactivePosition = {\n  ':not(.epr-search-active)': {\n    catBtn: {\n      ':hover': DarkActivePositionY,\n      '&.epr-active': DarkActivePositionY\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  catBtn: {\n    '.': 'epr-cat-btn',\n    display: 'inline-block',\n    transition: 'opacity 0.2s ease-in-out',\n    position: 'relative',\n    height: 'var(--epr-category-navigation-button-size)',\n    width: 'var(--epr-category-navigation-button-size)',\n    backgroundSize: 'calc(var(--epr-category-navigation-button-size) * 10)',\n    outline: 'none',\n    backgroundPosition: '0 0',\n    backgroundImage: `url(${SVGNavigation})`,\n    ':focus:before': {\n      content: '',\n      position: 'absolute',\n      top: '-2px',\n      left: '-2px',\n      right: '-2px',\n      bottom: '-2px',\n      border: '2px solid var(--epr-category-icon-active-color)',\n      borderRadius: '50%'\n    },\n    '&.epr-icn-suggested': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -8)'\n    },\n    '&.epr-icn-custom': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -9)'\n    },\n    '&.epr-icn-activities': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -4)'\n    },\n    '&.epr-icn-animals_nature': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -1)'\n    },\n    '&.epr-icn-flags': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -7)'\n    },\n    '&.epr-icn-food_drink': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -2)'\n    },\n    '&.epr-icn-objects': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -5)'\n    },\n    '&.epr-icn-smileys_people': {\n      backgroundPositionX: '0px'\n    },\n    '&.epr-icn-symbols': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -6)'\n    },\n    '&.epr-icn-travel_places': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -3)'\n    }\n  },\n  ...darkMode('catBtn', DarkPositionY),\n  '.epr-dark-theme': {\n    ...DarkInactivePosition\n  },\n  '.epr-auto-theme': {\n    ...DarkInactivePosition\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { categoryFromCategoryConfig } from '../../config/categoryConfig';\nimport { useCategoriesConfig } from '../../config/useConfig';\nimport { useActiveCategoryScrollDetection } from '../../hooks/useActiveCategoryScrollDetection';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useScrollCategoryIntoView } from '../../hooks/useScrollCategoryIntoView';\nimport { useShouldHideCustomEmojis } from '../../hooks/useShouldHideCustomEmojis';\nimport { isCustomCategory } from '../../typeRefinements/typeRefinements';\nimport { useCategoryNavigationRef } from '../context/ElementRefContext';\n\nimport { CategoryButton } from './CategoryButton';\n\nexport function CategoryNavigation() {\n  const [activeCategory, setActiveCategory] = useState<string | null>(null);\n  const scrollCategoryIntoView = useScrollCategoryIntoView();\n  useActiveCategoryScrollDetection(setActiveCategory);\n  const isSearchMode = useIsSearchMode();\n\n  const categoriesConfig = useCategoriesConfig();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const hideCustomCategory = useShouldHideCustomEmojis();\n\n  return (\n    <div\n      className={cx(styles.nav)}\n      role=\"tablist\"\n      aria-label=\"Category navigation\"\n      id=\"epr-category-nav-id\"\n      ref={CategoryNavigationRef}\n    >\n      {categoriesConfig.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n        const isActiveCategory = category === activeCategory;\n\n        if (isCustomCategory(categoryConfig) && hideCustomCategory) {\n          return null;\n        }\n\n        const allowNavigation = !isSearchMode && !isActiveCategory;\n\n        return (\n          <CategoryButton\n            key={category}\n            category={category}\n            isActiveCategory={isActiveCategory}\n            allowNavigation={allowNavigation}\n            categoryConfig={categoryConfig}\n            onClick={() => {\n              scrollCategoryIntoView(category);\n              setTimeout(() => {\n                setActiveCategory(category);\n              }, 10);\n            }}\n          />\n        );\n      })}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  nav: {\n    '.': 'epr-category-nav',\n    display: 'flex',\n    flexDirection: 'row',\n    justifyContent: 'space-around',\n    padding: 'var(--epr-header-padding)'\n  },\n  '.epr-search-active': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  },\n  '.epr-main:has(input:not(:placeholder-shown))': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  }\n});\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport { NullableElement } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  usePickerMainRef\n} from '../components/context/ElementRefContext';\n\nexport function useScrollCategoryIntoView() {\n  const BodyRef = useBodyRef();\n  const PickerMainRef = usePickerMainRef();\n\n  return function scrollCategoryIntoView(category: string): void {\n    if (!BodyRef.current) {\n      return;\n    }\n    const $category = BodyRef.current?.querySelector(\n      `[data-name=\"${category}\"]`\n    ) as NullableElement;\n\n    if (!$category) {\n      return;\n    }\n\n    const offsetTop = $category.offsetTop || 0;\n\n    scrollTo(PickerMainRef.current, offsetTop);\n  };\n}\n", "import { useEffect } from 'react';\n\nimport { categoryNameFromDom } from '../DomUtils/categoryNameFromDom';\nimport { asSelectors, ClassNames } from '../DomUtils/classNames';\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nexport function useActiveCategoryScrollDetection(\n  setActiveCategory: (category: string) => void\n) {\n  const BodyRef = useBodyRef();\n\n  useEffect(() => {\n    const visibleCategories = new Map();\n    const bodyRef = BodyRef.current;\n    const observer = new IntersectionObserver(\n      entries => {\n        if (!bodyRef) {\n          return;\n        }\n\n        for (const entry of entries) {\n          const id = categoryNameFromDom(entry.target);\n          visibleCategories.set(id, entry.intersectionRatio);\n        }\n\n        const ratios = Array.from(visibleCategories);\n        const lastCategory = ratios[ratios.length - 1];\n\n        if (lastCategory[1] == 1) {\n          return setActiveCategory(lastCategory[0]);\n        }\n\n        for (const [id, ratio] of ratios) {\n          if (ratio) {\n            setActiveCategory(id);\n            break;\n          }\n        }\n      },\n      {\n        threshold: [0, 1]\n      }\n    );\n    bodyRef?.querySelectorAll(asSelectors(ClassNames.category)).forEach(el => {\n      observer.observe(el);\n    });\n  }, [BodyRef, setActiveCategory]);\n}\n", "export function categoryNameFromDom($category: Element | null): string | null {\n  return $category?.getAttribute('data-name') ?? null;\n}\n", "import { useCustomEmojisConfig } from '../config/useConfig';\n\nexport function useShouldHideCustomEmojis() {\n  const customCategoryConfig = useCustomEmojisConfig();\n\n  if (!customCategoryConfig) {\n    return false;\n  }\n\n  return customCategoryConfig.length === 0;\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../../Stylesheet/stylesheet';\nimport { useClearSearch } from '../../../hooks/useFilter';\nimport { Button } from '../../atoms/Button';\n\nimport SVGTimes from './svg/times.svg';\n\nexport function BtnClearSearch() {\n  const clearSearch = useClearSearch();\n\n  return (\n    <Button\n      className={cx(\n        styles.btnClearSearch,\n        commonInteractionStyles.visibleOnSearchOnly\n      )}\n      onClick={clearSearch}\n      aria-label=\"Clear\"\n      title=\"Clear\"\n    >\n      <div className={cx(styles.icnClearnSearch)} />\n    </Button>\n  );\n}\n\nconst HoverDark = {\n  ':hover': {\n    '> .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', HoverDark)\n});\n", "import * as React from 'react';\n\nimport { ClassNames, asSelectors } from '../../../DomUtils/classNames';\nimport { getNormalizedSearchTerm } from '../../../hooks/useFilter';\n\nconst SCOPE = `${asSelectors(ClassNames.emojiPicker)} ${asSelectors(\n  ClassNames.emojiList\n)}`;\n\nconst EMOJI_BUTTON = ['button', asSelectors(ClassNames.emoji)].join('');\nconst CATEGORY = asSelectors(ClassNames.category);\n\nexport function CssSearch({ value }: { value: undefined | string }) {\n  if (!value) {\n    return null;\n  }\n\n  const q = genQuery(value);\n\n  return (\n    <style>{`\n    ${SCOPE} ${EMOJI_BUTTON} {\n      display: none;\n    }\n\n\n    ${SCOPE} ${q} {\n      display: flex;\n    }\n\n    ${SCOPE} ${CATEGORY}:not(:has(${q})) {\n      display: none;\n    }\n  `}</style>\n  );\n}\n\nfunction genQuery(value: string): string {\n  return [\n    EMOJI_BUTTON,\n    '[data-full-name*=\"',\n    getNormalizedSearchTerm(value),\n    '\"]'\n  ].join('');\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\n\nimport SVGMagnifier from './svg/magnifier.svg';\n\nexport function IcnSearch() {\n  return <div className={cx(styles.icnSearch)} />;\n}\n\nconst styles = stylesheet.create({\n  icnSearch: {\n    '.': 'epr-icn-search',\n    content: '',\n    position: 'absolute',\n    top: '50%',\n    left: 'var(--epr-search-bar-inner-padding)',\n    transform: 'translateY(-50%)',\n    width: '20px',\n    height: '20px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '20px',\n    backgroundImage: `url(${SVGMagnifier})`\n  },\n  ...darkMode('icnSearch', {\n    backgroundPositionY: '-20px'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useAutoFocusSearchConfig,\n  useSearchDisabledConfig,\n  useSearchPlaceHolderConfig\n} from '../../../config/useConfig';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFilter } from '../../../hooks/useFilter';\nimport { useIsSkinToneInSearch } from '../../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../../Layout/Flex';\nimport Relative from '../../Layout/Relative';\nimport { useSearchInputRef } from '../../context/ElementRefContext';\nimport { SkinTonePicker } from '../SkinTonePicker/SkinTonePicker';\n\nimport { BtnClearSearch } from './BtnClearSearch';\nimport { CssSearch } from './CssSearch';\nimport { IcnSearch } from './IcnSearch';\nimport SVGTimes from './svg/times.svg';\n\nexport function SearchContainer() {\n  const searchDisabled = useSearchDisabledConfig();\n\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  if (searchDisabled) {\n    return null;\n  }\n\n  return (\n    <Flex className={cx(styles.overlay)}>\n      <Search />\n\n      {isSkinToneInSearch ? <SkinTonePicker /> : null}\n    </Flex>\n  );\n}\n\nexport function Search() {\n  const [inc, setInc] = useState(0);\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const SearchInputRef = useSearchInputRef();\n  const placeholder = useSearchPlaceHolderConfig();\n  const autoFocus = useAutoFocusSearchConfig();\n  const { statusSearchResults, searchTerm, onChange } = useFilter();\n\n  const input = SearchInputRef?.current;\n  const value = input?.value;\n\n  return (\n    <Relative className={cx(styles.searchContainer)}>\n      <CssSearch value={value} />\n      <input\n        // eslint-disable-next-line jsx-a11y/no-autofocus\n        autoFocus={autoFocus}\n        aria-label={'Type to search for an emoji'}\n        onFocus={closeAllOpenToggles}\n        className={cx(styles.search)}\n        type=\"text\"\n        aria-controls=\"epr-search-id\"\n        placeholder={placeholder}\n        onChange={event => {\n          setInc(inc + 1);\n          setTimeout(() => {\n            onChange(event?.target?.value ?? value);\n          });\n        }}\n        ref={SearchInputRef}\n      />\n      {searchTerm ? (\n        <div\n          role=\"status\"\n          className={cx('epr-status-search-results', styles.visuallyHidden)}\n          aria-live=\"polite\"\n          id=\"epr-search-id\"\n          aria-atomic=\"true\"\n        >\n          {statusSearchResults}\n        </div>\n      ) : null}\n      <IcnSearch />\n      <BtnClearSearch />\n    </Relative>\n  );\n}\n\nconst styles = stylesheet.create({\n  overlay: {\n    padding: 'var(--epr-header-padding)',\n    zIndex: 'var(--epr-header-overlay-z-index)'\n  },\n  searchContainer: {\n    '.': 'epr-search-container',\n    flex: '1',\n    display: 'block',\n    minWidth: '0'\n  },\n  visuallyHidden: {\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(50%)',\n    height: '1px',\n    overflow: 'hidden',\n    position: 'absolute',\n    whiteSpace: 'nowrap',\n    width: '1px'\n  },\n  search: {\n    outline: 'none',\n    transition: 'all 0.2s ease-in-out',\n    color: 'var(--epr-search-input-text-color)',\n    borderRadius: 'var(--epr-search-input-border-radius)',\n    padding: 'var(--epr-search-input-padding)',\n    height: 'var(--epr-search-input-height)',\n    backgroundColor: 'var(--epr-search-input-bg-color)',\n    border: '1px solid var(--epr-search-input-bg-color)',\n    width: '100%',\n    ':focus': {\n      backgroundColor: 'var(--epr-search-input-bg-color-active)',\n      border: '1px solid var(--epr-search-border-color)'\n    },\n    '::placeholder': {\n      color: 'var(--epr-search-input-placeholder-color)'\n    }\n  },\n\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', {\n    ':hover > .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonInteractionStyles } from '../../Stylesheet/stylesheet';\nimport Relative from '../Layout/Relative';\nimport { CategoryNavigation } from '../navigation/CategoryNavigation';\n\nimport { SearchContainer } from './Search/Search';\n\nexport function Header() {\n  return (\n    <Relative\n      className={cx('epr-header', commonInteractionStyles.hiddenOnReactions)}\n    >\n      <SearchContainer />\n      <CategoryNavigation />\n    </Relative>\n  );\n}\n", "import * as React from 'react';\n\nimport { PickerStyleTag } from './Stylesheet/stylesheet';\nimport { Reactions } from './components/Reactions/Reactions';\nimport { Body } from './components/body/Body';\nimport { ElementRefContextProvider } from './components/context/ElementRefContext';\nimport { PickerConfigProvider } from './components/context/PickerConfigContext';\nimport { useReactionsModeState } from './components/context/PickerContext';\nimport { Preview } from './components/footer/Preview';\nimport { Header } from './components/header/Header';\nimport PickerMain from './components/main/PickerMain';\nimport { compareConfig } from './config/compareConfig';\nimport { useAllowExpandReactions, useOpenConfig } from './config/useConfig';\n\nimport { PickerProps } from './index';\n\nfunction EmojiPicker(props: PickerProps) {\n  return (\n    <ElementRefContextProvider>\n      <PickerStyleTag />\n      <PickerConfigProvider {...props}>\n        <ContentControl />\n      </PickerConfigProvider>\n    </ElementRefContextProvider>\n  );\n}\n\nfunction ContentControl() {\n  const [reactionsDefaultOpen] = useReactionsModeState();\n  const allowExpandReactions = useAllowExpandReactions();\n\n  const [renderAll, setRenderAll] = React.useState(!reactionsDefaultOpen);\n  const isOpen = useOpenConfig();\n\n  React.useEffect(() => {\n    if (reactionsDefaultOpen && !allowExpandReactions) {\n      return;\n    }\n\n    if (!renderAll) {\n      setRenderAll(true);\n    }\n  }, [renderAll, allowExpandReactions, reactionsDefaultOpen]);\n\n  if (!isOpen) {\n    return null;\n  }\n\n  return (\n    <PickerMain>\n      <Reactions />\n      <ExpandedPickerContent renderAll={renderAll} />\n    </PickerMain>\n  );\n}\n\nfunction ExpandedPickerContent({ renderAll }: { renderAll: boolean }) {\n  if (!renderAll) {\n    return null;\n  }\n\n  return (\n    <>\n      <Header />\n      <Body />\n      <Preview />\n    </>\n  );\n}\n\n// eslint-disable-next-line complexity\nexport default React.memo(EmojiPicker, compareConfig);\n", "import * as React from 'react';\n\nexport default class ErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError() {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    // eslint-disable-next-line no-console\n    console.error('Emoji Picker React failed to render:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return null;\n    }\n\n    return this.props.children;\n  }\n}\n", "import * as React from 'react';\n\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { GetEmojiUrl } from './BaseEmojiProps';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\nexport function ExportedEmoji({\n  unified,\n  size = 32,\n  emojiStyle = EmojiStyle.APPLE,\n  lazyLoad = false,\n  getEmojiUrl,\n  emojiUrl\n}: {\n  unified: string;\n  emojiStyle?: EmojiStyle;\n  size?: number;\n  lazyLoad?: boolean;\n  getEmojiUrl?: GetEmojiUrl;\n  emojiUrl?: string;\n}) {\n  if (!unified && !emojiUrl && !getEmojiUrl) {\n    return null;\n  }\n\n  return (\n    <ViewOnlyEmoji\n      unified={unified}\n      size={size}\n      emojiStyle={emojiStyle}\n      lazyLoad={lazyLoad}\n      getEmojiUrl={emojiUrl ? () => emojiUrl : getEmojiUrl}\n    />\n  );\n}\n", "import * as React from 'react';\n\nimport EmojiPickerReact from './EmojiPickerReact';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { PickerConfig } from './config/config';\nimport {\n  MutableConfigContext,\n  useDefineMutableConfig\n} from './config/mutableConfig';\n\nexport { ExportedEmoji as Emoji } from './components/emoji/ExportedEmoji';\n\nexport {\n  EmojiStyle,\n  SkinTones,\n  Theme,\n  Categories,\n  EmojiClickData,\n  SuggestionMode,\n  SkinTonePickerLocation\n} from './types/exposedTypes';\n\nexport interface PickerProps extends PickerConfig {}\n\nexport default function EmojiPicker(props: PickerProps) {\n  const MutableConfigRef = useDefineMutableConfig({\n    onEmojiClick: props.onEmojiClick,\n    onReactionClick: props.onReactionClick,\n    onSkinToneChange: props.onSkinToneChange,\n  });\n\n  return (\n    <ErrorBoundary>\n      <MutableConfigContext.Provider value={MutableConfigRef}>\n        <EmojiPickerReact {...props} />\n      </MutableConfigContext.Provider>\n    </ErrorBoundary>\n  );\n}\n"], "names": ["ClassNames", "asSelectors", "classNames", "Array", "_len", "_key", "arguments", "map", "c", "join", "stylesheet", "createSheet", "hidden", "display", "opacity", "pointerEvents", "visibility", "overflow", "commonStyles", "create", "_extends", ".", "PickerStyleTag", "React", "suppressHydrationWarning", "dangerouslySetInnerHTML", "__html", "getStyle", "commonInteractionStyles", ".epr-main", ":has(input:not(:placeholder-shown))", "categoryBtn", ":hover", "backgroundPositionY", "hiddenOnSearch", ":has(input:placeholder-shown)", "visibleOnSearchOnly", "hiddenOnReactions", "transition", ".epr-reactions", "height", "width", ".EmojiPickerReact:not(.epr-search-active)", "&.epr-active", "darkMode", "key", "value", ".epr-dark-theme", "_eprDarkTheme", ".epr-auto-theme", "_eprAutoTheme", "@media (prefers-color-scheme: dark)", "compareConfig", "prev", "next", "prevCustomEmojis", "_prev$customEmojis", "customEmojis", "nextCustomEmojis", "_next$customEmojis", "open", "emojiVersion", "reactionsDefaultOpen", "searchPlaceHolder", "searchPlaceholder", "defaultSkinTone", "skinTonesDisabled", "autoFocusSearch", "emojiStyle", "theme", "suggestedEmojisMode", "lazyLoadEmojis", "className", "style", "searchDisabled", "skinTonePickerLocation", "length", "SuggestionMode", "EmojiStyle", "Theme", "SkinTones", "Categories", "SkinTonePickerLocation", "DEFAULT_REACTIONS", "categoriesOrdered", "SUGGESTED", "CUSTOM", "SMILEYS_PEOPLE", "ANIMALS_NATURE", "FOOD_DRINK", "TRAVEL_PLACES", "ACTIVITIES", "OBJECTS", "SYMBOLS", "FLAGS", "SuggestedRecent", "name", "category", "configByCategory", "_configByCategory", "baseCategoriesConfig", "modifiers", "categoryFromCategoryConfig", "categoryNameFromCategoryConfig", "getBaseConfigByCategory", "modifier", "Object", "assign", "EmojiProperties", "skinToneVariations", "NEUTRAL", "LIGHT", "MEDIUM_LIGHT", "MEDIUM", "MEDIUM_DARK", "DARK", "skinTonesNamed", "entries", "reduce", "acc", "_ref", "skinTonesMapped", "mapped", "skinTone", "_Object$assign", "alphaNumericEmojiIndex", "indexEmoji", "emoji", "emojiNames", "flat", "toLowerCase", "replace", "split", "for<PERSON>ach", "char", "_alphaNumericEmojiInd", "emojiUnified", "_emoji$EmojiPropertie", "emojiName", "unifiedWithoutSkinTone", "unified", "splat", "_splat$splice", "splice", "emojiHasVariations", "_emojiVariationUnifie", "emojiVariations", "find", "variation", "includes", "emojiVariationUnified", "emojisByCategory", "_emojis$category", "emojis", "emojiUrlByUnified", "TWITTER", "GOOGLE", "FACEBOOK", "APPLE", "cdnUrl", "_emoji$EmojiPropertie2", "variations", "emojiByUnified", "allEmojisByUnified", "withoutSkinTone", "setTimeout", "allEmojis", "searchIndex", "values", "<PERSON><PERSON><PERSON>", "KNOWN_FAILING_EMOJIS", "mergeConfig", "userConfig", "base", "basePickerConfig", "previewConfig", "_userConfig$previewCo", "config", "categories", "userCategoriesConfig", "extra", "suggestionMode", "RECENT", "_userCategoriesConfig", "mergeCategoriesConfig", "hidden<PERSON><PERSON><PERSON><PERSON>", "unicodeToHide", "add", "_config$customEmojis", "emojiData", "names", "id", "added_in", "imgUrl", "customToRegularEmoji", "push", "PREVIEW", "getEmojiUrl", "basePreviewConfig", "SEARCH", "FREQUENT", "Set", "reactions", "allowExpandReactions", "defaultEmoji", "defaultCaption", "showPreview", "ConfigContext", "PickerConfigProvider", "children", "mergedConfig", "_React$useState", "setMergedConfig", "useSetConfig", "_objectWithoutPropertiesLoose", "_excluded", "Provider", "usePickerConfig", "useDebouncedState", "initialValue", "delay", "_useState", "useState", "state", "setState", "timer", "useRef", "Promise", "resolve", "current", "clearTimeout", "_window", "window", "useDisallowedEmojis", "DisallowedEmojisRef", "emojiVersionConfig", "useMemo", "parseFloat", "Number", "isNaN", "disallowed<PERSON><PERSON><PERSON><PERSON>", "supportedLevel", "addedIn", "addedInNewerVersion", "useIsEmojiDisallowed", "isUnicodeHidden", "has", "Boolean", "PickerContextProvider", "dispatch", "filterRef", "disallowClickRef", "disallowMouseRef", "disallowedEmojisRef", "suggestedUpdateState", "Date", "now", "searchTerm", "skinToneFanOpenState", "activeSkinTone", "activeCategoryState", "emojisThatFailedToLoadState", "emojiVariationPickerState", "reactionsModeState", "isPastInitialLoad", "useEffect", "<PERSON>er<PERSON>ontext", "useFilterRef", "useDisallowMouseRef", "useReactionsModeState", "useSearchTermState", "useActiveSkinToneState", "useEmojisThatFailedToLoadState", "useEmojiVariationPickerState", "useSkinToneFanOpenState", "useUpdateSuggested", "setsuggestedUpdate", "MOUSE_EVENT_SOURCE", "MutableConfigContext", "createContext", "useMutableConfig", "useContext", "emptyFunc", "useAllowExpandReactions", "useSkinTonesDisabledConfig", "useEmojiStyleConfig", "useCategoriesConfig", "usePreviewConfig", "useSearchDisabledConfig", "useSkinTonePickerLocationConfig", "useGetEmojiUrlConfig", "getDimension", "dimensionConfig", "useIsSearchMode", "focusElement", "element", "requestAnimationFrame", "focus", "focusPrevElementSibling", "previousElementSibling", "focusNextElementSibling", "nextElement<PERSON><PERSON>ling", "focusFirstElementChild", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getActiveElement", "document", "activeElement", "ElementRefContextProvider", "Picker<PERSON>ain<PERSON><PERSON>", "AnchoredEmojiRef", "BodyRef", "SearchInputRef", "SkinTonePickerRef", "CategoryNavigationRef", "VariationPickerRef", "ReactionsRef", "ElementRefContext", "KeyboardEvents", "useElementRef", "usePickerMainRef", "useAnchoredEmojiRef", "useSetAnchoredEmojiRef", "target", "useBodyRef", "useSearchInputRef", "useSkinTonePickerRef", "useCategoryNavigationRef", "scrollTo", "root", "top", "$eprBody", "queryScrollBody", "scrollTop", "scrollEmojiAboveLabel", "emojiDistanceFromScrollTop", "categoryLabelHeight", "closestCategory", "isEmojiBehindLabel", "closest", "variationPicker", "scrollBody", "closestScrollBody", "by", "scrollBy", "focusFirstVisibleEmoji", "parent", "firstVisibleEmoji", "useCloseAllOpenToggles", "_useEmojiVariationPic", "setVariationPicker", "_useSkinToneFanOpenSt", "skinToneFanOpen", "setSkinToneFanOpen", "useCallback", "useHasOpenToggles", "useAllowMouseMove", "DisallowMouseRef", "useIsMouseDisallowed", "useFocusSearchInput", "useFocusCategoryNavigation", "useClearSearch", "applySearch", "useApplySearch", "focusSearchInput", "setSearchTerm", "then", "hasMatch", "keyword", "some", "getNormalizedSearchTerm", "str", "trim", "useSetVariationPicker", "setAnchoredEmojiRef", "setEmojiVariationPicker", "emojiFromElement", "useIsSkinToneInSearch", "useIsSkinToneInPreview", "useGoDownFromSearchInput", "focusCategoryNavigation", "isSearchMode", "focusNextSkinTone", "exitLeft", "currentSkinTone", "focusPrevSkinTone", "useOnType", "appendSearch", "closeAllOpenToggles", "event", "metaKey", "ctrl<PERSON>ey", "altKey", "hasModifier", "match", "preventDefault", "preloadedEmojs", "<PERSON><PERSON><PERSON><PERSON>", "PickerRootElement", "_ref2", "reactionsMode", "searchModeActive", "_usePickerConfig15", "useStyleConfig", "focusSkinTonePicker", "setSkinToneFanOpenState", "goDownFromSearchInput", "isSkinToneInSearch", "onKeyDown", "clearSearch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disallowMouseMove", "Escape", "addEventListener", "removeEventListener", "usePickerMainKeyboardEvents", "first<PERSON><PERSON><PERSON>", "ArrowRight", "ArrowDown", "Enter", "click", "_useSkinToneFanOpenSt2", "isOpen", "setIsOpen", "isSkinToneInPreview", "onType", "ArrowLeft", "ArrowUp", "useSkinTonePickerKeyboardEvents", "useCategoryNavigationKeyboardEvents", "goUpFromBody", "buttonFromTarget", "nextVisibleEmoji", "isVisibleEmoji", "nextCategory", "focusNextVisibleEmoji", "prevVisibleEmoji", "lastVisibleEmoji", "prevCategory", "focusPrevVisibleEmoji", "index", "nextRowElements", "allElements", "currentRow", "elementsInRow", "nextRow", "categoryContent", "closestCategoryContent", "indexInRow", "elementIndexInRow", "row", "rowNumber", "countInRow", "elementCountInRow", "elementHeight", "getBoundingClientRect", "elementTop", "parentTop", "parentHeight", "Math", "round", "hasNextRow", "nextVisibleCategory", "getElementInRow", "allVisibleEmojis", "getRowElements", "visibleEmojiOneRowDown", "exitUp", "prevRowElements", "prevVisibleCategory", "visibleEmojiOneRowUp", "focusVisibleEmojiOneRowUp", "Space", "useBodyKeyboardEvents", "useKeyboardNavigation", "NATIVE", "bodyRef", "onFocus", "button", "url", "Image", "src", "preloadEmoji", "_ref3", "styleProps", "cx", "styles", "main", "baseVariables", "darkTheme", "AUTO", "autoThemeDark", "_cx", "searchActive", "reactionsMenu", "ref", "DarkTheme", "--epr-emoji-variation-picker-bg-color", "--epr-hover-bg-color-reduced-opacity", "--epr-highlight-color", "--epr-text-color", "--epr-hover-bg-color", "--epr-focus-bg-color", "--epr-search-input-bg-color", "--epr-category-label-bg-color", "--epr-picker-border-color", "--epr-bg-color", "--epr-reactions-bg-color", "--epr-search-input-bg-color-active", "--epr-emoji-variation-indicator-color", "--epr-category-icon-active-color", "--epr-skin-tone-picker-menu-color", "--epr-skin-tone-outer-border-color", "--epr-skin-tone-inner-border-color", "emojiPicker", "position", "flexDirection", "borderWidth", "borderStyle", "borderRadius", "borderColor", "backgroundColor", "*", "boxSizing", "fontFamily", "--", "--epr-horizontal-padding", "--epr-picker-border-radius", "--epr-search-border-color", "--epr-header-padding", "--epr-active-skin-tone-indicator-border-color", "--epr-active-skin-hover-color", "--epr-search-input-padding", "--epr-search-input-border-radius", "--epr-search-input-height", "--epr-search-input-text-color", "--epr-search-input-placeholder-color", "--epr-search-bar-inner-padding", "--epr-category-navigation-button-size", "--epr-emoji-variation-picker-height", "--epr-preview-height", "--epr-preview-text-size", "--epr-preview-text-padding", "--epr-preview-border-color", "--epr-preview-text-color", "--epr-category-padding", "--epr-category-label-text-color", "--epr-category-label-padding", "--epr-category-label-height", "DEFAULT_LABEL_HEIGHT", "--epr-emoji-size", "--epr-emoji-padding", "--epr-emoji-fullsize", "--epr-emoji-hover-color", "--epr-emoji-variation-indicator-color-hover", "--epr-header-overlay-z-index", "--epr-emoji-variations-indictator-z-index", "--epr-category-label-z-index", "--epr-skin-variation-picker-z-index", "--epr-preview-z-index", "--epr-dark", "--epr-dark-emoji-variation-picker-bg-color", "--epr-dark-highlight-color", "--epr-dark-text-color", "--epr-dark-hover-bg-color", "--epr-dark-hover-bg-color-reduced-opacity", "--epr-dark-focus-bg-color", "--epr-dark-search-input-bg-color", "--epr-dark-category-label-bg-color", "--epr-dark-picker-border-color", "--epr-dark-bg-color", "--epr-dark-reactions-bg-color", "--epr-dark-search-input-bg-color-active", "--epr-dark-emoji-variation-indicator-color", "--epr-dark-category-icon-active-color", "--epr-dark-skin-tone-picker-menu-color", "--epr-dark-skin-tone-outer-border-color", "--epr-dark-skin-tone-inner-border-color", "autoTheme", "<PERSON><PERSON>ilter", "parentWidth", "elementWidth", "floor", "elementLeft", "left", "parentLeft", "elements", "lastRow", "slice", "rowElements", "EmojiButtonSelector", "VisibleEmojiSelector", "visible", "emojiElement", "_emojiElement$closest", "originalUnified", "originalUnifiedFromEmojiElement", "unifiedFromEmojiElement", "_element$clientHeight", "clientHeight", "emojiTrueOffsetTop", "labelHeight", "elementOffsetTop", "categoryWithoutLabel", "querySelector", "_category$clientHeigh", "_categoryWithoutLabel", "matches", "_closestScrollBody$sc", "_closestScrollBody", "_element$closest", "_element$offsetTop", "offsetTop", "elementOffsetLeft", "_element$offsetLeft", "offsetLeft", "_elementDataSetKey", "_elementDataSet$key", "_element$dataset", "dataset", "elementDataSet", "allUnifiedFromEmojiElement", "classList", "contains", "isHidden", "from", "querySelectorAll", "last", "maxVisibilityDiffThreshold", "parentBottom", "bottom", "parentTopWithLabel", "parentNode", "_i", "_labels", "label", "getLabelHeight", "elementBottom", "maxVisibilityDiffPixels", "elementTopWithAllowedDiff", "elementBottomWithAllowedDiff", "firstVisibleElementInContainer", "parseNative<PERSON><PERSON><PERSON>", "hex", "String", "fromCodePoint", "parseInt", "getSuggested", "mode", "_window$localStorage$", "_window2", "localStorage", "recent", "JSON", "parse", "getItem", "sort", "a", "b", "count", "_unused", "isCustomEmoji", "undefined", "useMouseDownHandlers", "ContainerRef", "mouseEventSource", "mouseDownTimerRef", "onEmojiClick", "setReactionsOpen", "handler", "onReactionClick", "REACTIONS", "args", "apply", "concat", "collapseToReactions", "o", "_len2", "_key2", "useOnEmojiClickConfig", "updateSuggested", "activeEmojiStyle", "onClick", "_emojiFromEvent", "emojiFromEvent", "skinToneToUse", "suspectedSkinTone", "activeVariationFromUnified", "nextList", "existing", "filter", "i", "original", "min", "_window3", "setItem", "stringify", "_unused2", "setSuggested", "getImageUrl", "imageUrl", "isCustom", "emojiClickOutput", "onMouseDown", "onMouseUp", "confainerRef", "passive", "_element$parentElemen", "parentElement", "<PERSON><PERSON>", "props", "type", "cursor", "border", "background", "outline", "ClickableEmojiButton", "_ref$showVariations", "showVariations", "hasVariations", "_ref$noBackground", "noBackground", "getAriaLabel", "_emojiNames$", "alignItems", "justifyContent", "max<PERSON><PERSON><PERSON>", "maxHeight", ":focus", ":after", "content", "right", "borderLeft", "borderRight", "transform", "borderBottom", "zIndex", ":hover:after", "emojiStyles", "external", "fontSize", "common", "alignSelf", "justifySelf", "EmojiImg", "_ref$lazyLoad", "lazyLoad", "onError", "alt", "emojiImag", "loading", "min<PERSON><PERSON><PERSON>", "minHeight", "padding", "NativeEmoji", "native<PERSON><PERSON>ji", "lineHeight", "textAlign", "letterSpacing", "View<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "size", "_ref$getEmojiUrl", "setEmojisThatFailedToLoad", "emojiToRender", "ClickableEmoji", "BtnPlus", "setReactionsMode", "title", "tabIndex", "plusSign", "color", "backgroundImage", "backgroundRepeat", "backgroundSize", "Reactions", "reactionsOpen", "list", "reaction", "emojiButton", "listStyle", "margin", ":active", "EmojiCategory", "categoryConfig", "categoryName", ":not(:has(.epr-visible))", "gridGap", "gridTemplateColumns", "fontWeight", "textTransform", "isEverMounted", "Suggested", "suggestedUpdated", "isMounted", "setIsMounted", "useIsEverMounted", "suggestedEmojisModeConfig", "suggested", "_getSuggested", "isEmojiDisallowed", "suggestedItem", "EmojiList", "renderdCategoriesCountRef", "emojiList", "RenderCategory", "emojisThatFailedToLoad", "isEmojiFiltered", "isEmojiHidden", "_filter$searchTerm", "isEmojiFilteredBySearchTerm", "failedToLoad", "filteredOut", "emojisToPush", "hiddenCounter", "_isEmojiHidden", "isDisallowed", "Direction", "EmojiVariationPicker", "pointerStyle", "_useVariationPickerTo", "direction", "Up", "getMenuDirection", "getTop", "emojiOffsetTop", "_bodyRef$scrollTop", "buttonHeight", "Down", "useVariationPickerTop", "getPointerStyle", "emojiTruOffsetLeft", "clientWidth", "usePointerStyle", "pointingUp", "pointer", "boxShadow", "transform<PERSON><PERSON>in", ".pointing-up", "backgroundPosition", "Body", "onScroll", "useOnScroll", "PICKER", "allowMouseMove", "isMouseDisallowed", "onMouseMove", "useOnMouseMove", "body", "FlexDirection", "flex", "overflowY", "overflowX", "Flex", "_ref$style", "_ref$direction", "ROW", "_stylesheet$create", "COLUMN", "Absolute", "Relative", "BtnSkinToneVariation", "isActive", "skinToneVariation", "tone", "closedTone", "active", "SkinTonePickerDirection", "&.epr-tone-neutral", "&.epr-tone-1f3fb", "&.epr-tone-1f3fc", "&.epr-tone-1f3fd", "&.epr-tone-1f3fe", "&.epr-tone-1f3ff", "SkinTonePickerMenu", "SkinTonePicker", "VERTICAL", "HORIZONTAL", "isDisabled", "_useActiveSkinToneSta", "setActiveSkinTone", "onSkinToneChange", "expandedSize", "ITEM_SIZE", "vertical", "skinTones", "verticalShadow", "flexBasis", "select", "--epr-skin-tone-size", ".epr-active", "Preview", "_useReactionsModeStat", "preview", "hideOnReactions", "PreviewBody", "previewEmoji", "setPreviewEmoji", "variationPickerEmoji", "allow", "onEscape", "onMouseOver", "onEnter", "onLeave", "e", "_allUnifiedFromEmojiE", "relatedTarget", "buttonRect", "bodyRect", "y", "detectEmojyPartiallyBelowFold", "_allUnifiedFromEmojiE2", "_document$activeEleme", "blur", "handlePartiallyVisibleElementFocus", "useEmojiPreviewEvents", "_previewEmoji$unified", "show", "defaultText", "borderTop", "CategoryButton", "isActiveCategory", "allowNavigation", "catBtn", "role", "DarkActivePositionY", "DarkInactivePosition", ":not(.epr-search-active)", ":focus:before", "&.epr-icn-suggested", "backgroundPositionX", "&.epr-icn-custom", "&.epr-icn-activities", "&.epr-icn-animals_nature", "&.epr-icn-flags", "&.epr-icn-food_drink", "&.epr-icn-objects", "&.epr-icn-smileys_people", "&.epr-icn-symbols", "&.epr-icn-travel_places", "CategoryNavigation", "activeCategory", "setActiveCategory", "scrollCategoryIntoView", "$category", "_BodyRef$current", "visibleCategories", "Map", "observer", "IntersectionObserver", "_step", "_iterator", "_createForOfIteratorHelperLoose", "done", "entry", "_$category$getAttribu", "getAttribute", "set", "intersectionRatio", "ratios", "lastCategory", "_ratios", "_ratios$_i", "threshold", "el", "observe", "useActiveCategoryScrollDetection", "customCategoryConfig", "categoriesConfig", "hideCustomCategory", "nav", "isCustomCategory", ".epr-search-active", ".epr-main:has(input:not(:placeholder-shown))", "BtnClearSearch", "btnClearSearch", "icnClearnSearch", "HoverDark", "> .epr-icn-clear-search", "SVGTimes", "SCOPE", "EMOJI_BUTTON", "CATEGORY", "CssSearch", "q", "gen<PERSON><PERSON><PERSON>", "IcnSearch", "icnSearch", "SearchContainer", "overlay", "Search", "_usePickerConfig", "inc", "setInc", "placeholder", "_find", "p", "autoFocus", "_useFilter", "setFilterRef", "setFilter", "setter", "useSetFilterRef", "onChange", "inputValue", "nextValue", "longestMatch", "dict", "longestMatchingKey", "keys", "findLongestMatch", "filtered", "filterEmojiObjectByKeyword", "statusSearchResults", "filterState", "searchResultsCount", "_Object$entries", "toString", "getStatusSearchResults", "useFilter", "input", "searchContainer", "search", "_event$target$value", "_event$target", "visuallyHidden", "clip", "clipPath", "whiteSpace", "::placeholder", ":hover > .epr-icn-clear-search", "Header", "EmojiPicker", "ContentControl", "renderAll", "setRenderAll", "ExpandedPickerContent", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_this", "call", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "_proto", "prototype", "componentDidCatch", "error", "errorInfo", "console", "render", "this", "_ref$size", "_ref$emojiStyle", "emojiUrl", "MutableConfigRef", "useDefineMutableConfig", "EmojiPickerReact"], "mappings": "0EAAYA,mrBAuBIC,+BAAeC,MAAwBC,MAAAC,GAAAC,IAAAA,EAAAD,EAAAC,IAAxBH,EAAwBG,GAAAC,UAAAD,GACrD,OAAOH,EAAWK,KAAI,SAAAC,GAAC,UAAQA,KAAKC,KAAK,KAxB3C,SAAYT,GACVA,wCACAA,mCACAA,sBACAA,wBACAA,sBACAA,oBACAA,gCACAA,mCACAA,+CACAA,gDACAA,wBACAA,6BACAA,6BACAA,iCACAA,kBACAA,0BACAA,8BACAA,+CACAA,6BACAA,6BApBF,CAAYA,IAAAA,OCKL,IAAMU,EAAaC,cAAY,MAAO,MAEvCC,EAAS,CACbC,QAAS,OACTC,QAAS,IACTC,cAAe,OACfC,WAAY,SACZC,SAAU,UAGCC,EAAeR,EAAWS,OAAO,CAC5CP,OAAMQ,GACJC,IAAKrB,EAAWY,QACbA,KAIMU,EAAiBC,QAAW,WACvC,OACEA,yBACEC,4BACAC,wBAAyB,CAAEC,OAAQhB,EAAWiB,iBAKvCC,EAA0BlB,EAAWS,OAAO,CACvDU,YAAa,CACXC,sCAAuC,CACrCC,YAAa,CACXC,SAAU,CACRlB,QAAS,IACTmB,oBAAqB,+CAGzBC,eAAcd,GACZC,IAAKrB,EAAWkC,gBACbtB,IAGPuB,gCAAiC,CAC/BC,oBAAqBxB,IAGzByB,kBAAmB,CACjBC,WAAY,wBAEdC,iBAAkB,CAChBF,kBAAmB,CACjBG,OAAQ,MACRC,MAAO,MACP3B,QAAS,IACTC,cAAe,OACfE,SAAU,WAGdyB,4CAA6C,CAC3CX,YAAa,CACXC,SAAU,CACRlB,QAAS,IACTmB,oBAAqB,8CAEvBU,eAAgB,CACd7B,QAAS,IACTmB,oBAAqB,+CAGzBG,oBAAmBhB,GACjBC,IAAK,8BACFT,eAKOgC,EAASC,EAAaC,WACpC,MAAO,CACLC,mBAAiBC,KAAAA,EACdH,GAAMC,EAAKE,GAEdC,mBAAiBC,KAAAA,EACdL,GAAM,CACLM,sCAAuCL,GACxCI,aCpFSE,EAAcC,EAAoBC,WAC1CC,SAAgBC,EAAGH,EAAKI,cAAYD,EAAI,GACxCE,SAAgBC,EAAGL,EAAKG,cAAYE,EAAI,GAC9C,OACEN,EAAKO,OAASN,EAAKM,MACnBP,EAAKQ,eAAiBP,EAAKO,cAC3BR,EAAKS,uBAAyBR,EAAKQ,sBACnCT,EAAKU,oBAAsBT,EAAKS,mBAChCV,EAAKW,oBAAsBV,EAAKU,mBAChCX,EAAKY,kBAAoBX,EAAKW,iBAC9BZ,EAAKa,oBAAsBZ,EAAKY,mBAChCb,EAAKc,kBAAoBb,EAAKa,iBAC9Bd,EAAKe,aAAed,EAAKc,YACzBf,EAAKgB,QAAUf,EAAKe,OACpBhB,EAAKiB,sBAAwBhB,EAAKgB,qBAClCjB,EAAKkB,iBAAmBjB,EAAKiB,gBAC7BlB,EAAKmB,YAAclB,EAAKkB,WACxBnB,EAAKb,SAAWc,EAAKd,QACrBa,EAAKZ,QAAUa,EAAKb,OACpBY,EAAKoB,QAAUnB,EAAKmB,OACpBpB,EAAKqB,iBAAmBpB,EAAKoB,gBAC7BrB,EAAKsB,yBAA2BrB,EAAKqB,wBACrCpB,EAAiBqB,SAAWlB,EAAiBkB,OCzB1C,ICWKC,EAKAC,EAQAC,EAMAC,EASAC,EAaAC,IDpDCC,EAAoB,CAC/B,QACA,YACA,QACA,QACA,QACA,QACA,UCIUN,EAAAA,yBAAAA,4CAEVA,uBAGUC,EAAAA,qBAAAA,wCAEVA,gBACAA,oBACAA,kBACAA,uBAGUC,EAAAA,gBAAAA,+BAEVA,gBACAA,eAGUC,EAAAA,oBAAAA,yCAEVA,gBACAA,uBACAA,iBACAA,sBACAA,gBAGUC,EAAAA,qBAAAA,8CAEVA,kBACAA,kCACAA,kCACAA,0BACAA,gCACAA,0BACAA,oBACAA,oBACAA,iBAGUC,EAAAA,iCAAAA,oDAEVA,oBCtDF,IAIME,EAAkC,CACtCH,mBAAWI,UACXJ,mBAAWK,OACXL,mBAAWM,eACXN,mBAAWO,eACXP,mBAAWQ,WACXR,mBAAWS,cACXT,mBAAWU,WACXV,mBAAWW,QACXX,mBAAWY,QACXZ,mBAAWa,OAGAC,EAAkC,CAC7CC,KAAM,gBACNC,SAAUhB,mBAAWI,WAQjBa,IAAgBC,MACnBlB,mBAAWI,WAAY,CACtBY,SAAUhB,mBAAWI,UACrBW,KAAM,mBACPG,EACAlB,mBAAWK,QAAS,CACnBW,SAAUhB,mBAAWK,OACrBU,KAAM,iBACPG,EACAlB,mBAAWM,gBAAiB,CAC3BU,SAAUhB,mBAAWM,eACrBS,KAAM,oBACPG,EACAlB,mBAAWO,gBAAiB,CAC3BS,SAAUhB,mBAAWO,eACrBQ,KAAM,oBACPG,EACAlB,mBAAWQ,YAAa,CACvBQ,SAAUhB,mBAAWQ,WACrBO,KAAM,gBACPG,EACAlB,mBAAWS,eAAgB,CAC1BO,SAAUhB,mBAAWS,cACrBM,KAAM,mBACPG,EACAlB,mBAAWU,YAAa,CACvBM,SAAUhB,mBAAWU,WACrBK,KAAM,cACPG,EACAlB,mBAAWW,SAAU,CACpBK,SAAUhB,mBAAWW,QACrBI,KAAM,WACPG,EACAlB,mBAAWY,SAAU,CACpBI,SAAUhB,mBAAWY,QACrBG,KAAM,WACPG,EACAlB,mBAAWa,OAAQ,CAClBG,SAAUhB,mBAAWa,MACrBE,KAAM,SACPG,YAGaC,EACdC,GAEA,OAAOjB,EAAkB7E,KAAI,SAAA0F,GAC3B,OAAA7E,KACK8E,EAAiBD,GAChBI,GAAaA,EAAUJ,IAAaI,EAAUJ,gBAKxCK,EAA2BL,GACzC,OAAOA,EAASA,kBAGFM,EAA+BN,GAC7C,OAAOA,EAASD,KAuClB,SAASQ,EACPP,EACAQ,GAEA,gBAFAA,IAAAA,EAA2B,IAEpBC,OAAOC,OAAOT,EAAiBD,GAAWQ,OC/HvCG,8jjJCANC,EAAqB,CACzB7B,kBAAU8B,QACV9B,kBAAU+B,MACV/B,kBAAUgC,aACVhC,kBAAUiC,OACVjC,kBAAUkC,YACVlC,kBAAUmC,MAGCC,EAAiBV,OAAOW,QAAQrC,mBAAWsC,QACtD,SAACC,EAAGC,GAEF,OADAD,EADeC,MAAPA,KAEDD,IAET,IAGWE,EAGTZ,EAAmBS,QACrB,SAACI,EAAQC,GAAQ,IAAAC,EAAA,OACflB,OAAOC,OAAOe,IAAME,MACjBD,GAAWA,EAAQC,MAExB,KDzBF,SAAYhB,GACVA,WACAA,cACAA,iBACAA,eACAA,kBALF,CAAYA,IAAAA,OECL,IAAMiB,EAAoC,YAWjCC,EAAWC,GACAC,EAAWD,GACjCE,OACAxH,KAAK,IACLyH,cACAC,QAAQ,eAAgB,IACxBC,MAAM,IAEQC,SAAQ,SAAAC,SACvBT,EAAuBS,UAAKC,EAAGV,EAAuBS,IAAKC,EAAI,GAE/DV,EAAuBS,GAAME,EAAaT,IAAUA,cCbxCC,EAAWD,SACzB,cAAAU,EAAOV,EAAMnB,EAAgBZ,OAAKyC,EAAI,YAOxBC,EAAUX,GACxB,OAAKA,EAIEC,EAAWD,GAAO,GAHhB,YAMKY,EAAuBC,GACrC,IAAMC,EAAQD,EAAQR,MAAM,KAC5BU,EAAmBD,EAAME,OAAO,EAAG,GAEnC,OAAItB,EAFWqB,MAGND,EAAMpI,KAAK,KAGbmI,WAGOJ,EAAaT,EAAkBJ,SACvCiB,EAAUb,EAAMnB,EAAgBgC,SAEtC,OAAKjB,GAAaqB,EAAmBjB,WAIrCkB,WAyBAlB,EACAJ,GAEA,OAAOA,EACHuB,EAAgBnB,GAAOoB,MAAK,SAAAC,GAAS,OAAIA,EAAUC,SAAS1B,MAC5Da,EAAaT,GA9BVuB,CAAsBvB,EAAOJ,IAASsB,EAHpCL,WAMKW,EAAiBtD,SAE/B,cAAAuD,QAAOC,SAAAA,EAASxD,IAASuD,EAAI,YAIfE,EACdd,EACAxE,GAEA,kBChDqBA,GACrB,OAAQA,GACN,KAAKU,mBAAW6E,QACd,MAPJ,wEAQE,KAAK7E,mBAAW8E,OACd,MAPJ,sEAQE,KAAK9E,mBAAW+E,SACd,MAbJ,0EAcE,KAAK/E,mBAAWgF,MAChB,QACE,MAlBJ,qEDwDUC,CAAO3F,GAAcwE,kBAGjBM,EAAgBnB,SAC9B,cAAAiC,EAAOjC,EAAMnB,EAAgBqD,aAAWD,EAAI,YAG9BhB,EAAmBjB,GACjC,OAAOmB,EAAgBnB,GAAOnD,OAAS,WAYzBsF,EAAetB,GAC7B,GAAKA,EAAL,CAIA,GAAIuB,EAAmBvB,GACrB,OAAOuB,EAAmBvB,GAG5B,IAAMwB,EAAkBzB,EAAuBC,GAC/C,OAAOuB,EAAmBC,IDpF5BC,YAAW,WACTC,EAAUhD,QAAO,SAACiD,EAAaxC,GAE7B,OADAD,EAAWC,GACJwC,IACN1C,MCmFE,IAAMyC,EAAwB5D,OAAO8D,OAAOf,GAAQxB,OA6BrDkC,EAEF,GAEJE,YAAW,WACTC,EAAUhD,QAAO,SAACgD,EAAWG,GAS3B,OARAH,EAAU9B,EAAaiC,IAAUA,EAE7BzB,EAAmByB,IACrBvB,EAAgBuB,GAAOpC,SAAQ,SAAAe,GAC7BkB,EAAUlB,GAAaqB,KAIpBH,IACNH,UEhHCO,EAAuB,CAAC,YAAa,YAAa,sBAWxCC,EACdC,oBAAAA,IAAAA,EAA2B,IAE3B,IFwD8BnH,EExDxBoH,EAAOC,IAEPC,EAAgBrE,OAAOC,OAC3BkE,EAAKE,qBAAaC,EAClBJ,EAAWG,eAAaC,EAAI,IAExBC,EAASvE,OAAOC,OAAOkE,EAAMD,GAE7BM,WNqDNC,EACA9E,kBADA8E,IAAAA,EAA2C,aAC3C9E,IAAAA,EAAqC,IAErC,IAAM+E,EAAQ,GAEV/E,EAAUgF,iBAAmBxG,uBAAeyG,SAC9CF,EAAMnG,mBAAWI,WAAaU,GAGhC,IAAM8E,EAAOzE,EAAqBgF,GAClC,cAAIG,EAACJ,IAAAI,EAAsB3G,OAIpBuG,EAAqB5K,KAAI,SAAA0F,GAC9B,MAAwB,iBAAbA,EACFO,EAAwBP,EAAUmF,EAAMnF,IAGjD7E,KACKoF,EAAwBP,EAASA,SAAUmF,EAAMnF,EAASA,WAC1DA,MAVE4E,EMhEUW,CAAsBZ,EAAWM,WAAY,CAC9DG,eAAgBJ,EAAO3G,sBAGzB2G,EAAOQ,aAAapD,SAAQ,SAACN,GAC3BkD,EAAOS,cAAcC,IAAI5D,MF2CGtE,SExCfmI,EAACX,EAAOxH,cAAYmI,EAAI,GFyCvCnC,EAAOxE,mBAAWK,QAAQV,OAAS,EAEnCnB,EAAa4E,SAAQ,SAAAN,GACnB,IAAM8D,EAcV,SAA8B9D,SAC5B,OAAAP,MACGZ,EAAgBZ,MAAO+B,EAAM+D,MAAMvL,KAAI,SAAAyF,GAAI,OAAIA,EAAKkC,iBAAcV,EAClEZ,EAAgBgC,SAAUb,EAAMgE,GAAG7D,cAAaV,EAChDZ,EAAgBoF,UAAW,IAAGxE,EAC9BZ,EAAgBqF,QAASlE,EAAMkE,OAAMzE,EAnBpB0E,CAAqBnE,GAEvC0B,EAAOxE,mBAAWK,QAAQ6G,KAAKN,GAE3B1B,EAAmB0B,EAAUjF,EAAgBgC,YAIjD0B,EAAU6B,KAAKN,GACf1B,EAAmB0B,EAAUjF,EAAgBgC,UAAYiD,EACzD/D,EAAW+D,OEpDb,IAAMlH,EAAyBsG,EAAOvG,eAClCQ,+BAAuBkH,QACvBnB,EAAOtG,uBAEX,OAAAvD,KACK6J,GACHC,WAAAA,EACAH,cAAAA,EACApG,uBAAAA,IAIJ,SAAgBmG,IACd,MAAO,CACL3G,iBAAiB,EACjB+G,WAAY9E,IACZ5B,UAAW,GACXf,aAAc,GACdQ,gBAAiBe,kBAAU8B,QAC3B1C,WAAYU,mBAAWgF,MACvBjG,aAAc,KACdwI,YAAa3C,EACblH,OAAQ,IACR+B,gBAAgB,EAChBwG,cAAa3J,KACRkL,GAEL5H,gBAAgB,EAChBX,kBA1DsC,SA2DtCC,kBA3DsC,SA4DtCW,uBAAwBO,+BAAuBqH,OAC/CrI,mBAAmB,EACnBO,MAAO,GACPH,oBAAqBO,uBAAe2H,SACpCnI,MAAOU,cAAMgC,MACb2E,cAAe,IAAIe,IAAY/B,GAC/BjI,MAAO,IACPqB,sBAAsB,EACtB4I,UAAWvH,EACXvB,MAAM,EACN+I,sBAAsB,EACtBlB,aAAc,IAuClB,IAAMa,EAAmC,CACvCM,aAAc,QACdC,eAAgB,oBAChBC,aAAa,mBC5HTC,GAAgBxL,gBACpBuJ,cAGckC,GAAoBxF,OAAGyF,EAAQzF,EAARyF,SAC/BC,WASqBjC,SAC3BkC,EAAwC5L,YAAe,WAAA,OACrDoJ,EAAYM,MADPiC,EAAYC,KAAEC,EAAeD,KAiCpC,OA7BA5L,aAAgB,WACV6B,EAAc8J,EAAcjC,IAGhCmC,EAAgBzC,EAAYM,MAG3B,QAAAW,EACDX,EAAOxH,qBAAPmI,EAAqBhH,OACrBqG,EAAOrH,KACPqH,EAAOpH,aACPoH,EAAOnH,qBACPmH,EAAOlH,kBACPkH,EAAOjH,kBACPiH,EAAOhH,gBACPgH,EAAO/G,kBACP+G,EAAO9G,gBACP8G,EAAO7G,WACP6G,EAAO5G,MACP4G,EAAO3G,oBACP2G,EAAO1G,eACP0G,EAAOzG,UACPyG,EAAOzI,OACPyI,EAAOxI,MACPwI,EAAOvG,eACPuG,EAAOtG,uBACPsG,EAAO0B,uBAGFO,EA3CcG,CADmCC,EAAA9F,EAAA+F,KAGxD,OACEhM,gBAACwL,GAAcS,UAAS1K,MAAOoK,GAC5BD,GA0CP,SAAgBQ,KACd,OAAOlM,aAAiBwL,aCjEVW,GACdC,EACAC,YAAAA,IAAAA,EAAgB,GAEhB,IAAAC,EAA0BC,WAAYH,GAA/BI,EAAKF,KAAEG,EAAQH,KAChBI,EAAQC,SAAsB,MAepC,MAAO,CAACH,EAbR,SAA2BjL,GACzB,OAAO,IAAIqL,SAAW,SAAAC,SAChBH,EAAMI,SACRC,aAAaL,EAAMI,SAGrBJ,EAAMI,eAAOE,EAAGC,eAAAD,EAAQlE,YAAW,WACjC2D,EAASlL,GACTsL,EAAQtL,KACP8K,iBCLOa,KACd,IAAMC,EAAsBR,SAAgC,IACtDS,ECsImBlB,KAAjB5J,aDpIR,OAAO+K,WAAQ,WACb,IAAM/K,EAAegL,cAAcF,GAEnC,OAAKA,GAAsBG,OAAOC,MAAMlL,GAC/B6K,EAAoBL,QAGtB/D,EAAUhD,QAAO,SAAC0H,EAAkBjH,GAKzC,OAgBN,SACEA,EACAkH,GAEA,gBLjCsBlH,GACtB,OAAO8G,WAAW9G,EAAMnB,EAAgBoF,WKgCjCkD,CAAQnH,GAASkH,EAxBhBE,CAAoBpH,EAAOlE,KAC7BmL,EAAiBxG,EAAaT,KAAU,GAGnCiH,IACNN,EAAoBL,WACtB,CAACM,IAGN,SAAgBS,KACd,IEhCQ1D,EFgCFsD,EAAmBP,KACnBY,GEjCE3D,EDiKkB+B,KAAlB/B,cChKC,SAAClD,GAAoB,OAAKkD,EAAc4D,IAAI9G,KFkCrD,OAAO,SAA2BT,GAChC,IAAMa,EAAUD,EAAuBH,EAAaT,IAEpD,OAAOwH,QAAQP,EAAiBpG,IAAYyG,EAAgBzG,cG1BhD4G,GAAqBhI,OCXnCiI,EDWsCxC,EAAQzF,EAARyF,SAChC+B,EAAmBP,KACnBxK,EFuBsBwJ,KAApBxJ,gBEtBFH,EF8H2B2J,KAAzB3J,qBE3HF4L,EAAYnO,SAA0BsG,GACtC8H,EAAmBpO,UAAsB,GACzCqO,EAAmBrO,UAAsB,GACzCsO,EAAsBtO,SAC1ByN,GAGIc,EAAuBpC,GAAkBqC,KAAKC,MAAO,KACrDC,EAAavC,GAAkB,GAAI,KACnCwC,EAAuBpC,YAAkB,GACzCqC,EAAiBrC,WAAoB7J,GACrCmM,EAAsBtC,WAA8B,MACpDuC,EAA8BvC,WAAsB,IAAIrB,KACxD6D,EAA4BxC,WAA2B,MACvDyC,EAAqBzC,WAAShK,GACpC+J,EAAkDC,YAAS,GAApD0C,EAAiB3C,KAIxB,OClCA4C,aAAU,WACRhB,GAAS,KACR,CAJHA,EDgC8C5B,OAK5CtM,gBAACmP,GAAclD,UACb1K,MAAO,CACLsN,oBAAAA,EACAD,eAAAA,EACAR,iBAAAA,EACAC,iBAAAA,EACAC,oBAAAA,EACAS,0BAAAA,EACAD,4BAAAA,EACAX,UAAAA,EACAc,kBAAAA,EACAP,WAAAA,EACAC,qBAAAA,EACAJ,qBAAAA,EACAS,mBAAAA,IAGDtD,GAOP,IAAMyD,GAAgBnP,gBAcnB,CACD6O,oBAAqB,CAAC,KAAM,cAC5BD,eAAgB,CAACnL,kBAAU8B,QAAS,cACpC6I,iBAAkB,CAAEtB,SAAS,GAC7BuB,iBAAkB,CAAEvB,SAAS,GAC7BwB,oBAAqB,CAAExB,QAAS,IAChCiC,0BAA2B,CAAC,KAAM,cAClCD,4BAA6B,CAAC,IAAI5D,IAAO,cACzCiD,UAAW,CAAErB,QAAS,IACtBmC,mBAAmB,EACnBP,WAAY,CAAC,GAAI,WAAA,OAAM,IAAI9B,SAAgB,iBAC3C+B,qBAAsB,EAAC,EAAO,cAC9BJ,qBAAsB,CAACC,KAAKC,MAAO,cACnCO,mBAAoB,EAAC,EAAO,gBAO9B,SAAgBI,KAEd,OADsBpP,aAAiBmP,IAA/BhB,UASV,SAAgBkB,KAEd,OAD6BrP,aAAiBmP,IAAtCd,iBAIV,SAAgBiB,KAEd,OAD+BtP,aAAiBmP,IAAxCH,mBAIV,SAAgBO,KAEd,OADuBvP,aAAiBmP,IAAhCT,WAIV,SAAgBc,KAKd,OAD2BxP,aAAiBmP,IAApCP,eAIV,SAAgBa,KAEd,OADwCzP,aAAiBmP,IAAjDL,4BASV,SAAgBY,KAEd,OADsC1P,aAAiBmP,IAA/CJ,0BAIV,SAAgBY,KAEd,OADiC3P,aAAiBmP,IAA1CR,qBAIV,SAKgBiB,KACd,IAAQrB,EAAyBvO,aAAiBmP,IAA1CZ,qBAEiBsB,EAAsBtB,KAC/C,MAAO,CADwCA,KAG7C,WACEsB,EAAmBrB,KAAKC,aF3IlBqB,GIfCC,GAAuB/P,EAAMgQ,cAExC,IAEF,SAAgBC,KAEd,OADsBjQ,EAAMkQ,WAAWH,IA2BzC,SAASI,MJET,SAAgBC,KAEd,OADiClE,KAAzBd,qBAIV,SAAgBiF,KAEd,OAD8BnE,KAAtBvJ,kBAIV,SAAgB2N,KAEd,OADuBpE,KAAfrJ,WASV,SAAgB0N,KAEd,OADuBrE,KAAfvC,WA+CV,SAAgB6G,KAEd,OAD0BtE,KAAlB1C,cAwCV,SAAgBiH,KAEd,OAD2BvE,KAAnB/I,eAIV,SAAgBuN,KAEd,OADmCxE,KAA3B9I,uBAcV,SAAgBuN,KAKd,OADwBzE,KAAhBpB,YAIV,SAAS8F,GAAaC,GACpB,MAAkC,iBAApBA,EACPA,OACHA,WKtLkBC,KAGtB,QAFqBvB,iBCDPwB,GAAaC,GACtBA,GAILC,uBAAsB,WACpBD,EAAQE,oBAIIC,GAAwBH,GACjCA,GAILD,GAFaC,EAAQI,iCAKPC,GAAwBL,GACjCA,GAILD,GAFaC,EAAQM,6BAKPC,GAAuBP,GAChCA,GAILD,GAFcC,EAAQQ,4BC7BRC,KACd,OAAOC,SAASC,uBCEFC,GAAyB3L,OACvCyF,EAAQzF,EAARyF,SAIMmG,EAAgB7R,SAA0B,MAC1C8R,EAAmB9R,SAA0B,MAC7C+R,EAAU/R,SAA6B,MACvCgS,EAAiBhS,SAA+B,MAChDiS,EAAoBjS,SAA6B,MACjDkS,EAAwBlS,SAA6B,MACrDmS,EAAqBnS,SAA6B,MAClDoS,EAAepS,SAA+B,MAEpD,OACEA,gBAACqS,GAAkBpG,UACjB1K,MAAO,CACLuQ,iBAAAA,EACAC,QAAAA,EACAG,sBAAAA,EACAL,cAAAA,EACAG,eAAAA,EACAC,kBAAAA,EACAE,mBAAAA,EACAC,aAAAA,IAGD1G,IRPP,SAAYoE,GACVA,wBACAA,kBAFF,CAAYA,KAAAA,QQ2BZ,ICNKwC,GDMCD,GAAoBrS,gBAAiC,CACzD8R,iBAAkB9R,cAClB+R,QAAS/R,cACTkS,sBAAuBlS,cACvB6R,cAAe7R,cACfgS,eAAgBhS,cAChBiS,kBAAmBjS,cACnBmS,mBAAoBnS,cACpBoS,aAAcpS,gBAGhB,SAASuS,KACP,OAAOvS,aAAiBqS,IAG1B,SAAgBG,KACd,OAAOD,KAA+B,cAGxC,SAAgBE,KACd,OAAOF,KAAkC,iBAG3C,SAAgBG,KACd,IAAMZ,EAAmBW,KACzB,OAAO,SAACE,GACS,OAAXA,GAAgD,OAA7Bb,EAAiBhF,SACtCiE,GAAae,EAAiBhF,SAGhCgF,EAAiBhF,QAAU6F,GAI/B,SAAgBC,KACd,OAAOL,KAAyB,QAOlC,SAAgBM,KACd,OAAON,KAAgC,eAGzC,SAAgBO,KACd,OAAOP,KAAmC,kBAG5C,SAAgBQ,KACd,OAAOR,KAAuC,+BExFhCS,GAASC,EAAuBC,YAAAA,IAAAA,EAAc,GAC5D,IAAMC,EAAWC,GAAgBH,GAE5BE,GAILlC,uBAAsB,WACpBkC,EAASE,UAAYH,cA+BTI,GAAsB9M,GACpC,GAAKA,YCgD4BA,GACjC,QAAKA,GAKH+M,GAA2B/M,GAC3BgN,GAAoBC,GAAgBjN,IDvDvBkN,CAAmBlN,KAI9BA,EAAMmN,QAAQjV,EAAYD,EAAWmV,kBAAzC,CAIA,IAAMC,EAAaC,GAAkBtN,GAC/BuN,EAAKR,GAA2B/M,aArCfyM,EAAuBc,GAC9C,IAAMZ,EAAWC,GAAgBH,GAE5BE,GAILlC,uBAAsB,WACpBkC,EAASE,UAAYF,EAASE,UAAYU,KA8B5CC,CAASH,IAAcL,GAAoBC,GAAgBjN,IAAUuN,cEzCvDE,GAAuBC,GACrC,IAAM1N,EAAQ2N,GAAkBD,GAChCnD,GAAavK,GACb8M,GAAsB9M,YCpBR4N,KACd,IAAAC,EAA8C3E,KAAvCkE,EAAeS,KAAEC,EAAkBD,KAC1CE,EAA8C5E,KAAvC6E,EAAeD,KAAEE,EAAkBF,KAiB1C,OAf4BG,eAAY,WAClCd,GACFU,EAAmB,MAGjBE,GACFC,GAAmB,KAEpB,CACDb,EACAY,EACAF,EACAG,IAMJ,SAAgBE,KACd,IAAOf,EAAmBlE,QACnB8E,EAAmB7E,QAE1B,OAAO,WACL,QAASiE,GAAmBY,GCtBhC,SAAgBI,KACd,IAAMC,EAAmBxF,KACzB,OAAO,WACLwF,EAAiB/H,SAAU,GAI/B,SAAgBgI,KACd,IAAMD,EAAmBxF,KACzB,OAAO,WACL,OAAOwF,EAAiB/H,kBCbZiI,KACd,IAAM/C,EAAiBa,KAEvB,OAAO6B,eAAY,WACjB3D,GAAaiB,EAAelF,WAC3B,CAACkF,IAeN,SAAgBgD,KACd,IAAM9C,EAAwBa,KAE9B,OAAO2B,eAAY,WACZxC,EAAsBpF,SAI3ByE,GAAuBW,EAAsBpF,WAC5C,CAACoF,ICRN,SAAgB+C,KACd,IAAMC,EAAcC,KACdnD,EAAiBa,KACjBuC,EAAmBL,KAEzB,OAAO,WACD/C,EAAelF,UACjBkF,EAAelF,QAAQvL,MAAQ,IAGjC2T,EAAY,IACZE,KA+DJ,SAASD,KACP,IAASE,EAAiB9F,QACpBsC,EAAgBW,KAEtB,OAAO,SAAqB9D,GAC1BuC,uBAAsB,WACpBoE,EAAc3G,QAAaA,SAAAA,EAAY/H,cAAgB+H,GAAY4G,MACjE,WACEtC,GAASnB,EAAc/E,QAAS,UAwB1C,SAASyI,GAAS/O,EAAkBgP,GAClC,OAAO/O,EAAWD,GAAOiP,MAAK,SAAAhR,GAAI,OAAIA,EAAKqD,SAAS0N,eA+CtCE,GAAwBC,GACtC,OAAKA,GAAsB,iBAARA,EAIZA,EAAIC,OAAOjP,cAHT,YCtLakP,KACtB,IAAMC,EAAsBpD,KACnBqD,EAA2BrG,QAEpC,OAAO,SAA4BsB,GACjC,IAAOxK,EAASwP,GAAiBhF,MAE7BxK,IACFsP,EAAoB9E,GACpB+E,EAAwBvP,cCFdyP,KAGd,OAFqCvF,OAEG/M,+BAAuBqH,OAGjE,SAAgBkL,KAGd,OAFqCxF,OAEG/M,+BAAuBkH,QTwXjE,SAASsL,KACP,IAAMC,EAA0BpB,KAC1BqB,EAAevF,KACfiB,EAAUa,KAEhB,OAAO8B,eACL,WACE,OAAI2B,EACKpC,GAAuBlC,EAAQjF,SAEjCsJ,MAET,CAACrE,EAASqE,EAAyBC,IAoBvC,SAASC,GAAkBC,GACzB,IAAMC,EAAkB/E,KAEnB+E,IAIsBA,EU7PVlF,oBV8PfiF,IAGFlF,GAAwBmF,IAG1B,SAASC,KACP,IAAMD,EAAkB/E,KAEnB+E,GAILrF,GAAwBqF,GAG1B,SAASE,KACP,IOvZM1E,EACAkD,EPsZAyB,GOvZA3E,EAAiBa,KACjBqC,EAAcC,KAEb,SAAsBQ,GACvB3D,EAAelF,SACjBkF,EAAelF,QAAQvL,SAAWyQ,EAAelF,QAAQvL,MAAQoU,EACjET,EAAYQ,GAAwB1D,EAAelF,QAAQvL,SAE3D2T,EAAYQ,GAAwBC,MPgZlCP,EAAmBL,KACnB5R,EAAiBsN,KACjBmG,EAAsBxC,KAE5B,OAAO,SAAgByC,GACrB,IAAQvV,EAAQuV,EAARvV,KAeZ,SAAqBuV,GAGnB,OAFqCA,EAA7BC,SAA6BD,EAApBE,SAAoBF,EAAXG,QAdpBC,CAAYJ,IAAU1T,GAItB7B,EAAI4V,MAAM,wBACZL,EAAMM,iBACNP,IACAxB,IACAuB,EAAarV,MAvanB,SAAKgR,GACHA,wBACAA,oBACAA,wBACAA,0BACAA,kBACAA,gBACAA,YAPF,CAAKA,KAAAA,QWdE,IAAM8E,GAA8B,IAAIlM,mCCNvBmM,GAAUpR,GAChC,OACEjG,gBAACiO,QACCjO,gBAACsX,QAHsCrR,EAARyF,WAcrC,SAAS4L,GAAiBC,SC7BlBxF,EACAlP,EACAiI,ED2BqBY,EAAQ6L,EAAR7L,SACpB8L,EAAiBlI,QAClBxM,ErB4EYoJ,KAAVpJ,MqB3EF2U,EAAmB3G,KACnBe,EAAgBW,KAChBvP,ErByFgBiJ,KAAdjJ,UqBxFFC,ErB4FR,WACE,IAAAwU,EAAiCxL,KAAjBhL,EAAKwW,EAALxW,MAAOgC,EAAKwU,EAALxU,MACvB,OAAArD,GAASoB,OAAQ2P,GADH8G,EAANzW,QAC+BC,MAAO0P,GAAa1P,IAAWgC,GqB9FxDyU,IZUhB,WAgEA,IACQC,EACA/F,EACAE,EACAC,EACG6F,EACHC,EACAC,EAEAC,EM/GA/F,GN8CR,WACE,IK3DM4C,EJkCA9C,EDyBAF,EAAgBW,KAChByF,EAAchD,KACdjC,GC3BAjB,EAAUa,KAET8B,eACL,SAACxB,GACCjC,uBAAsB,WAChBc,EAAQjF,UACViF,EAAQjF,QAAQuG,UAAYH,QAIlC,CAACnB,KDkBGC,EAAiBa,KACjBuC,EAAmBL,KACnBmD,EAAiBvD,KACjBwD,GKjEAtD,EAAmBxF,KAClB,WACLwF,EAAiB/H,SAAU,ILiEvB8J,EAAsBxC,KAEtB4D,EAAY3K,WAChB,WAAA,OACE,SAAmBwJ,GACjB,IAAQvV,EAAQuV,EAARvV,IAGR,OADA6W,IACQ7W,GAEN,KAAKgR,GAAe8F,OAElB,GADAvB,EAAMM,iBACFe,IAEF,YADAtB,IAGFqB,IACAjF,EAAS,GACToC,QAIR,CACEpC,EACAiF,EACArB,EACAxB,EACA8C,EACAC,IAIJjJ,aAAU,WACR,IAAMpC,EAAU+E,EAAc/E,QAE9B,GAAKA,EAML,OAFAA,EAAQuL,iBAAiB,UAAWL,GAE7B,WACLlL,EAAQwL,oBAAoB,UAAWN,MAExC,CAACnG,EAAeG,EAAgBgB,EAAUgF,IA5D7CO,GMvCMtG,EAAoBa,KNuGpB8E,EMrGClD,eAAY,WACZzC,EAAkBnF,SAIvByE,GAAuBU,EAAkBnF,WACxC,CAACmF,INgGEJ,EAAgBW,KAChBT,EAAUa,KACVZ,EAAiBa,KACdgF,EAA2BlI,QAC9BmI,EAAwB3B,KACxB4B,EAAqB9B,KAErB+B,EAAY3K,WAChB,WAAA,OACE,SAAmBwJ,GACjB,IGrGA2B,EHuGA,OAFgB3B,EAARvV,KAGN,KAAKgR,GAAemG,WAClB,IAAKV,EACH,OAEFlB,EAAMM,iBACNU,GAAwB,GACxBD,IACA,MACF,KAAKtF,GAAeoG,UAClB7B,EAAMM,iBACNW,IACA,MACF,KAAKxF,GAAeqG,MAClB9B,EAAMM,iBGnHhBpG,GAFMyH,EAAarE,GHsHsBpC,EAAQjF,gBGnHjD0L,GAAAA,EAAYI,YHuHV,CACEhB,EACAE,EACAD,EACA9F,EACAgG,IAIJ7I,aAAU,WACR,IAAMpC,EAAUkF,EAAelF,QAE/B,GAAKA,EAML,OAFAA,EAAQuL,iBAAiB,UAAWL,GAE7B,WACLlL,EAAQwL,oBAAoB,UAAWN,MAExC,CAACnG,EAAeG,EAAgBgG,IAGrC,WACE,IAAM/F,EAAoBa,KACpBsC,EAAmBL,KACnB/C,EAAiBa,KACjBiF,EAAwB3B,KAC9B0C,EAA4BlJ,KAArBmJ,EAAMD,KAAEE,EAASF,KAClBG,EAAsB9C,KACtB6B,EAAqB9B,KACrBgD,EAASvC,KAETsB,EAAY3K,WAChB,WAAA,gBAEqBwJ,GACjB,IAAQvV,EAAQuV,EAARvV,IAER,GAAIyW,EACF,OAAQzW,GACN,KAAKgR,GAAe4G,UAElB,GADArC,EAAMM,kBACD2B,EACH,OAAO1D,IAETkB,GAAkBlB,GAClB,MACF,KAAK9C,GAAemG,WAElB,GADA5B,EAAMM,kBACD2B,EACH,OAAO1D,IAETqB,KACA,MACF,KAAKnE,GAAeoG,UAClB7B,EAAMM,iBACF2B,GACFC,GAAU,GAEZjB,IACA,MACF,QACEmB,EAAOpC,GAKb,GAAImC,EACF,OAAQ1X,GACN,KAAKgR,GAAe6G,QAElB,GADAtC,EAAMM,kBACD2B,EACH,OAAO1D,IAETkB,GAAkBlB,GAClB,MACF,KAAK9C,GAAeoG,UAElB,GADA7B,EAAMM,kBACD2B,EACH,OAAO1D,IAETqB,KACA,MACF,QACEwC,EAAOpC,OAKjB,CACEiC,EACA1D,EACA2D,EACAjB,EACAmB,EACAD,EACAjB,IAIJ7I,aAAU,WACR,IAAMpC,EAAUmF,EAAkBnF,QAElC,GAAKA,EAML,OAFAA,EAAQuL,iBAAiB,UAAWL,GAE7B,WACLlL,EAAQwL,oBAAoB,UAAWN,MAExC,CAAC/F,EAAmBD,EAAgB8G,EAAQd,IAhN/CoB,GAmNF,WACE,IAAMhE,EAAmBL,KACnB7C,EAAwBa,KACxBhB,EAAUa,KACVqG,EAASvC,KAETsB,EAAY3K,WAChB,WAAA,OACE,SAAmBwJ,GAGjB,OAFgBA,EAARvV,KAGN,KAAKgR,GAAe6G,QAClBtC,EAAMM,iBACN/B,IACA,MACF,KAAK9C,GAAemG,WAClB5B,EAAMM,iBACN9F,GAAwBI,MACxB,MACF,KAAKa,GAAe4G,UAClBrC,EAAMM,iBACNhG,GAAwBM,MACxB,MACF,KAAKa,GAAeoG,UAClB7B,EAAMM,iBACNlD,GAAuBlC,EAAQjF,SAC/B,MACF,QACEmM,EAAOpC,OAIf,CAAC9E,EAASqD,EAAkB6D,IAG9B/J,aAAU,WACR,IAAMpC,EAAUoF,EAAsBpF,QAEtC,GAAKA,EAML,OAFAA,EAAQuL,iBAAiB,UAAWL,GAE7B,WACLlL,EAAQwL,oBAAoB,UAAWN,MAExC,CAAC9F,EAAuBH,EAASiG,IAlQpCqB,GAqQF,WACE,IA2FMjE,EACAgB,EACAC,EA7FAtE,EAAUa,KACV0G,GA0FAlE,EAAmBL,KACnBqB,EAA0BpB,KAC1BqB,EAAevF,KAEd4D,eACL,WACE,OAAI2B,EACKjB,IAEFgB,MAET,CAAChB,EAAkBiB,EAAcD,KApG7B9B,EAAqBuB,KACrBqC,EAAiBvD,KACjBiC,EAAsBxC,KAEtB6E,EAASvC,KAETsB,EAAY3K,WAChB,WAAA,gBAEqBwJ,GACjB,IGrPoC7F,EHqP5B1P,EAAQuV,EAARvV,IAEFqQ,EAAgB4H,GAAiB9H,MAEvC,OAAQnQ,GACN,KAAKgR,GAAemG,WAClB5B,EAAMM,0BG3SoBnG,GACpC,GAAKA,EAAL,CAIA,IAAMjP,WDyLQyX,EAAiBxI,GAC/B,IAAMjP,EAAOiP,EAAQM,mBAErB,OAAKvP,EAIA0X,GAAe1X,GAIbA,EAHEyX,EAAiBzX,GAJjBoS,GAAkBuF,GAAa1I,IC7L3BwI,CAAiBxI,GAE9B,IAAKjP,EACH,OAAOkS,GAAuByF,GAAa1I,IAG7CD,GAAahP,GACbuR,GAAsBvR,IHgSZ4X,CAAsBhI,GACtB,MACF,KAAKW,GAAe4G,UAClBrC,EAAMM,0BGhSoBnG,GACpC,GAAKA,EAAL,CAIA,IAAMlP,EAAO8X,GAAiB5I,GAEzBlP,GAILiP,GAAajP,GACbwR,GAAsBxR,IA9BtBiP,GAAa8I,GA0BkBC,GAAa9I,MHyRlC+I,CAAsBpI,GACtB,MACF,KAAKW,GAAeoG,UAElB,GADA7B,EAAMM,iBACFe,IAAkB,CACpBtB,IACA,OGtQ8B5F,EHwQJW,IGjQ/BZ,GAqCT,SAAgCC,GAC9B,IAAKA,EACH,OAAO,KAGT,IO/BAgJ,EAEMC,EA9BNC,EACAC,EACAC,EAEMC,EPuDAC,EAAkBC,GAAuBvJ,GACzCtM,EAAW+O,GAAgB6G,GAC3BE,EAAaC,GAAkBH,EAAiBtJ,GAChD0J,EAAMC,GAAUL,EAAiBtJ,GACjC4J,EAAaC,GAAkBP,EAAiBtJ,GACtD,aO/FAkD,EACAlD,GAEA,IAAKkD,IAAWlD,EACd,OAAO,EAGT,IAAM8J,EAAgB9J,EAAQ+J,wBAAwB9Z,OAChD+Z,EAAahK,EAAQ+J,wBAAwB7H,IAC7C+H,EAAY/G,EAAO6G,wBAAwB7H,IAC3CgI,EAAehH,EAAO6G,wBAAwB9Z,OAEpD,OAAOka,KAAKC,MAAMJ,EAAaC,EAAYH,GAAiBI,EPmFvDG,CAAWf,EAAiBtJ,GAAU,CACzC,IAAMsK,EAAsB5B,GAAahV,GAEzC,OAAK4W,EAIEC,GACLC,GAAiBF,GACjB,EACAV,EACAJ,GAPO,KAkBX,OO1DAR,EPuDEQ,GOnFFN,EPgFEsB,GAAiBlB,GO/EnBH,EPgFEO,EO/EFN,EPgFEQ,EO9EIP,EAAUF,EAAa,EA0BvBF,EAxBFI,EAAUD,EAAgBF,EAAY7W,OACjC,GAGFoY,GAAevB,EAAaG,EAASD,IA4B1BJ,IAChBC,EAAgBA,EAAgB5W,OAAS,IACzC,KPzBWqY,CAAuB1K,IHoQ1B,MACF,KAAKsB,GAAe6G,QAElB,GADAtC,EAAMM,iBACFe,IAAkB,CACpBtB,IACA,gBG/RZ5F,EACA2K,GAEA,GAAK3K,EAAL,CAIA,IAAMlP,EAoBR,SAA8BkP,GAC5B,IAAKA,EACH,OAAO,KAGT,IOuBAgJ,EAEM4B,EPzBAtB,EAAkBC,GAAuBvJ,GACzCtM,EAAW+O,GAAgB6G,GAC3BE,EAAaC,GAAkBH,EAAiBtJ,GAChD0J,EAAMC,GAAUL,EAAiBtJ,GACjC4J,EAAaC,GAAkBP,EAAiBtJ,GAEtD,GAAY,IAAR0J,EAAW,CACb,IAAMmB,EAAsB/B,GAAapV,GAEzC,OAAKmX,EAIEN,GACLC,GAAiBK,IAChB,EACDjB,EACAJ,GAPO,KAWX,OOEAR,EPEEQ,GOAIoB,EAAkBH,GPHtBD,GAAiBlB,GACjBI,EOIa,EPHbE,IOSgBZ,IAChB4B,EAAgBA,EAAgBvY,OAAS,IACzC,KP5DWyY,CAAqB9K,GAElC,IAAKlP,EACH,OAAO6Z,IAGT5K,GAAajP,GACbwR,GAAsBxR,IHmRZia,CAA0BpK,EAAe2H,GACzC,MACF,KAAKhH,GAAe0J,MAClBnF,EAAMM,iBACN7C,EAAmBuC,EAAMlE,QACzB,MACF,QACEsG,EAAOpC,OAIf,CACEyC,EACAL,EACA3E,EACA4D,EACAtB,IAIJ1H,aAAU,WACR,IAAMpC,EAAUiF,EAAQjF,QAExB,GAAKA,EAML,OAFAA,EAAQuL,iBAAiB,UAAWL,GAE7B,WACLlL,EAAQwL,oBAAoB,UAAWN,MAExC,CAACjG,EAASiG,IA5UbiE,IYbAC,GCrCMnK,EAAUa,KACV/P,EAAayN,KACbxF,EAAc6F,KAEpBzB,aAAU,WACR,GAAIrM,IAAeU,mBAAW4Y,OAA9B,CAIA,IAAMC,EAAUrK,EAAQjF,QAIxB,aAFAsP,GAAAA,EAAS/D,iBAAiB,UAAWgE,GAE9B,iBACLD,GAAAA,EAAS9D,oBAAoB,UAAW+D,IAG1C,SAASA,EAAQxF,GACf,IAAMyF,EAAS/C,GAAiB1C,EAAMlE,QAEtC,GAAK2J,EAAL,CAIA,IAAO9V,EAASwP,GAAiBsG,MAE5B9V,GAIDiB,EAAmBjB,aFnC3BsE,EACAtE,EACA3D,GAEA,GAAK2D,GAID3D,IAAeU,mBAAW4Y,OAA9B,CAIA,IAAM9U,EAAUJ,EAAaT,GAEzB4Q,GAAerJ,IAAI1G,KAIvBM,EAAgBnB,GAAOM,SAAQ,SAACe,GAC9B,IASkB0U,EAAAA,EATDzR,EAAYjD,EAAWhF,IAU5B,IAAI2Z,OACZC,IAAMF,KAPZnF,GAAehN,IAAI/C,KEabqV,CAAa5R,EAAatE,EAAO3D,OAGpC,CAACkP,EAASlP,EAAYiI,IDMzB,IAAA6R,EAAyCzZ,GAAS,GAA1ChC,EAAKyb,EAALzb,MAAOD,EAAM0b,EAAN1b,OAAW2b,EAAU7Q,EAAA4Q,EAAA3Q,IAEpC,OACEhM,yBACEiD,UAAW4Z,KACTC,GAAOC,KACPD,GAAOE,cACPla,IAAUU,cAAMoC,MAAQkX,GAAOG,UAC/Bna,IAAUU,cAAM0Z,MAAQJ,GAAOK,eAAaC,KAAAA,EAEzC3e,EAAW4e,cAAe5F,EAAgB2F,GAE7C5F,GAAiBsF,GAAOQ,cACxBra,GAEFsa,IAAK1L,EACL3O,MAAKrD,KACA+c,GACEpF,GAAiB,CAAEvW,OAAAA,EAAQC,MAAAA,KAGjCwK,GAKP,IAAM8R,GAAY,CAChBC,wCACE,kDACFC,uCACE,iDACFC,wBAAyB,kCACzBC,mBAAoB,6BACpBC,uBAAwB,iCACxBC,uBAAwB,iCACxBC,8BAA+B,wCAC/BC,gCAAiC,0CACjCC,4BAA6B,sCAC7BC,iBAAkB,2BAClBC,2BAA4B,qCAC5BC,qCACE,+CACFC,wCACE,kDACFC,mCACE,6CACFC,oCACE,8CACFC,qCAAsC,+CACtCC,qCAAsC,gDAGlC3B,GAAS3d,EAAWS,OAAO,CAC/Bmd,KAAM,CACJjd,IAAK,CAAC,WAAYrB,EAAWigB,aAC7BC,SAAU,WACVrf,QAAS,OACTsf,cAAe,SACfC,YAAa,MACbC,YAAa,QACbC,aAAc,kCACdC,YAAa,iCACbC,gBAAiB,sBACjBvf,SAAU,SACVqB,WAAY,0DACZme,IAAK,CACHC,UAAW,aACXC,WAAY,eAGhBpC,cAAe,CACbqC,KAAM,CACJ1B,wBAAyB,UACzBE,uBAAwB,UACxBH,uCAAwC,YACxCI,uBAAwB,UACxBF,mBAAoB,UACpBG,8BAA+B,UAC/BE,4BAA6B,UAC7BC,iBAAkB,OAClBC,2BAA4B,YAC5BG,mCAAoC,UACpCC,oCAAqC,YACrCC,qCAAsC,UACtCC,qCAAsC,sBAEtCa,2BAA4B,OAE5BC,6BAA8B,MAG9BC,4BAA6B,6BAC7BC,uBAAwB,qCAGxBC,gDACE,6BACFC,gCAAiC,4BAGjCvB,qCAAsC,mCACtCwB,6BAA8B,SAC9BC,mCAAoC,MACpCC,4BAA6B,OAC7BC,gCAAiC,wBACjCC,uCAAwC,wBACxCC,iCAAkC,gCAGlCC,wCAAyC,OAGzCC,sCAAuC,OACvC1C,wCAAyC,sBAGzC2C,uBAAwB,OACxBC,0BAA2B,OAC3BC,6BAA8B,kCAC9BC,6BAA8B,iCAC9BC,2BAA4B,wBAG5BC,yBAA0B,kCAG1BzC,gCAAiC,YACjC0C,kCAAmC,wBACnCC,+BAAgC,kCAChCC,8BAAkCC,OAGlCC,mBAAoB,OACpBC,sBAAuB,MACvBC,uBACE,6DACFC,0BAA2B,4BAC3B5C,wCAAyC,iCACzC6C,8CAA+C,wBAG/CC,+BAAgC,IAChCC,4CAA6C,IAC7CC,+BAAgC,IAChCC,sCAAuC,IACvCC,wBAAyB,IAGzBC,aAAc,OACdC,6CAA8C,kBAC9CC,6BAA8B,UAC9BC,wBAAyB,6BACzBC,4BAA6B,YAC7BC,4CAA6C,YAC7CC,4BAA6B,UAC7BC,mCAAoC,UACpCC,qCAAsC,YACtCC,iCAAkC,UAClCC,sBAAuB,UACvBC,gCAAiC,YACjCC,0CAA2C,kBAC3CC,6CAA8C,OAC9CC,wCAAyC,UACzCC,yCAA0C,YAC1CC,0CAA2C,sCAC3CC,0CAA2C,cAG/CtF,cAAe,CACbrd,IAAKrB,EAAWikB,UAChB9gB,sCAAuC,CACrCyd,KAAM7B,KAGVP,UAAW,CACTnd,IAAKrB,EAAWwe,UAChBoC,KAAM7B,IAERF,cAAe,CACbxd,IAAK,gBACLmB,OAAQ,OACR3B,QAAS,cACT2f,gBAAiB,gCAEjB0D,eAAgB,YAChBtD,KAAM,CACJE,6BAA8B,oBFxOpB1E,GACd3G,EACAlD,GAEA,IAAKkD,IAAWlD,EACd,OAAO,EAGT,IAAM4R,EAAc1O,EAAO6G,wBAAwB7Z,MAC7C2hB,EAAe7R,EAAQ+J,wBAAwB7Z,MACrD,OAAOia,KAAK2H,MAAMF,EAAcC,YAGlBpI,GACdvG,EACAlD,GAEA,IAAKkD,IAAWlD,EACd,OAAO,EAGT,IAAM6R,EAAe7R,EAAQ+J,wBAAwB7Z,MAC/C6hB,EAAc/R,EAAQ+J,wBAAwBiI,KAC9CC,EAAa/O,EAAO6G,wBAAwBiI,KAElD,OAAO7H,KAAK2H,OAAOC,EAAcE,GAAcJ,YAGjClI,GACdzG,EACAlD,GAEA,IAAKkD,IAAWlD,EACd,OAAO,EAGT,IAAM8J,EAAgB9J,EAAQ+J,wBAAwB9Z,OAChD+Z,EAAahK,EAAQ+J,wBAAwB7H,IAC7C+H,EAAY/G,EAAO6G,wBAAwB7H,IACjD,OAAOiI,KAAKC,OAAOJ,EAAaC,GAAaH,GAmB/C,SAASW,GACPyH,EACAxI,EACAN,GAEA,IAAa,IAATM,EAAY,CACd,IAAMyI,EAAUhI,KAAK2H,OAAOI,EAAS7f,OAAS,GAAK+W,GAGnD,OAAO8I,EAASE,MAFUD,EAAU/I,EACX8I,EAAS7f,OAAS,EACiB,GAG9D,OAAO6f,EAASE,MAAM1I,EAAMN,GAAgBM,EAAM,GAAKN,GAiBzD,SAAgBmB,GACd2H,EACAxI,EACAN,EACAI,GAEA,IAAM6I,EAAc5H,GAAeyH,EAAUxI,EAAKN,GAElD,OAAOiJ,EAAY7I,IAAe6I,EAAYA,EAAYhgB,OAAS,IAAM,SRzF9DigB,YAA+B5kB,EAAYD,EAAW+H,OACtD+c,GAAuB,CAClCD,GACA5kB,EAAYD,EAAW+kB,iBACf9kB,EAAYD,EAAWY,aAC/BH,KAAK,aAESqa,GACdkK,SAEA,cAAAC,QAAOD,SAAAA,EAAc9P,QAAQ2P,KAAoBI,EAAI,cAWvC1N,GACdhF,GAEA,IAAM2S,EAAkBC,GAAgC5S,GAClD3J,EAAUwc,GAAwB7S,GAExC,IAAK2S,EACH,MAAO,GAGT,IAAMnd,EAAQmC,QAAetB,EAAAA,EAAWsc,GAExC,OAAKnd,EAIE,CAACA,EAAOa,GAHN,YA0BKyT,GAAc9J,SAC5B,cAAA8S,QAAO9S,SAAAA,EAAS+S,cAAYD,EAAI,WAGlBE,GAAmBhT,GACjC,IAAKA,EACH,OAAO,EAGT,IAAMsL,EAAS/C,GAAiBvI,GAC1BtM,EAAW+O,GAAgB6I,GAG3B2H,EAAczQ,GAAoB9O,GAExC,OAAOwf,GAAiB5H,GAAU4H,GAAiBxf,GAAYuf,WAGjDzQ,GAAoB9O,WAClC,IAAKA,EACH,OAAO,EAGT,IAAMyf,EAAuBzf,EAAS0f,cACpC1lB,EAAYD,EAAW6b,kBAGzB,cACE+J,QAAC3f,SAAAA,EAAUqf,cAAYM,EAAI,WAACC,QAAKH,SAAAA,EAAsBJ,cAAYO,EAAI,YAe3DlR,GAAgBH,GAC9B,OAAKA,EAEEA,EAAKsR,QAAQ7lB,EAAYD,EAAWoV,aACvCZ,EACAA,EAAKmR,cAAc1lB,EAAYD,EAAWoV,aAJ5B,cAOJN,GAA2B/M,WACzC,OAAKA,EAIEwd,GAAmBxd,WAAMge,SAAAC,EAAI3Q,GAAkBtN,WAAlBie,EAA0BpR,WAASmR,EAAI,GAHlE,WAMK1Q,GAAkB9C,SAChC,OAAKA,UAIL0T,EAAO1T,EAAQ2C,QAAQjV,EAAYD,EAAWoV,cAAY6Q,EAHjD,KAaX,SAASR,GAAiBlT,SACxB,cAAA2T,QAAO3T,SAAAA,EAAS4T,WAASD,EAAI,EAG/B,SAASE,GAAkB7T,SACzB,cAAA8T,QAAO9T,SAAAA,EAAS+T,YAAUD,EAAI,WAGhBjB,GAAwBrd,WACtC,cAAAwe,SAkCAC,EAGF,SAAwBjU,SACtB,cAAAkU,QAAOlU,SAAAA,EAASmU,SAAOD,EAAI,GAJpBE,CAlCkB7L,GAAiB/S,IAkCR,SAACye,EAAI,MAlCqBD,EAAI,cAGlDpB,GACdpd,GAEA,IAAMa,EAAUwc,GAAwBrd,GAExC,OAAIa,EACKD,EAAuBC,GAEzB,cAGOge,GACd7e,GAEA,OAAKA,EAOE,CACLa,QAASwc,GAAwBrd,GACjCmd,gBAAiBC,GAAgCpd,IAR1C,CACLa,QAAS,KACTsc,gBAAiB,eAqBPlK,GAAezI,GAC7B,OAAOA,EAAQsU,UAAUC,SAAS9mB,EAAW+kB,kBAG/BgC,GAASxU,GACvB,OAAKA,GAEEA,EAAQsU,UAAUC,SAAS9mB,EAAWY,iBAG/Bmc,GAAiBtH,GAC/B,OAAKA,EAIEtV,MAAM6mB,KACXvR,EAAOwR,iBAAiBnC,KAJjB,YAQK1J,GAAiB7I,GAC/B,IAAKA,EAAS,OAAO,KAErB,IACO2U,EADWnK,GAAiBxK,GACVoS,OAAO,MAChC,OAAKuC,EAIAlM,GAAekM,GAIbA,EAHE/L,GAAiB+L,GAJjB,cAwBK/L,GAAiB5I,GAC/B,IAAMlP,EAAOkP,EAAQI,uBAErB,OAAKtP,EAIA2X,GAAe3X,GAIbA,EAHE8X,GAAiB9X,GAJjB+X,GAAiBC,GAAa9I,aAUzBmD,GAAkBD,GAChC,OAAKA,EQrHP,SACEA,EACAgP,EACA0C,GAEA,YAFAA,IAAAA,EAA6B,IAExB1R,IAAWgP,EAAS7f,OACvB,OAAO,KAGT,IAAM4X,EAAY/G,EAAO6G,wBAAwB7H,IAC3C2S,EAAe3R,EAAO6G,wBAAwB+K,OAC9CC,EAAqB9K,EA+B7B,SAAwB+K,GAKtB,IAJA,IAIAC,IAAAC,EAJetnB,MAAM6mB,KACnBO,EAAWN,iBAAiBhnB,EAAYD,EAAW0nB,SAG3BF,EAAAC,EAAA7iB,OAAA4iB,IAAE,CAAvB,IACGhlB,EADQilB,EAAAD,GACOlL,wBAAwB9Z,OAE7C,GAAIA,EAAS,EACX,OAAOA,EAIX,OE9KkC,GFkIKmlB,CAAelS,GAwBtD,OAtBwBgP,EAAStb,MAAK,SAAAoJ,GACpC,IAAMgK,EAAahK,EAAQ+J,wBAAwB7H,IAC7CmT,EAAgBrV,EAAQ+J,wBAAwB+K,OAChDQ,EACJtV,EAAQ+S,aAAe6B,EAEnBW,EAA4BvL,EAAasL,EACzCE,EACJH,EAAgBC,EAElB,QAAIC,EAA4BR,KAK7BQ,GAA6BtL,GAC5BsL,GAA6BV,GAC9BW,GAAgCvL,GAC/BuL,GAAgCX,OAIZ,KRwFnBY,CAA+BvS,EAFpBsH,GAAiBtH,GAEsB,IALhD,cAQK4F,GAAa9I,GAC3B,IAAMtM,EAAW+O,GAAgBzC,GAEjC,IAAKtM,EACH,OAAO,KAGT,IAAM5C,EAAO4C,EAAS0M,uBAEtB,OAAKtP,EAID0jB,GAAS1jB,GACJgY,GAAahY,GAGfA,EAPE,cAUK4X,GAAa1I,GAC3B,IAAMtM,EAAW+O,GAAgBzC,GAEjC,IAAKtM,EACH,OAAO,KAGT,IAAM3C,EAAO2C,EAAS4M,mBAEtB,OAAKvP,EAIDyjB,GAASzjB,GACJ2X,GAAa3X,GAGfA,EAPE,cAUK0R,GAAgBzC,GAC9B,OAAKA,EAGEA,EAAQ2C,QAAQjV,EAAYD,EAAWiG,WAFrC,cAKK6V,GAAuBvJ,GACrC,OAAKA,EAGEA,EAAQ2C,QACbjV,EAAYD,EAAW6b,kBAHhB,cY9TKoM,GAAiBrf,GAC/B,OAAOA,EACJR,MAAM,KACN7H,KAAI,SAAA2nB,GAAG,OAAIC,OAAOC,cAAcC,SAASH,EAAK,QAC9CznB,KAAK,aCWM6nB,GAAaC,GAC3B,IAAI,IAAAha,EAAAia,EAAAC,EACF,UAAIla,EAACC,UAAAD,EAAQma,aACX,MAAO,GAET,IAAMC,EAASC,KAAKC,aAAKL,SAAAC,EACvBja,eAAAia,EAAQC,aAAaI,QAhBF,kBAgB2BN,EAAI,MAGpD,OAAID,IAAS1jB,uBAAe2H,SACnBmc,EAAOI,MAAK,SAACC,EAAGC,GAAC,OAAKA,EAAEC,MAAQF,EAAEE,SAGpCP,EACP,MAAAQ,GACA,MAAO,aChBKC,GAAcrhB,GAC5B,YAAwBshB,IAAjBthB,EAAMkE,gBCqBCqd,GACdC,EACAC,GAEA,IAAMC,EAAoBvb,WACpB2H,EAAqBuB,KACrBzH,ExB+DuBpO,aAAiBmP,IAAtCf,iBwB9DC2H,EAA2BrG,QAC9BkH,EAAsBxC,KACrBxF,EAAkBY,QACnB2Y,W1BkCNF,GAEA,IAAQnb,EAAYmD,KAAZnD,QACCsb,EAAoB9Y,QAEvB+Y,EAAUvb,EAAQqb,cAAiB,aACjCG,EAAoBxb,EAApBwb,gBAER,OAAIL,IAAqBnY,GAAmByY,WAAaD,EAChD,WAAA,QAAAzpB,EAAAE,UAAAsE,OAAImlB,MAAI5pB,MAAAC,GAAAC,IAAAA,EAAAD,EAAAC,IAAJ0pB,EAAI1pB,GAAAC,UAAAD,GAAA,OACbwpB,EAAeG,aAAID,EAAIE,QAAE,CACvBC,oBAAqB,WACnBP,GAAiB,SAAAQ,GAAC,OAAIA,WAKvB,sCAAIJ,MAAI5pB,MAAAiqB,GAAAC,IAAAA,EAAAD,EAAAC,IAAJN,EAAIM,GAAA/pB,UAAA+pB,GACbT,EAAOI,aAAID,EAAIE,QAAE,CACfC,oBAAqB,WACnBP,GAAiB,S0BtDFW,CAAsBd,GAClCe,EAAmBpZ,QACtB9E,EAAc6F,KACdsY,EAAmB3Y,KAEnB4Y,EAAUlpB,eACd,SAAiB6W,GACf,IAAIzI,EAAiBtB,QAArB,CAIA8J,IAEA,IAAAuS,EAAyBC,GAAevS,GAAjCrQ,EAAK2iB,KAAE9hB,EAAO8hB,KAErB,GAAK3iB,GAAUa,EAAf,CAIA,IAAMgiB,WhC0E+BhiB,GACzC,IAASiiB,EAAqBjiB,EAAQR,MAAM,QAC5C,OAAOvB,EAAmBwC,SAASwhB,GAC/BA,EACA,KgC7EEC,CAA2BliB,IAAYuH,EAEzCoa,aFlCuBxiB,EAAkBJ,GAC7C,IAOIojB,EAPEpC,EAASL,KAET1f,EAAUJ,EAAaT,EAAOJ,GAC9Bud,EAAkB1c,EAAaT,GAEjCijB,EAAWrC,EAAOxf,MAAK,SAAA3B,GAAU,OAAGA,EAAVoB,UAAuBA,KAKnDmiB,EADEC,EACS,CAACA,GAAUf,OAAOtB,EAAOsC,QAAO,SAAAC,GAAC,OAAIA,IAAMF,OAEtDA,EAAW,CACTpiB,QAAAA,EACAuiB,SAAUjG,EACVgE,MAAO,IAEWe,OAAKtB,GAG3BqC,EAAS9B,QAET6B,EAASnmB,OAAS8X,KAAK0O,IAAIL,EAASnmB,OAAQ,IAE5C,IAAI,IAAAymB,SACFA,EAAA7c,SAAA6c,EAAQ3C,aAAa4C,QAvDA,gBAuD0B1C,KAAK2C,UAAUR,IAE9D,MAAAS,KEOEC,CAAa1jB,EAAO6iB,GACpBlB,EAgGN,SACE3hB,EACAoI,EACAqa,EACAne,GAEA,IAAMP,EAAQ9D,EAAWD,GAEzB,GAAIqhB,GAAcrhB,GAAQ,CACxB,IAAMa,EAAUJ,EAAaT,GAC7B,MAAO,CACLoI,eAAAA,EACApI,MAAOa,EACP8iB,uBACE,OAAO3jB,EAAMkE,QAEf0f,SAAU5jB,EAAMkE,OAChB2f,UAAU,EACV9f,MAAAA,EACAlD,QAAAA,EACAD,uBAAwBC,GAG5B,IAAMA,EAAUJ,EAAaT,EAAOoI,GAEpC,MAAO,CACLA,eAAAA,EACApI,MAAOkgB,GAAiBrf,GACxB8iB,qBAAYtnB,GACV,gBADUA,IAAAA,QAAyBomB,EAAAA,EAAoB1lB,mBAAWgF,OAC3DuC,EAAYzD,EAASxE,IAE9BunB,SAAUtf,EAAYzD,QAAS4hB,EAAAA,EAAoB1lB,mBAAWgF,OAC9D8hB,UAAU,EACV9f,MAAAA,EACAlD,QAAAA,EACAD,uBAAwBH,EAAaT,IAlIjC8jB,CAAiB9jB,EAAO6iB,EAAeJ,EAAkBne,GACzD+L,OAGJ,CACEjI,EACAgI,EACAxI,EACA+Z,EACAa,EACAle,EACAme,IAIEsB,EAAcvqB,eAClB,SAAqB6W,SACfqR,EAAkBpb,SACpBC,aAAamb,EAAkBpb,SAGjC,IAAOtG,EAAS4iB,GAAevS,MAE1BrQ,GAAUiB,EAAmBjB,KAIlC0hB,EAAkBpb,eAAOE,EAAGC,eAAAD,EAAQlE,YAAW,WAC7CsF,EAAiBtB,SAAU,EAC3Bob,EAAkBpb,aAAUgb,EAC5BlR,IACAtC,EAAmBuC,EAAMlE,QACzBoD,EAAwBvP,KACvB,QAEL,CACE4H,EACAwI,EACAtC,EACAyB,IAGEyU,EAAYxqB,eAChB,WACMkoB,EAAkBpb,SACpBC,aAAamb,EAAkBpb,SAC/Bob,EAAkBpb,aAAUgb,GACnB1Z,EAAiBtB,SAO1BmE,uBAAsB,WACpB7C,EAAiBtB,SAAU,OAIjC,CAACsB,IAGHc,aAAU,WACR,GAAK8Y,EAAalb,QAAlB,CAGA,IAAM2d,EAAezC,EAAalb,QAYlC,OAXA2d,EAAapS,iBAAiB,QAAS6Q,EAAS,CAC9CwB,SAAS,IAGXD,EAAapS,iBAAiB,YAAakS,EAAa,CACtDG,SAAS,IAEXD,EAAapS,iBAAiB,UAAWmS,EAAW,CAClDE,SAAS,IAGJ,iBACLD,GAAAA,EAAcnS,oBAAoB,QAAS4Q,SAC3CuB,GAAAA,EAAcnS,oBAAoB,YAAaiS,SAC/CE,GAAAA,EAAcnS,oBAAoB,UAAWkS,OAE9C,CAACxC,EAAckB,EAASqB,EAAaC,IAG1C,SAASpB,GAAevS,GACtB,If3G6B7F,Ie2GvB2B,QAASkE,SAAAA,EAAOlE,OACtB,Of5G6B3B,Ee4GT2B,Ef3Gb3E,eACLgD,SAAAA,EAASuT,QAAQjB,aACftS,UAAO2Z,EAAP3Z,EAAS4Z,sBAATD,EAAwBpG,QAAQjB,Me6G7BtN,GAAiBrD,GAHf,YCnJKkY,GAAOC,GACrB,OACE9qB,wCACE+qB,KAAK,UACDD,GACJ7nB,UAAW4Z,KAAGC,GAAOR,OAAQwO,EAAM7nB,aAElC6nB,EAAMpf,UAKb,IAAMoR,GAAS3d,EAAWS,OAAO,CAC/B0c,OAAQ,CACNxc,IAAK,UACLkrB,OAAQ,UACRC,OAAQ,IACRC,WAAY,OACZC,QAAS,mBCRGC,GAAoBnlB,SAClCQ,EAAUR,EAAVQ,WACAY,EAAOpB,EAAPoB,QACAhI,EAAM4G,EAAN5G,OACAsB,EAAcsF,EAAdtF,eAAc0qB,EAAAplB,EACdqlB,eAAAA,WAAcD,GAAOA,EACrBE,EAAatlB,EAAbslB,cACA7f,EAAQzF,EAARyF,SACAzI,EAASgD,EAAThD,UAASuoB,EAAAvlB,EACTwlB,aAAAA,WAAYD,GAAQA,EAEpB,OACExrB,gBAAC6qB,IACC5nB,UAAW4Z,KACTC,GAAOtW,MACPnH,GAAUM,EAAaN,OACvBsB,GAAkBN,EAAwBM,gBAAcyc,KAAAA,EAErD3e,EAAW+kB,UAAWnkB,IAAWsB,EAAcyc,MAE/CmO,IAAiBD,IAAmBxO,GAAOyO,cAC9CE,GAAgB3O,GAAO2O,aACvBxoB,kBAEYoE,eACFqkB,GAAajlB,oBACTA,GAEfiF,GAKP,SAASggB,GAAajlB,SACpB,OAAOA,EAAW,GAAGyQ,MAAM,iBAAQyU,EAC/BllB,EAAW,IAAEklB,EACbllB,EAAW,GAGjB,IAAMqW,GAAS3d,EAAWS,OAAO,CAC/B4G,MAAO,CACL1G,IAAKrB,EAAW+H,MAChBmY,SAAU,WACVzd,MAAO,4BACPD,OAAQ,4BACRke,UAAW,aACX7f,QAAS,OACTssB,WAAY,SACZC,eAAgB,SAChBC,SAAU,4BACVC,UAAW,4BACXhN,aAAc,MACdrf,SAAU,SACVqB,WAAY,wBACZN,SAAU,CACRwe,gBAAiB,gCAEnB+M,SAAU,CACR/M,gBAAiB,8BAGrBwM,aAAc,CACZP,WAAY,OACZzqB,SAAU,CACRwe,gBAAiB,cACjBiM,WAAY,QAEdc,SAAU,CACR/M,gBAAiB,cACjBiM,WAAY,SAGhBK,cAAe,CACbzrB,IAAKrB,EAAWgJ,mBAChBwkB,SAAU,CACRC,QAAS,GACT5sB,QAAS,QACT4B,MAAO,IACPD,OAAQ,IACRkrB,MAAO,MACPrG,OAAQ,MACRnH,SAAU,WACVyN,WAAY,wBACZC,YAAa,wBACbC,UAAW,iBACXC,aAAc,uDACdC,OAAQ,kDAEVC,eAAgB,CACdF,aAAc,iEC7GPG,GAAcvtB,EAAWS,OAAO,CAC3C+sB,SAAU,CACR7sB,IAAKrB,EAAWkuB,SAChBC,SAAU,KAEZC,OAAQ,CACNC,UAAW,SACXC,YAAa,SACbztB,QAAS,oBCHG0tB,GAAQ/mB,OAEtB/C,EAAK+C,EAAL/C,MAAK+pB,EAAAhnB,EACLinB,SAAAA,WAAQD,GAAQA,EAEhBE,EAAOlnB,EAAPknB,QAWA,OACEntB,uBACEyc,IAdExW,EAANyE,OAeI0iB,IAlBKnnB,EAATkB,UAmBIlE,UAAW4Z,KAAGC,GAAOuQ,UAAWX,GAAYC,SAAUD,GAAYG,OAd7D5mB,EAAThD,WAeIqqB,QAASJ,EAAW,OAAS,QAC7BC,QAASA,EACTjqB,MAAOA,IAKb,IAAM4Z,GAAS3d,EAAWS,OAAO,CAC/BytB,UAAW,CACTvtB,IAAK,gBACLgsB,SAAU,4BACVC,UAAW,4BACXwB,SAAU,4BACVC,UAAW,4BACXC,QAAS,uCCnCGC,GAAWznB,OACzBoB,EAAOpB,EAAPoB,QACAnE,EAAK+C,EAAL/C,MAOA,OACElD,wBACEiD,UAAW4Z,KACTC,GAAO6Q,YACPjB,GAAYG,OACZH,GAAYC,SAXT1mB,EAAThD,0BAckBoE,EACdnE,MAAOA,GAENwjB,GAAiBrf,IAKxB,IAAMyV,GAAS3d,EAAWS,OAAO,CAC/B+tB,YAAa,CACX7tB,IAAK,mBACLsf,WACE,2JACFT,SAAU,WACViP,WAAY,OACZhB,SAAU,wBACViB,UAAW,SACXf,UAAW,SACXC,YAAa,SACbe,cAAe,IACfL,QAAS,uCC9BGM,GAAa9nB,OAC3BO,EAAKP,EAALO,MACAa,EAAOpB,EAAPoB,QACAxE,EAAUoD,EAAVpD,WACAmrB,EAAI/nB,EAAJ+nB,KACAd,EAAQjnB,EAARinB,SAAQe,EAAAhoB,EACR6E,YAAAA,WAAWmjB,EAAG9lB,EAAiB8lB,EAC/BhrB,EAASgD,EAAThD,UAESirB,EAA6Bze,QAEhCvM,EAAQ,GACV8qB,IACF9qB,EAAMhC,MAAQgC,EAAMjC,OAASiC,EAAM0pB,SAAcoB,QAGnD,IAAMG,EAAgB3nB,GAAgBmC,EAAetB,GAErD,OAAK8mB,EAIDtG,GAAcsG,GAEdnuB,gBAACgtB,IACC9pB,MAAOA,EACPiE,UAAWE,EACXxE,WAAYU,mBAAW4Y,OACvB+Q,SAAUA,EACVxiB,OAAQyjB,EAAczjB,OACtByiB,QAASA,EACTlqB,UAAWA,IAMfjD,gCACG6C,IAAeU,mBAAW4Y,OACzBnc,gBAAC0tB,IAAYrmB,QAASA,EAASnE,MAAOA,EAAOD,UAAWA,IAExDjD,gBAACgtB,IACC9pB,MAAOA,EACPiE,UAAWA,EAAUgnB,GACrBtrB,WAAYA,EACZqqB,SAAUA,EACVxiB,OAAQI,EAAYzD,EAASxE,GAC7BsqB,QAASA,EACTlqB,UAAWA,KA7BV,KAmCT,SAASkqB,IACPe,GAA0B,SAAApsB,GAAI,OAAI,IAAIoJ,IAAIpJ,GAAMsI,IAAI/C,gBClDxC+mB,GAAcnoB,OAC5BO,EAAKP,EAALO,MACAa,EAAOpB,EAAPoB,QACAhI,EAAM4G,EAAN5G,OACAsB,EAAcsF,EAAdtF,eACAkC,EAAUoD,EAAVpD,WAAUwoB,EAAAplB,EACVqlB,eAAAA,WAAcD,GAAOA,EACrB2C,EAAI/nB,EAAJ+nB,KACAd,EAAQjnB,EAARinB,SACApiB,EAAW7E,EAAX6E,YACA7H,EAASgD,EAAThD,UAASuoB,EAAAvlB,EACTwlB,aAAAA,WAAYD,GAAQA,EAEdD,EAAgB9jB,EAAmBjB,GAEzC,OACExG,gBAACorB,IACCG,cAAeA,EACfD,eAAgBA,EAChBjsB,OAAQA,EACRsB,eAAgBA,EAChB8F,WAAYA,EAAWD,GACvBa,QAASA,EACTokB,aAAcA,GAEdzrB,gBAAC+tB,IACC1mB,QAASA,EACTb,MAAOA,EACPwnB,KAAMA,EACNnrB,WAAYA,EACZqqB,SAAUA,EACVpiB,YAAaA,EACb7H,UAAWA,cC3CHorB,KACd,IAASC,EAAoBhf,QAC7B,OACEtP,gBAAC6qB,iBACY,kBACX0D,MAAM,kBACNC,SAAU,EACVvrB,UAAW4Z,KAAGC,GAAO2R,UACrBvF,QAAS,WAAA,OAAMoF,GAAiB,MAKtC,IAAMxR,GAAS3d,EAAWS,OAAMC,GAC9B4uB,SAAU,CACR7B,SAAU,OACVa,QAAS,OACTiB,MAAO,wBACP3P,aAAc,MACd8O,UAAW,SACXD,WAAY,OACZ1sB,MAAO,OACPD,OAAQ,OACR3B,QAAS,OACTusB,eAAgB,SAChBD,WAAY,SACZ7qB,WAAY,oCACZkrB,SAAU,CACRC,QAAS,GACTqB,SAAU,OACVC,UAAW,OACXmB,8lEACA1P,gBAAiB,cACjB2P,iBAAkB,YAClBC,eAAgB,OAChBnuB,oBAAqB,KAEvBD,SAAU,CACRiuB,MAAO,6BACPzP,gBAAiB,4CACjBgN,SAAU,CACRvrB,oBAAqB,UAGzBsrB,SAAU,CACR0C,MAAO,6BACPzP,gBAAiB,4CACjBgN,SAAU,CACRvrB,oBAAqB,YAIxBW,EAAS,WAAY,CACtB4qB,SAAU,CAAEvrB,oBAAqB,SACjC+rB,eAAgB,CAAE/rB,oBAAqB,sBC3C3BouB,KACd,IAAOC,EAAiBzf,QAClB8C,E3BqECG,KAA8B,a2BpE/BpH,EnCkJgBe,KAAdf,UmCjJR4c,GAAqB3V,EAActC,GAAmByY,WACtD,IAAM1lB,EAAayN,KACblF,EAAuBgF,KACvBtF,EAAc6F,KAEpB,OAAKoe,EAKH/uB,sBACEiD,UAAW4Z,KAAGC,GAAOkS,MAAOD,GAAiBpvB,EAAaN,QAC1Dke,IAAKnL,GAEJjH,EAAUnM,KAAI,SAAAiwB,GAAQ,OACrBjvB,sBAAIsB,IAAK2tB,GACPjvB,gBAACouB,IACC5nB,MAAOmC,EAAesmB,GACtBpsB,WAAYA,EACZwE,QAAS4nB,EACT3D,gBAAgB,EAChBroB,UAAW4Z,KAAGC,GAAOoS,aACrBzD,gBACA3gB,YAAaA,QAIlBM,EACCpL,0BACEA,gBAACquB,UAED,MAzBC,KA8BX,IAAMvR,GAAS3d,EAAWS,OAAO,CAC/BovB,KAAM,CACJG,UAAW,OACXC,OAAQ,IACR3B,QAAS,QACTnuB,QAAS,OACTusB,eAAgB,gBAChBD,WAAY,SACZ3qB,OAAQ,QAEViuB,YAAa,CACXzuB,SAAU,CACR6rB,UAAW,cAEbN,SAAU,CACRM,UAAW,cAEb+C,UAAW,CACT/C,UAAW,cAEbvrB,WAAY,mEC1DAuuB,GAAarpB,OAC3BspB,EAActpB,EAAdspB,eACA7jB,EAAQzF,EAARyF,SACArM,EAAM4G,EAAN5G,OACAsB,EAAcsF,EAAdtF,eAEM+D,EAAWK,EAA2BwqB,GACtCC,EAAexqB,EAA+BuqB,GAEpD,OACEvvB,sBACEiD,UAAW4Z,KACTC,GAAOpY,SACPrF,GAAUM,EAAaN,OACvBsB,GAAkBN,EAAwBM,4BAEjC+D,eACC8qB,GAEZxvB,sBAAIiD,UAAW4Z,KAAGC,GAAOqJ,QAASqJ,GAClCxvB,uBAAKiD,UAAW4Z,KAAGC,GAAOxC,kBAAmB5O,IAKnD,IAAMoR,GAAS3d,EAAWS,OAAO,CAC/B8E,SAAU,CACR5E,IAAKrB,EAAWiG,SAChB+qB,2BAA4B,CAC1BnwB,QAAS,SAGbgb,gBAAiB,CACfxa,IAAKrB,EAAW6b,gBAChBhb,QAAS,OACTowB,QAAS,IACTC,oBAAqB,+CACrB9D,eAAgB,gBAChBuD,OAAQ,8BACRzQ,SAAU,YAEZwH,MAAO,CACLrmB,IAAKrB,EAAW0nB,MAChByF,WAAY,SAEZjJ,eAAgB,YAChB1D,gBAAiB,qCACjByP,MAAO,uCACPpvB,QAAS,OACTstB,SAAU,OACVgD,WAAY,OACZ3uB,OAAQ,mCACRmuB,OAAQ,IACR3B,QAAS,oCACT9O,SAAU,SACVkR,cAAe,aACf3c,IAAK,IACLhS,MAAO,OACPsrB,OAAQ,uCC9ERsD,IAAgB,WCmBJC,GAAS9pB,OAAGspB,EAActpB,EAAdspB,eACnBS,EAAoBpgB,QACrBqgB,EDnBR,WACE,IAAArkB,EAAkC5L,WAAe8vB,IAA1CG,EAASrkB,KAAEskB,EAAYtkB,KAO9B,OALA5L,aAAgB,WACdkwB,GAAa,GACbJ,IAAgB,IACf,IAEIG,GAAaH,GCWFK,GACZC,EtCoG0BlkB,KAAxBnJ,oBsCnGF+H,EAAc6F,KACd0f,EAAYrwB,WAChB,WAAA,IAAAswB,EAAA,cAAAA,EAAMvJ,GAAaqJ,IAA0BE,EAAI,KAEjD,CAACN,EAAkBI,IAEfvtB,EAAayN,KACbigB,EAAoB1iB,KAE1B,OAAKoiB,EAKHjwB,gBAACsvB,IACCC,eAAgBA,EAChB5uB,kBACAtB,OAA6B,IAArBgxB,EAAUhtB,QAEjBgtB,EAAUrxB,KAAI,SAAAwxB,GACb,IAAMhqB,EAAQmC,EAAe6nB,EAAc5G,UAE3C,OAAKpjB,EAID+pB,EAAkB/pB,GACb,KAIPxG,gBAACouB,IACC9C,gBAAgB,EAChBjkB,QAASmpB,EAAcnpB,QACvBxE,WAAYA,EACZ2D,MAAOA,EACPlF,IAAKkvB,EAAcnpB,QACnByD,YAAaA,IAdR,SAbN,cCNK2lB,KACd,IAAM9mB,EAAa4G,KACbmgB,EAA4B1wB,SAAa,GAE/C,OACEA,sBAAIiD,UAAW4Z,KAAGC,GAAO6T,YACtBhnB,EAAW3K,KAAI,SAAAuwB,GACd,IAAM7qB,EAAWK,EAA2BwqB,GAE5C,OAAI7qB,IAAahB,mBAAWI,UACnB9D,gBAAC+vB,IAAUzuB,IAAKoD,EAAU6qB,eAAgBA,IAIjDvvB,gBAACA,YAAesB,IAAKoD,GACnB1E,gBAAC4wB,IACClsB,SAAUA,EACV6qB,eAAgBA,EAChBmB,0BAA2BA,SASzC,SAASE,GAAc3qB,OvBqFJyjB,EACVhb,EwBvIAmiB,EACDC,EDiDNpsB,EAAQuB,EAARvB,SACA6qB,EAActpB,EAAdspB,eACAmB,EAAyBzqB,EAAzByqB,0BAMMK,GC1DCF,EAA0BphB,QxBsIhBia,EAAWta,KAApBtC,QACD4B,EAAca,QwBtIfuhB,ExBwIC,SAAAzpB,GAAO,OAGhB,SACEA,EACAqiB,EACAhb,SAEA,SAAKgb,IAAWhb,UAITsiB,EAACtH,EAAOhb,KAAPsiB,EAAqB3pB,IAZX4pB,CAA4B5pB,EAASqiB,EAAQhb,IwBtIxD,SAAClI,GACN,IAAMa,EAAUJ,EAAaT,GAEvB0qB,EAAeL,EAAuB9iB,IAAI1G,GAC1C8pB,EAAcL,EAAgBzpB,GAEpC,MAAO,CACL6pB,aAAAA,EACAC,YAAAA,EACA9xB,OAAQ6xB,GAAgBC,KD+CtBnuB,EvC+DqBkJ,KAAnBlJ,euC9DFH,EAAayN,KACbrB,ErCsEwBjP,aAAiBmP,IAAvCF,kBqCrEDL,EAAkBY,QACnB+gB,EAAoB1iB,KACpB/C,EAAc6F,KACd2a,GAAkBjb,KAIlB+gB,GACHniB,GAAqByhB,EAA0B5jB,QAAU,EACtD,GACA9E,EAAiBtD,GAEnB0sB,EAAa/tB,OAAS,GACxBqtB,EAA0B5jB,UAG5B,IAAIukB,EAAgB,EAEdnpB,EAASkpB,EAAapyB,KAAI,SAAAwH,GAC9B,IAAMa,EAAUJ,EAAaT,EAAOoI,GACpC0iB,EAA8CP,EAAcvqB,GAApD0qB,EAAYI,EAAZJ,aAAcC,EAAWG,EAAXH,YAAa9xB,EAAMiyB,EAANjyB,OAE7BkyB,EAAehB,EAAkB/pB,GAMvC,OAJInH,GAAUkyB,IACZF,IAGEE,EACK,KAIPvxB,gBAACouB,IACC9C,eAAgBA,EAChBhqB,IAAK+F,EACLb,MAAOA,EACPa,QAASA,EACThI,OAAQ6xB,EACRvwB,eAAgBwwB,EAChBtuB,WAAYA,EACZqqB,SAAUlqB,EACV8H,YAAaA,OAKnB,OACE9K,gBAACsvB,IACCC,eAAgBA,EAGhBlwB,OAAQgyB,IAAkBnpB,EAAO7E,QAEhC6E,GAKP,IE/FKspB,GF+FC1U,GAAS3d,EAAWS,OAAO,CAC/B+wB,UAAW,CACT7wB,IAAKrB,EAAWkyB,UAChBxB,UAAW,OACXC,OAAQ,IACR3B,QAAS,OE9Fb,SAAgBgE,KACd,IA6BIve,EAAKwe,EA7BH5f,EAAmBW,KACnBN,EjCkECI,KAAoC,mBiCjEpC/L,EAASkJ,QACV7M,EAAayN,KAEnBqhB,EAwFF,SACExf,GAEA,IAAML,EAAmBW,KACnBV,EAAUa,KACZgf,EAAYJ,GAAUK,GAE1B,MAAO,CACLC,iBAIF,WACE,OAAOF,GAJPG,OAOF,WACEH,EAAYJ,GAAUK,GACtB,IAAIG,EAAiB,EAErB,IAAK7f,EAAmBrF,QACtB,OAAO,EAGT,IAAM7L,EAAS6Z,GAAc3I,EAAmBrF,SAEhD,GAAIgF,EAAiBhF,QAAS,CAAA,IAAAmlB,EACtB7V,EAAUrK,EAAQjF,QAClBwP,EAAS/C,GAAiBzH,EAAiBhF,SAE3ColB,EAAepX,GAAcwB,GAEnC0V,EAAiBhO,GAAmB1H,WAErB2V,QAAG7V,SAAAA,EAAS/I,WAAS4e,EAAI,GAExBD,EAAiB/wB,IAC/B2wB,EAAYJ,GAAUW,KACtBH,GAAkBE,EAAejxB,GAIrC,OAAO+wB,EAAiB/wB,IAlIWmxB,CACnCjgB,GADM4f,EAAMJ,EAANI,OAAQD,EAAgBH,EAAhBG,iBAGVhc,EAAsBpD,KACtB2f,EA2DR,SAAyBlgB,GACvB,IAAML,EAAmBW,KACzB,OAAO,WACL,IAAMvP,EAA6B,GACnC,IAAKiP,EAAmBrF,QACtB,OAAO5J,EAGT,GAAI4O,EAAiBhF,QAAS,CAC5B,IAAMwP,EAAS/C,GAAiBzH,EAAiBhF,SAE3CiY,W9BmBuB/T,GACjC,IAAMsL,EAAS/C,GAAiBvI,GAC1BtM,EAAW+O,GAAgB6I,GAEjC,OAAOuI,GAAkBvI,GAAUuI,GAAkBngB,G8BvB9B4tB,CAAmBhW,GAEtC,IAAKA,EACH,OAAOpZ,EAITA,EAAM8f,KAAO+B,SAAazI,SAAAA,EAAQiW,aAAc,EAGlD,OAAOrvB,GAhFesvB,CAAgBrgB,GAClCrH,EAAc6F,KAEd2L,EAAS/C,GAAiBzH,EAAiBhF,SAE3C0W,EAAUxV,QACdxH,GACE8V,GACA7U,EAAmBjB,IACnB8V,EAAOgJ,UAAUC,SAAS9mB,EAAWgJ,qBAoBzC,OAjBAyH,aAAU,WACHsU,GAILvP,GAAuB9B,EAAmBrF,WACzC,CAACqF,EAAoBqR,EAAS1R,KAI5B0R,GAAW1R,EAAiBhF,QAC/BgJ,EAAoB,OAEpB5C,EAAM6e,IACNL,EAAeW,KAIfryB,uBACEud,IAAKpL,EACLlP,UAAW4Z,KACTC,GAAOlJ,gBACPke,MAAuBN,GAAUW,MAAQrV,GAAO2V,WAChDjP,GAAW1G,GAAO0G,SAEpBtgB,MAAO,CAAEgQ,IAAAA,IAERsQ,GAAWhd,EACR,CAACS,EAAaT,IACXkiB,OAAO/gB,EAAgBnB,IACvB4c,MAAM,EAAG,GACTpkB,KAAI,SAAAqI,GAAO,OACVrH,gBAACouB,IACC9sB,IAAK+F,EACLb,MAAOA,EACPa,QAASA,EACTxE,WAAYA,EACZyoB,gBAAgB,EAChBxgB,YAAaA,OAGnB,KACJ9K,uBAAKiD,UAAW4Z,KAAGC,GAAO4V,SAAUxvB,MAAOwuB,MAtEjD,SAAKF,GACHA,eACAA,mBAFF,CAAKA,KAAAA,QAkJL,IAAM1U,GAAS3d,EAAWS,OAAMC,GAC9B+T,gBAAiB,CACf9T,IAAKrB,EAAWmV,gBAChB+K,SAAU,WACVwN,MAAO,OACPnJ,KAAM,OACNyK,QAAS,MACTkF,UAAW,iCACX5T,aAAc,MACdzf,QAAS,OACTssB,WAAY,SACZC,eAAgB,eAChBtsB,QAAS,IACTE,WAAY,SACZD,cAAe,OACf0T,IAAK,QACL+X,OAAQ,2CACRhqB,OAAQ,2CACRurB,OAAQ,2CACRtB,WAAY,6CACZoB,UAAW,aACXvrB,WAAY,kDAEdyiB,QAAS,CACPjkB,QAAS,IACTE,WAAY,UACZD,cAAe,MACf8sB,UAAW,YAEbmG,WAAY,CACV3yB,IAAK,cACL8yB,gBAAiB,YACjBtG,UAAW,cAEbuG,eAAgB,CACdH,QAAS,CACPxf,IAAK,IACLoZ,UAAW,qDAGfoG,QAAS,CACP5yB,IAAK,oBACLosB,QAAS,GACTvN,SAAU,WACVzd,MAAO,OACPD,OAAQ,OACR2tB,iBAAkB,YAClBkE,mBAAoB,MACpBjE,eAAgB,YAChB3b,IAAK,OACLoZ,UAAW,oBACXqC,o6BAECttB,EAAS,UAAW,CACrByxB,mBAAoB,uBCxNRC,KACd,IAAMhhB,EAAUa,KAKhB,gBCjB0Bb,GAC1B,IAAM6E,EAAsBxC,KAE5BlF,aAAU,WACR,IAAMkN,EAAUrK,EAAQjF,QACxB,GAAKsP,EAYL,OARAA,EAAQ/D,iBAAiB,SAAU2a,EAAU,CAC3CtI,SAAS,IAOJ,iBACLtO,GAAAA,EAAS9D,oBAAoB,SAAU0a,IALzC,SAASA,IACPpc,OAMD,CAAC7E,EAAS6E,IDPbqc,CAAYlhB,GACZgW,GAAqBhW,EAASjC,GAAmBojB,Q5BMnD,WACE,IAAMnhB,EAAUa,KACVugB,EAAiBve,KACjBwe,EAAoBte,KAE1B5F,aAAU,WACR,IAAMkN,EAAUrK,EAAQjF,QAKxB,SAASumB,IACHD,KACFD,IAGJ,aATA/W,GAAAA,EAAS/D,iBAAiB,YAAagb,EAAa,CAClD3I,SAAS,IAQJ,iBACLtO,GAAAA,EAAS9D,oBAAoB,YAAa+a,MAE3C,CAACthB,EAASohB,EAAgBC,I4BxB7BE,GAGEtzB,uBACEiD,UAAW4Z,KAAGC,GAAOyW,KAAMlzB,EAAwBS,mBACnDyc,IAAKxL,GAEL/R,gBAACyxB,SACDzxB,gBAACywB,UAKP,OE7BY+C,GF6BN1W,GAAS3d,EAAWS,OAAO,CAC/B2zB,KAAM,CACJzzB,IAAKrB,EAAWoV,WAChB4f,KAAM,IACNC,UAAW,SACXC,UAAW,SACXhV,SAAU,uBEvBUiV,GAAI3tB,OAC1ByF,EAAQzF,EAARyF,SACAzI,EAASgD,EAAThD,UAAS4wB,EAAA5tB,EACT/C,MAAU4wB,EAAA7tB,EACV2rB,UAAAA,WAASkC,EAAGN,GAAcO,IAAGD,EAE7B,OACE9zB,uBACEkD,MAAKrD,cALJg0B,EAAG,GAAEA,GAMN5wB,UAAW4Z,KAAGC,GAAO2W,KAAMxwB,EAAW6Z,GAAO8U,KAE5ClmB,IAvBP,SAAY8nB,GACVA,gBACAA,sBAFF,CAAYA,KAAAA,QA4BZ,IAAM1W,GAAS3d,EAAWS,SAAMo0B,IAC9BP,KAAM,CACJn0B,QAAS,UAEVk0B,GAAcO,KAAM,CACnBnV,cAAe,OAChBoV,GACAR,GAAcS,QAAS,CACtBrV,cAAe,UAChBoV,cCjCqBhY,GAAK/V,OAAGhD,EAASgD,EAAThD,UAAS4wB,EAAA5tB,EAAE/C,MACzC,OAAOlD,uBAAKkD,MAAKrD,GAAI4zB,KAAM,YADmBI,EAAG,GAAEA,GACT5wB,UAAW4Z,KAAG5Z,cCFlCixB,GAAQjuB,OAAGyF,EAAQzF,EAARyF,SAAUzI,EAASgD,EAAThD,UAC3C,OACEjD,uBAAKkD,MAAKrD,KAF+CoG,EAAL/C,OAE5Byb,SAAU,aAAc1b,UAAWA,GACxDyI,YCHiByoB,GAAQluB,OAAGyF,EAAQzF,EAARyF,SAAUzI,EAASgD,EAAThD,UAC3C,OACEjD,uBAAKkD,MAAKrD,KAF+CoG,EAAL/C,OAE5Byb,SAAU,aAAc1b,UAAWA,GACxDyI,YCMS0oB,GAAoBnuB,OAGlCouB,EAAQpuB,EAARouB,SACAC,EAAiBruB,EAAjBquB,kBAGA,OACEt0B,gBAAC6qB,IACC3nB,MAJC+C,EAAL/C,MAKIgmB,QARGjjB,EAAPijB,QASIjmB,UAAW4Z,iBACGyX,EACZxX,GAAOyX,MAZPtuB,EAAN6S,QAaiBgE,GAAO0X,WAClBH,GAAYvX,GAAO2X,uBAEPJ,4BACWxuB,EAAeyuB,KAK9C,ICsEYI,GDtEN5X,GAAS3d,EAAWS,OAAO,CAC/B40B,WAAY,CACVj1B,QAAS,IACTitB,OAAQ,KAEViI,OAAQ,CACN30B,IAAK,aACL0sB,OAAQ,IACRjtB,QAAS,KAEXg1B,KAAM,CACJz0B,IAAK,WACLoB,MAAO,4BACP5B,QAAS,QACT0rB,OAAQ,UACRjM,aAAc,MACd9d,OAAQ,4BACR0d,SAAU,WACVwN,MAAO,IACPprB,WAAY,wDACZyrB,OAAQ,IACRvB,OAAQ,oDACR0H,UAAW,8DACXlyB,SAAU,CACRkyB,UAAW,6GAEb3G,SAAU,CACR2G,UAAW,uCAEbgC,qBAAsB,CACpB1V,gBAAiB,WAEnB2V,mBAAoB,CAClB3V,gBAAiB,WAEnB4V,mBAAoB,CAClB5V,gBAAiB,WAEnB6V,mBAAoB,CAClB7V,gBAAiB,WAEnB8V,mBAAoB,CAClB9V,gBAAiB,WAEnB+V,mBAAoB,CAClB/V,gBAAiB,cCxDvB,SAAgBgW,KACd,OACEj1B,gBAACm0B,IAASjxB,MAAO,CAAEjC,OARL,KASZjB,gBAACk0B,IAAShxB,MAAO,CAAE4iB,OAAQ,EAAGqG,MAAO,IACnCnsB,gBAACk1B,IAAetD,UAAW8C,GAAwBS,sBAM3CD,GAAcjvB,WAC5B2rB,UAAAA,WAASkC,EAAGY,GAAwBU,WAAUtB,EAExC7hB,EAAoBa,KACpBuiB,EAAahlB,KACnBkE,EAA4B5E,KAArBmJ,EAAMvE,KAAEwE,EAASxE,KACxB+gB,EAA4C9lB,KAArCZ,EAAc0mB,KAAEC,EAAiBD,KAClCE,EjD6DcvlB,KAAZnD,QAEO0oB,kBAAqB,aiD9D9B5e,EAAsBxC,KACtBgB,EAAmBL,KAEzB,GAAIsgB,EACF,OAAO,KAGT,IAEMI,EAAe3c,EAjCL,GA+BiBxT,EAAmBjC,YAEVqyB,OAEpCC,EAAW/D,IAAc8C,GAAwBS,SAEvD,OACEn1B,gBAACm0B,IACClxB,UAAW4Z,KACTC,GAAO8Y,UACPD,GAAY7Y,GAAO6Y,SACnB7c,GAAUgE,GAAOza,KACjBszB,GAAY7c,GAAUgE,GAAO+Y,gBAE/B3yB,MACEyyB,EACI,CAAEG,UAAWL,EAAcx0B,OAAQw0B,GACnC,CAAEK,UAAWL,IAGnBz1B,uBAAKiD,UAAW4Z,KAAGC,GAAOiZ,QAASxY,IAAKtL,GACrC3M,EAAmBtG,KAAI,SAACs1B,EAAmB3K,GAC1C,IAAM8K,EAASH,IAAsB1lB,EAErC,OACE5O,gBAACo0B,IACC9yB,IAAKgzB,EACLA,kBAAmBA,EACnBxb,OAAQA,EACR5V,MAAO,CACLopB,UAAWzP,KACT8Y,iBACmBhM,GAAK7Q,EA/DxB,GA+D6C,wBAC1B6Q,GAAK7Q,EAhExB,GAgE6C,SAC7CA,GAAU2b,GAAU,eAGxBJ,SAAUI,EACVvL,QAAS,WACHpQ,GACFyc,EAAkBjB,GAClBkB,EAAiBlB,GACjBlf,KAEA2D,GAAU,GAEZnC,aAUhB,SAAY8d,GACVA,0BACAA,8BAFF,CAAYA,KAAAA,QAKZ,IAAM5X,GAAS3d,EAAWS,OAAO,CAC/Bg2B,UAAW,CACT91B,IAAK,iBACLuf,KAAM,CACJ2W,uBAAwB,QAE1B12B,QAAS,OACTssB,WAAY,SACZC,eAAgB,WAChB9qB,WAAY,uBACZ0sB,QAAS,UAEXkI,SAAU,CACRlI,QAAS,MACT7B,WAAY,WACZhN,cAAe,SACfG,aAAc,MACdkM,OAAQ,iCAEV4K,eAAgB,CACdlD,UAAW,4CAEbtwB,KAAM,CAEJsgB,eAAgB,YAChBuI,WAAY,yCACZ+K,cAAe,CACbhL,OAAQ,iEAGZ8K,OAAQ,CACNj2B,IAAK,uBACL6e,SAAU,WACVzd,MAAO,4BACPD,OAAQ,wCCxHIi1B,KACd,IAAM1sB,EAAgBgH,KAChBwI,EAAsB9C,KAC5BigB,EAAwB7mB,KAExB,OAAK9F,EAAc+B,YAKjBvL,gBAAC4zB,IACC3wB,UAAW4Z,KACTC,GAAOsZ,QACP/1B,EAAwBS,kBAVVq1B,MAWGrZ,GAAOuZ,kBAG1Br2B,gBAACs2B,SACDt2B,gBAACgc,SACAhD,EAAsBhZ,gBAACi1B,SAAwB,MAb3C,KAkBX,SAAgBqB,WACR9sB,EAAgBgH,KACtBlE,EAAwCC,WAAuB,MAAxDgqB,EAAYjqB,KAAEkqB,EAAelqB,KAC9BzJ,EAAayN,KACZmmB,EAAwB/mB,QACzB5E,EAAc6F,eCxCpB+lB,EACAF,GAEA,IAAMzkB,EAAUa,KACVwgB,EAAoBte,KACpBqe,EAAiBve,KAEvB1F,aAAU,WACR,GAAKwnB,EAAL,CAGA,IAAMta,EAAUrK,EAAQjF,QAoExB,aAlEAsP,GAAAA,EAAS/D,iBAAiB,UAAWse,EAAU,CAC7CjM,SAAS,UAGXtO,GAAAA,EAAS/D,iBAAiB,YAAaue,GAAa,SAEpDxa,GAAAA,EAAS/D,iBAAiB,QAASwe,GAAS,SAE5Cza,GAAAA,EAAS/D,iBAAiB,WAAYye,EAAS,CAC7CpM,SAAS,UAEXtO,GAAAA,EAAS/D,iBAAiB,OAAQye,GAAS,GAuDpC,iBACL1a,GAAAA,EAAS9D,oBAAoB,YAAase,SAC1Cxa,GAAAA,EAAS9D,oBAAoB,WAAYwe,SACzC1a,GAAAA,EAAS9D,oBAAoB,QAASue,GAAS,SAC/Cza,GAAAA,EAAS9D,oBAAoB,OAAQwe,GAAS,SAC9C1a,GAAAA,EAAS9D,oBAAoB,UAAWqe,IA1D1C,SAASE,EAAQE,GACf,IAAMza,EAAS/C,GAAiBwd,EAAEpkB,QAElC,IAAK2J,EACH,OAAOwa,IAGT,IAAAE,EAAqC3R,GAA2B/I,GAAxDjV,EAAO2vB,EAAP3vB,QAASsc,EAAeqT,EAAfrT,gBAEjB,IAAKtc,IAAYsc,EACf,OAAOmT,IAGTN,EAAgB,CACdnvB,QAAAA,EACAsc,gBAAAA,IAGJ,SAASmT,EAAQC,GACf,GAAIA,IAGGxd,GAFiBwd,EAAEE,eAGtB,OAAOT,EAAgB,MAI3BA,EAAgB,MAElB,SAASG,EAASI,GACF,WAAVA,EAAEz1B,KACJk1B,EAAgB,MAIpB,SAASI,EAAYG,GACnB,IAAI3D,IAAJ,CAIA,IAAM9W,EAAS/C,GAAiBwd,EAAEpkB,QAElC,GAAI2J,EAAQ,CAGV,YCtFNA,EACAF,GAEA,IAAKE,IAAWF,EACd,OAAO,EAGT,IAAM8a,EAAa5a,EAAOvB,wBACpBoc,EAAW/a,EAAQrB,wBAGzB,OAAOoc,EAASl2B,QAAUi2B,EAAWE,EAAID,EAASC,GDyEtBC,CAA8B/a,EAAQF,GACvCE,EAAOvB,wBAAwB9Z,OAElD,OAiBV,SACEqb,EACAka,SAEAc,EAAqCjS,GAA2B/I,GAAxDjV,EAAOiwB,EAAPjwB,QAASsc,EAAe2T,EAAf3T,gBAEZtc,GAAYsc,WAIhB4T,EAAA7lB,SAASC,sBAAT4lB,EAAwCC,MAAxCD,EAAwCC,OAEzChB,EAAgB,CACdnvB,QAAAA,EACAsc,gBAAAA,KA/Ba8T,CAAmCnb,EAAQka,GAGpDzlB,GAAauL,QAWhB,CAACvK,EAAS2kB,EAAOF,EAAiBpD,EAAmBD,ID5CxDuE,CAAsBluB,EAAc+B,YAAairB,GAEjD,IAAMhwB,EAAQmC,SAAcgvB,QAC1BpB,SAAAA,EAAclvB,SAAOswB,QAAIpB,SAAAA,EAAc5S,iBAGnCiU,EAAgB,MAATpxB,GAAiC,MAAhB+vB,EAE9B,OAAOv2B,iBAEP,WACE,IAAMqL,QACJorB,EAAAA,EAAwB9tB,EAAea,EAAc6B,cACvD,IAAKA,EACH,OAAO,KAET,IAAMwsB,EAAcpB,EAChBtvB,EAAUsvB,GACVjtB,EAAc8B,eAElB,OACEtL,gCACEA,2BACG43B,EACC53B,gBAAC+tB,IACC1mB,cAASkvB,SAAAA,EAAclvB,QACvBb,MAAOA,EACP3D,WAAYA,EACZmrB,KAAM,GACNljB,YAAaA,EACb7H,UAAW4Z,KAAGC,GAAOtW,SAErB6E,EACFrL,gBAAC+tB,IACC1mB,QAASJ,EAAaoE,GACtB7E,MAAO6E,EACPxI,WAAYA,EACZmrB,KAAM,GACNljB,YAAaA,EACb7H,UAAW4Z,KAAGC,GAAOtW,SAErB,MAENxG,uBAAKiD,UAAW4Z,KAAGC,GAAOqJ,QACvByR,EAAOzwB,EAAUX,GAASqxB,YAYrC,IAAM/a,GAAS3d,EAAWS,OAAO,CAC/Bw2B,QAAS,CACPxK,WAAY,SACZkM,UAAW,4CACX72B,OAAQ,4BACRwsB,QAAS,kCACT9O,SAAU,WACV6N,OAAQ,8BAEVrG,MAAO,CACLuI,MAAO,gCACP9B,SAAU,+BACVa,QAAS,kCACToC,cAAe,cAEjBrpB,MAAO,CACLinB,QAAS,KAEX4I,gBAAiB,CACf92B,QAAS,IACTwB,WAAY,uCG/GAg3B,GAAc9xB,SAC5B+xB,EAAgB/xB,EAAhB+xB,iBAGAzI,EAActpB,EAAdspB,eACArG,EAAOjjB,EAAPijB,QAEA,OACElpB,gBAAC6qB,IACC2D,SANWvoB,EAAfgyB,gBAMgC,GAAK,EACjCh1B,UAAW4Z,KACTC,GAAOob,OACP73B,EAAwBG,uBAVtByF,EAARvB,UAWyB0Y,KAAAA,EAEhB3e,EAAWg2B,QAASuD,EAAgB5a,IAGzC8L,QAASA,eACGlkB,EAA+BuqB,mBAC5ByI,EACfG,KAAK,sBACS,wBAKpB,IAAMC,GAAsB,CAC1B13B,oBAAqB,wDAMjB23B,GAAuB,CAC3BC,2BAA4B,CAC1BJ,OAAQ,CACNz3B,SAAU23B,GACVh3B,eAAgBg3B,MAKhBtb,GAAS3d,EAAWS,OAAMC,GAC9Bq4B,OAAQ,CACNp4B,IAAK,cACLR,QAAS,eACTyB,WAAY,2BACZ4d,SAAU,WACV1d,OAAQ,6CACRC,MAAO,6CACP2tB,eAAgB,wDAChB1D,QAAS,OACT2H,mBAAoB,MACpBnE,0qzCACA4J,gBAAiB,CACfrM,QAAS,GACTvN,SAAU,WACVzL,IAAK,OACL8P,KAAM,OACNmJ,MAAO,OACPrG,OAAQ,OACRmF,OAAQ,kDACRlM,aAAc,OAEhByZ,sBAAuB,CACrBC,oBACE,yDAEJC,mBAAoB,CAClBD,oBACE,yDAEJE,uBAAwB,CACtBF,oBACE,yDAEJG,2BAA4B,CAC1BH,oBACE,yDAEJI,kBAAmB,CACjBJ,oBACE,yDAEJK,uBAAwB,CACtBL,oBACE,yDAEJM,oBAAqB,CACnBN,oBACE,yDAEJO,2BAA4B,CAC1BP,oBAAqB,OAEvBQ,oBAAqB,CACnBR,oBACE,yDAEJS,0BAA2B,CACzBT,oBACE,2DAGHp3B,EAAS,SA3EQ,CACpBX,oBAAqB,0DA2ErBc,kBAAiB3B,KACZw4B,IAEL32B,kBAAiB7B,KACZw4B,gBCvHSc,KACd,ICTMpnB,EACAF,EDQNvF,EAA4CC,WAAwB,MAA7D6sB,EAAc9sB,KAAE+sB,EAAiB/sB,KAClCgtB,GCVAvnB,EAAUa,KACVf,EAAgBW,KAEf,SAAgC9N,SACrC,GAAKqN,EAAQjF,QAAb,CAGA,IAAMysB,SAASC,EAAGznB,EAAQjF,gBAAR0sB,EAAiBpV,6BAClB1f,QAGZ60B,GAMLvmB,GAASnB,EAAc/E,QAFLysB,EAAU3U,WAAa,gBChB3CyU,GAEA,IAAMtnB,EAAUa,KAEhB1D,aAAU,WACR,IAAMuqB,EAAoB,IAAIC,IACxBtd,EAAUrK,EAAQjF,QAClB6sB,EAAW,IAAIC,sBACnB,SAAA9zB,GACE,GAAKsW,EAAL,CAIA,QAA2Byd,EAA3BC,qrBAAAC,CAAoBj0B,KAAO+zB,EAAAC,KAAAE,MAAE,CAAA,IAAlBC,EAAKJ,EAAAt4B,MACRiJ,SCpBd0vB,SADkCX,EDqBKU,EAAMtnB,eCpBtC4mB,EAAWY,aAAa,cAAYD,EAAI,KDqBvCT,EAAkBW,IAAI5vB,EAAIyvB,EAAMI,uBCtBNd,IDyBtBe,EAAS17B,MAAM6mB,KAAKgU,GACpBc,EAAeD,EAAOA,EAAOj3B,OAAS,GAE5C,GAAuB,GAAnBk3B,EAAa,GACf,OAAOlB,EAAkBkB,EAAa,IAGxC,QAAAtU,IAAAuU,EAA0BF,EAAMrU,EAAAuU,EAAAn3B,OAAA4iB,IAAE,CAA7B,IAAAwU,EAAAD,EAAAvU,GACH,GADmBwU,KACR,CACTpB,EAFUoB,MAGV,WAIN,CACEC,UAAW,CAAC,EAAG,WAGnBte,GAAAA,EAASsJ,iBAAiBhnB,EAAYD,EAAWiG,WAAWoC,SAAQ,SAAA6zB,GAClEhB,EAASiB,QAAQD,QAElB,CAAC5oB,EAASsnB,IF3BbwB,CAAiCxB,GACjC,IIjBMyB,EJiBAzkB,EAAevF,KAEfiqB,EAAmBxqB,KACnB2B,EAAwBa,KACxBioB,KIrBAF,E1DmEmB5uB,KAAjBhK,e0D7D+B,IAAhC44B,EAAqBz3B,OJiB5B,OACErD,uBACEiD,UAAW4Z,KAAGC,GAAOme,KACrB9C,KAAK,uBACM,sBACX3tB,GAAG,sBACH+S,IAAKrL,GAEJ6oB,EAAiB/7B,KAAI,SAAAuwB,GACpB,IAAM7qB,EAAWK,EAA2BwqB,GACtCyI,EAAmBtzB,IAAa00B,EAEtC,gB7B7BN10B,GAEA,OAAOA,EAASA,WAAahB,mBAAWK,O6B2B9Bm3B,CAAiB3L,IAAmByL,EAC/B,KAMPh7B,gBAAC+3B,IACCz2B,IAAKoD,EACLA,SAAUA,EACVszB,iBAAkBA,EAClBC,iBAPqB5hB,IAAiB2hB,EAQtCzI,eAAgBA,EAChBrG,QAAS,WACPoQ,EAAuB50B,GACvBoE,YAAW,WACTuwB,EAAkB30B,KACjB,WASjB,IAAMoY,GAAS3d,EAAWS,OAAO,CAC/Bq7B,IAAK,CACHn7B,IAAK,mBACLR,QAAS,OACTsf,cAAe,MACfiN,eAAgB,eAChB4B,QAAS,6BAEX0N,qBAAsB,CACpBF,IAAK,CACH17B,QAAS,MACTyrB,OAAQ,UACRxrB,cAAe,SAGnB47B,+CAAgD,CAC9CH,IAAK,CACH17B,QAAS,MACTyrB,OAAQ,UACRxrB,cAAe,onIKtEL67B,KACd,IAAMpjB,EAAchD,KAEpB,OACEjV,gBAAC6qB,IACC5nB,UAAW4Z,KACTC,GAAOwe,eACPj7B,EAAwBQ,qBAE1BqoB,QAASjR,eACE,QACXsW,MAAM,SAENvuB,uBAAKiD,UAAW4Z,KAAGC,GAAOye,oBAKhC,IAAMC,GAAY,CAChB/6B,SAAU,CACRg7B,0BAA2B,CACzB/6B,oBAAqB,WAKrBoc,GAAS3d,EAAWS,OAAMC,GAC9By7B,eAAgB,CACdx7B,IAAK,uBACL6e,SAAU,WACVwN,MAAO,sCACPlrB,OAAQ,OACRC,MAAO,OACP5B,QAAS,OACTssB,WAAY,SACZC,eAAgB,SAChB3Y,IAAK,MACLoZ,UAAW,mBACXmB,QAAS,IACT1O,aAAc,MACdte,SAAU,CACRyqB,WAAY,6BAEdc,SAAU,CACRd,WAAY,8BAGhBqQ,gBAAiB,CACfz7B,IAAK,uBACLmf,gBAAiB,cACjB2P,iBAAkB,YAClBC,eAAgB,OAChB5tB,OAAQ,OACRC,MAAO,OACPytB,uBAAwB+M,OACxBj7B,SAAU,CACRC,oBAAqB,SAEvBsrB,SAAU,CACRtrB,oBAAqB,WAGtBW,EAAS,kBAAmB,CAC7BX,oBAAqB,UAEpBW,EAAS,iBAAkBm6B,MCzE1BG,GAAWj9B,EAAYD,EAAWigB,iBAAgBhgB,EACtDD,EAAWkyB,WAGPiL,GAAe,CAAC,SAAUl9B,EAAYD,EAAW+H,QAAQtH,KAAK,IAC9D28B,GAAWn9B,EAAYD,EAAWiG,mBAExBo3B,GAAS71B,OAAG1E,EAAK0E,EAAL1E,MAC1B,IAAKA,EACH,OAAO,KAGT,IAAMw6B,EAoBR,SAAkBx6B,GAChB,MAAO,CACLq6B,GACA,qBACAlmB,GAAwBnU,GACxB,MACArC,KAAK,IA1BG88B,CAASz6B,GAEnB,OACEvB,sCACE27B,OAASC,+CAKTD,OAASI,4CAITJ,OAASE,gBAAqBE,oDCvBpBE,KACd,OAAOj8B,uBAAKiD,UAAW4Z,KAAGC,GAAOof,aAGnC,IAAMpf,GAAS3d,EAAWS,OAAMC,GAC9Bq8B,UAAW,CACTp8B,IAAK,iBACLosB,QAAS,GACTvN,SAAU,WACVzL,IAAK,MACL8P,KAAM,sCACNsJ,UAAW,mBACXprB,MAAO,OACPD,OAAQ,OACR2tB,iBAAkB,YAClBkE,mBAAoB,MACpBjE,eAAgB,OAChBF,woDAECttB,EAAS,YAAa,CACvBX,oBAAqB,qBCJTy7B,KACd,IAAMh5B,EAAiBsN,KAEjBsH,EAAqB9B,KAE3B,OAAI9S,EACK,KAIPnD,gBAAC4zB,IAAK3wB,UAAW4Z,KAAGC,GAAOsf,UACzBp8B,gBAACq8B,SAEAtkB,EAAqB/X,gBAACk1B,SAAoB,MAKjD,SAAgBmH,KACd,M9DXAC,E8DWAhwB,EAAsBC,WAAS,GAAxBgwB,EAAGjwB,KAAEkwB,EAAMlwB,KACZsK,EAAsBxC,KACtBpC,EAAiBa,KACjB4pB,S9DbNC,EACE,EAFFJ,EAAiDpwB,MAAzC1J,kBAAoC85B,EAAjB75B,mBAEcmF,MACrC,SAAA+0B,GAAC,MJRmC,WIQ/BA,MACND,EJTqC,SkEoBlCE,E9DcsB1wB,KAApBtJ,gB8DbRi6B,E9CYF,WACE,IAAM7qB,EAAiBa,KACjB1E,EAAYiB,KACZ0tB,aA7CN,IAAM3uB,EAAYiB,KAElB,OAAO,SAAS2tB,EACdC,GAEA,GAAsB,mBAAXA,EACT,OAAOD,EAAUC,EAAO7uB,EAAUrB,UAGpCqB,EAAUrB,QAAUkwB,GAoCDC,GACf/nB,EAAcC,KAEbzG,EAAca,QAMrB,MAAO,CACL2tB,SAMF,SAAkBC,GAChB,IAAMzT,EAASvb,EAAUrB,QAEnBswB,EAAYD,EAAWx2B,cAE7B,SAAI+iB,GAAAA,EAAS0T,IAAcA,EAAU/5B,QAAU,EAC7C,OAAO6R,EAAYkoB,GAGrB,IAAMC,EA0EV,SACE7nB,EACA8nB,GAEA,IAAKA,EACH,OAAO,KAGT,GAAIA,EAAK9nB,GACP,OAAO8nB,EAAK9nB,GAGd,IAAM+nB,EAAqBp4B,OAAOq4B,KAAKF,GACpC9V,MAAK,SAACC,EAAGC,GAAC,OAAKA,EAAErkB,OAASokB,EAAEpkB,UAC5BuE,MAAK,SAAAtG,GAAG,OAAIkU,EAAQ1N,SAASxG,MAEhC,OAAIi8B,EACKD,EAAKC,GAGP,KA9FgBE,CAAiBL,EAAW1T,GAEjD,IAAK2T,EAGH,OAAOnoB,EAAYkoB,GAGrBN,GAAa,SAAAhwB,GAAO,IAAAzG,EAAA,OAClBlB,OAAOC,OAAO0H,IAAOzG,MAClB+2B,GAsBT,SACEl1B,EACAsN,GAEA,IAAMkoB,EAAuB,GAE7B,IAAK,IAAMr2B,KAAWa,EAAQ,CAC5B,IAAM1B,EAAQ0B,EAAOb,GAEjBkO,GAAS/O,EAAOgP,KAClBkoB,EAASr2B,GAAWb,GAIxB,OAAOk3B,EApCYC,CAA2BN,EAAcD,GAAU/2B,OAGpE6O,EAAYkoB,IA3BZ1uB,WAAAA,EACAsD,eAAAA,EACA4rB,oBAqHJ,SACEC,EACAnvB,ShBPqCovB,EgBSrC,aAAKD,GAAAA,EAAcnvB,IhBTkBovB,UgBYnCC,EAAA54B,OAAOW,cAAQ+3B,SAAAA,EAAcnvB,YAA7BqvB,EAA2C16B,SAAU,GhBXf,EACvBy6B,EAAqB,EJ5JtC,4DIgK4Cl3B,QACpC,KACAk3B,EAAmBE,YJpK3B,0DAJ6C,mBoByKN,GAlIXC,CAC1B9vB,EAAUrB,QACV4B,I8CrBoDwvB,GAA9CN,EAAmBf,EAAnBe,oBAAqBlvB,EAAUmuB,EAAVnuB,WAAYwuB,EAAQL,EAARK,SAEnCiB,QAAQnsB,SAAAA,EAAgBlF,QACxBvL,QAAQ48B,SAAAA,EAAO58B,MAErB,OACEvB,gBAACm0B,IAASlxB,UAAW4Z,KAAGC,GAAOshB,kBAC7Bp+B,gBAAC87B,IAAUv6B,MAAOA,IAClBvB,yBAEE48B,UAAWA,eACC,8BACZvgB,QAASzF,EACT3T,UAAW4Z,KAAGC,GAAOuhB,QACrBtT,KAAK,uBACS,gBACd0R,YAAaA,EACbS,SAAU,SAAArmB,GACR2lB,EAAOD,EAAM,GACbzzB,YAAW,mBACTo0B,SAAQoB,QAACznB,UAAK0nB,EAAL1nB,EAAOlE,eAAP4rB,EAAeh9B,OAAK+8B,EAAI/8B,OAGrCgc,IAAKvL,IAENtD,EACC1O,uBACEm4B,KAAK,SACLl1B,UAAW4Z,KAAG,4BAA6BC,GAAO0hB,4BACxC,SACVh0B,GAAG,8BACS,QAEXozB,GAED,KACJ59B,gBAACi8B,SACDj8B,gBAACq7B,UAKP,IAAMve,GAAS3d,EAAWS,OAAMC,GAC9Bu8B,QAAS,CACP3O,QAAS,4BACTjB,OAAQ,qCAEV4R,gBAAiB,CACft+B,IAAK,uBACL2zB,KAAM,IACNn0B,QAAS,QACTiuB,SAAU,KAEZiR,eAAgB,CACdC,KAAM,gBACNC,SAAU,aACVz9B,OAAQ,MACRvB,SAAU,SACVif,SAAU,WACVggB,WAAY,SACZz9B,MAAO,OAETm9B,OAAQ,CACNlT,QAAS,OACTpqB,WAAY,uBACZ2tB,MAAO,qCACP3P,aAAc,wCACd0O,QAAS,kCACTxsB,OAAQ,iCACRge,gBAAiB,mCACjBgM,OAAQ,6CACR/pB,MAAO,OACP8qB,SAAU,CACR/M,gBAAiB,0CACjBgM,OAAQ,4CAEV2T,gBAAiB,CACflQ,MAAO,8CAIX4M,eAAgB,CACdx7B,IAAK,uBACL6e,SAAU,WACVwN,MAAO,sCACPlrB,OAAQ,OACRC,MAAO,OACP5B,QAAS,OACTssB,WAAY,SACZC,eAAgB,SAChB3Y,IAAK,MACLoZ,UAAW,mBACXmB,QAAS,IACT1O,aAAc,MACdte,SAAU,CACRyqB,WAAY,6BAEdc,SAAU,CACRd,WAAY,8BAGhBqQ,gBAAiB,CACfz7B,IAAK,uBACLmf,gBAAiB,cACjB2P,iBAAkB,YAClBC,eAAgB,OAChB5tB,OAAQ,OACRC,MAAO,OACPytB,uBAAwB+M,OACxBj7B,SAAU,CACRC,oBAAqB,SAEvBsrB,SAAU,CACRtrB,oBAAqB,WAGtBW,EAAS,kBAAmB,CAC7BX,oBAAqB,UAEpBW,EAAS,iBAAkB,CAC5Bw9B,iCAAkC,CAChCn+B,oBAAqB,sBC/JXo+B,KACd,OACE9+B,gBAACm0B,IACClxB,UAAW4Z,KAAG,aAAcxc,EAAwBS,oBAEpDd,gBAACm8B,SACDn8B,gBAACm5B,UCCP,SAAS4F,GAAYjU,GACnB,OACE9qB,gBAAC4R,QACC5R,gBAACD,QACDC,gBAACyL,oBAAyBqf,GACxB9qB,gBAACg/B,WAMT,SAASA,KACP,IAAOz8B,EAAwB+M,QACzBlE,EAAuBgF,KAE7BxE,EAAkC5L,YAAgBuC,GAA3C08B,EAASrzB,KAAEszB,EAAYtzB,KACxBkN,EhE2CW5M,KAAT7J,KgE/BR,OAVArC,aAAgB,WACVuC,IAAyB6I,GAIxB6zB,GACHC,GAAa,KAEd,CAACD,EAAW7zB,EAAsB7I,IAEhCuW,EAKH9Y,gBAACqX,QACCrX,gBAAC8uB,SACD9uB,gBAACm/B,IAAsBF,UAAWA,KAN7B,KAWX,SAASE,GAAqBl5B,GAC5B,OADwCA,EAATg5B,UAM7Bj/B,gCACEA,gBAAC8+B,SACD9+B,gBAAC+yB,SACD/yB,gBAACk2B,UAPI,KAaX,OAAel2B,OAAW++B,GAAal9B,GCrElBu9B,YAAcC,WAIjC,SAAAD,EAAYtU,SAEuB,OADjCwU,EAAAD,EAAAE,UAAMzU,UACDte,MAAQ,CAAEgzB,UAAU,GAAQF,IANFD,KAAAD,yEAOhCA,EAEMK,yBAAP,WACE,MAAO,CAAED,UAAU,IACpB,IAAAE,EAAAN,EAAAO,UAaA,OAbAD,EAEDE,kBAAA,SAAkBC,EAAcC,GAE9BC,QAAQF,MAAM,uCAAwCA,EAAOC,IAC9DJ,EAEDM,OAAA,WACE,OAAIC,KAAKzzB,MAAMgzB,SACN,KAGFS,KAAKnV,MAAMpf,UACnB0zB,GAxBwCp/B,oCCKdiG,OAC3BoB,EAAOpB,EAAPoB,QAAO64B,EAAAj6B,EACP+nB,KAASmS,EAAAl6B,EACTpD,WAAAA,WAAUs9B,EAAG58B,mBAAWgF,MAAK43B,EAAAlT,EAAAhnB,EAC7BinB,SACApiB,EAAW7E,EAAX6E,YACAs1B,EAAQn6B,EAARm6B,SASA,OAAK/4B,GAAY+4B,GAAat1B,EAK5B9K,gBAAC+tB,IACC1mB,QAASA,EACT2mB,cApBAkS,EAAG,GAAEA,EAqBLr9B,WAAYA,EACZqqB,kBApBID,GAAQA,EAqBZniB,YAAas1B,EAAW,WAAA,OAAMA,GAAWt1B,IATpC,+BCCyBggB,GAClC,IAAMuV,W/DLN32B,GAEA,IAAM22B,EAAmBrgC,EAAM2M,OAAsB,CACnDwb,aAAcze,EAAOye,cAAgBhY,GACrCmY,gBAAiB5e,EAAO4e,iBAAmB5e,EAAOye,aAClDqN,iBAAkB9rB,EAAO8rB,kBAAoBrlB,KAc/C,OAXAnQ,EAAMkP,WAAU,WACdmxB,EAAiBvzB,QAAQqb,aAAeze,EAAOye,cAAgBhY,GAC/DkwB,EAAiBvzB,QAAQwb,gBACvB5e,EAAO4e,iBAAmB5e,EAAOye,eAClC,CAACze,EAAOye,aAAcze,EAAO4e,kBAEhCtoB,EAAMkP,WAAU,WACdmxB,EAAiBvzB,QAAQ0oB,iBACvB9rB,EAAO8rB,kBAAoBrlB,KAC5B,CAACzG,EAAO8rB,mBAEJ6K,E+DdkBC,CAAuB,CAC9CnY,aAAc2C,EAAM3C,aACpBG,gBAAiBwC,EAAMxC,gBACvBkN,iBAAkB1K,EAAM0K,mBAG1B,OACEx1B,gBAACo/B,QACCp/B,gBAAC+P,GAAqB9D,UAAS1K,MAAO8+B,GACpCrgC,gBAACugC,oBAAqBzV"}